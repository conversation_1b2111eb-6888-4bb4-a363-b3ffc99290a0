# PFP图片批量上传接口使用说明

## 概述

本次改造将原来需要手动上传图片到服务器的流程改为通过API接口自动化处理，大大提升了运营效率。

## 改造内容

### 1. 原有问题
- 需要手动将图片文件放到服务器指定目录
- 图片需要按照特定规则命名
- 每次都需要运营人员手动操作
- 硬编码的本地路径：`/Users/<USER>/project/clkj-project/meta/PFP 期数/PFP二期素材/Mutant Bear (" + number + ").png"`

### 2. 改造方案
- 提供批量上传文件接口
- 提供批量从URL上传接口
- 自动生成文件名和排序号
- 自动上传到OSS存储
- 自动保存到数据库

## API接口说明

### 1. 批量上传文件接口

**接口地址**: `POST /v1/pfp/image/batch_upload_files`

**请求方式**: `multipart/form-data`

**请求参数**:
- `periodId` (必填): 期数ID
- `imageFiles` (必填): 图片文件列表
- `namingRule` (可选): 命名规则，AUTO-自动编号，FILENAME-使用文件名，默认AUTO
- `startNumber` (可选): 起始编号，默认1
- `imagePrefix` (可选): 图片前缀，默认pfp

**使用示例**:
```bash
curl -X POST "http://localhost:8080/core/v1/pfp/image/batch_upload_files" \
  -H "Authorization: Bearer your-token" \
  -F "periodId=123" \
  -F "imageFiles=@image1.png" \
  -F "imageFiles=@image2.png" \
  -F "imageFiles=@image3.png" \
  -F "namingRule=AUTO" \
  -F "startNumber=1" \
  -F "imagePrefix=pfp"
```

### 2. 从ZIP文件批量上传接口

**接口地址**: `POST /v1/pfp/image/batch_upload_zip`

**请求方式**: `multipart/form-data`

**请求参数**:
- `periodId` (必填): 期数ID
- `zipFile` (必填): ZIP压缩文件
- `namingRule` (可选): 命名规则，AUTO-自动编号，FILENAME-使用文件名，默认AUTO
- `startNumber` (可选): 起始编号，默认1
- `imagePrefix` (可选): 图片前缀，默认pfp
- `keepDirectoryStructure` (可选): 是否保持ZIP内的目录结构，默认false
- `supportedFormats` (可选): 支持的图片格式，默认jpg,jpeg,png,gif,bmp,webp

**使用示例**:
```bash
curl -X POST "http://localhost:8080/core/v1/pfp/image/batch_upload_zip" \
  -H "Authorization: Bearer your-token" \
  -F "periodId=123" \
  -F "zipFile=@images.zip" \
  -F "namingRule=AUTO" \
  -F "startNumber=1" \
  -F "imagePrefix=pfp" \
  -F "keepDirectoryStructure=false" \
  -F "supportedFormats=jpg,jpeg,png,gif,bmp,webp"
```

**ZIP文件要求**:
- 支持标准ZIP格式
- 单个图片文件不超过10MB
- ZIP文件内最多1000个文件
- 支持嵌套目录结构
- 自动过滤非图片文件

### 3. OSS直传ZIP文件处理接口（推荐大文件）

**接口地址**: `POST /v1/pfp/image/batch_upload_oss_zip`

**请求方式**: `application/json`

**请求参数**:
```json
{
  "periodId": 123,
  "zipFileUrl": "https://your-oss-bucket.oss-cn-hangzhou.aliyuncs.com/temp/zip/1234567890_images.zip",
  "namingRule": "AUTO",
  "startNumber": 1,
  "imagePrefix": "pfp",
  "keepDirectoryStructure": false,
  "supportedFormats": "jpg,jpeg,png,gif,bmp,webp",
  "deleteAfterProcess": true
}
```

**使用流程**:
1. 调用获取OSS临时Token接口
2. 使用临时Token直接上传ZIP文件到OSS
3. 调用此接口处理OSS上的ZIP文件

**优势**:
- 支持大文件（推荐500MB以内）
- 不占用后端带宽
- 上传速度更快
- 支持断点续传

### 4. 获取OSS上传临时Token接口

**接口地址**: `POST /v1/pfp/image/get_oss_token`

**请求参数**:
- `prefix` (可选): 上传路径前缀，默认temp/zip
- `expireMinutes` (可选): 过期时间（分钟），默认60

**响应示例**:
```json
{
  "code": "000000",
  "data": {
    "accessKeyId": "STS.xxx",
    "accessKeySecret": "xxx",
    "securityToken": "xxx",
    "bucket": "your-bucket",
    "endpoint": "https://oss-cn-hangzhou.aliyuncs.com",
    "prefix": "temp/zip",
    "expiration": 3600,
    "baseUrl": "https://your-bucket.oss-cn-hangzhou.aliyuncs.com"
  },
  "message": "操作成功"
}
```

### 5. 批量从URL上传接口

**接口地址**: `POST /v1/pfp/image/batch_upload_urls`

**请求方式**: `application/json`

**请求参数**:
```json
{
  "periodId": 123,
  "imageUrls": [
    "https://example.com/image1.png",
    "https://example.com/image2.png",
    "https://example.com/image3.png"
  ],
  "namingRule": "AUTO",
  "startNumber": 1,
  "imagePrefix": "pfp"
}
```

**使用示例**:
```bash
curl -X POST "http://localhost:8080/core/v1/pfp/image/batch_upload_urls" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "periodId": 123,
    "imageUrls": [
      "https://example.com/image1.png",
      "https://example.com/image2.png"
    ],
    "namingRule": "AUTO",
    "startNumber": 1,
    "imagePrefix": "pfp"
  }'
```

### 3. 响应格式

```json
{
  "code": "000000",
  "data": {
    "successCount": 2,
    "failCount": 1,
    "successList": [
      {
        "orderNumber": 1,
        "originalName": "image1.png",
        "ossUrl": "https://oss.example.com/pfp/uuid_1.png",
        "ipfsUrl": "https://oss.example.com/pfp/uuid_1.png"
      },
      {
        "orderNumber": 2,
        "originalName": "image2.png",
        "ossUrl": "https://oss.example.com/pfp/uuid_2.png",
        "ipfsUrl": "https://oss.example.com/pfp/uuid_2.png"
      }
    ],
    "failList": [
      {
        "originalName": "image3.png",
        "errorMessage": "文件上传失败"
      }
    ]
  },
  "message": "操作成功"
}
```

## 使用流程

### 1. 准备工作
1. 确保有有效的用户token
2. 准备要上传的图片文件或图片URL列表
3. 确定期数ID

### 2. 上传方式选择

#### 方式一：本地文件上传
适用于图片文件在本地的情况
```javascript
// 前端JavaScript示例
const formData = new FormData();
formData.append('periodId', '123');
formData.append('namingRule', 'AUTO');
formData.append('startNumber', '1');

// 添加多个文件
for (let i = 0; i < files.length; i++) {
    formData.append('imageFiles', files[i]);
}

fetch('/v1/pfp/image/batch_upload_files', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token
    },
    body: formData
}).then(response => response.json())
  .then(data => console.log(data));
```

#### 方式二：ZIP文件上传
适用于有大量图片需要批量上传的情况
```javascript
// 前端JavaScript示例
const formData = new FormData();
formData.append('periodId', '123');
formData.append('zipFile', zipFileInput.files[0]); // ZIP文件
formData.append('namingRule', 'AUTO');
formData.append('startNumber', '1');
formData.append('imagePrefix', 'pfp');
formData.append('keepDirectoryStructure', 'false');
formData.append('supportedFormats', 'jpg,jpeg,png,gif,bmp,webp');

fetch('/v1/pfp/image/batch_upload_zip', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token
    },
    body: formData
}).then(response => response.json())
  .then(data => {
      console.log(`成功上传: ${data.data.successCount} 个文件`);
      console.log(`失败: ${data.data.failCount} 个文件`);
  });
```

#### 方式三：OSS直传ZIP文件（推荐大文件）
适用于大ZIP文件的情况，避免通过后端传输
```javascript
// 完整的OSS直传流程示例
async function ossDirectUpload() {
    // 1. 获取OSS临时Token
    const tokenResponse = await fetch('/v1/pfp/image/get_oss_token?prefix=temp/zip&expireMinutes=60', {
        method: 'POST',
        headers: { 'Authorization': 'Bearer ' + token }
    });
    const tokenData = await tokenResponse.json();
    const ossConfig = tokenData.data;

    // 2. 初始化OSS客户端
    const ossClient = new OSS({
        region: ossConfig.endpoint.replace('https://', '').replace('.aliyuncs.com', ''),
        accessKeyId: ossConfig.accessKeyId,
        accessKeySecret: ossConfig.accessKeySecret,
        stsToken: ossConfig.securityToken,
        bucket: ossConfig.bucket
    });

    // 3. 上传ZIP文件到OSS
    const file = document.getElementById('zipFile').files[0];
    const objectName = `${ossConfig.prefix}/${Date.now()}_${file.name}`;

    const result = await ossClient.put(objectName, file, {
        progress: function (p) {
            console.log('上传进度:', Math.round(p * 100) + '%');
        }
    });

    const ossFileUrl = `${ossConfig.baseUrl}/${objectName}`;

    // 4. 通知后端处理OSS上的ZIP文件
    const processResponse = await fetch('/v1/pfp/image/batch_upload_oss_zip', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + token
        },
        body: JSON.stringify({
            periodId: 123,
            zipFileUrl: ossFileUrl,
            namingRule: 'AUTO',
            startNumber: 1,
            imagePrefix: 'pfp',
            keepDirectoryStructure: false,
            supportedFormats: 'jpg,jpeg,png,gif,bmp,webp',
            deleteAfterProcess: true
        })
    });

    const processData = await processResponse.json();
    console.log('处理结果:', processData);
}
```

#### 方式四：URL批量上传
适用于图片已经在网络上的情况
```javascript
// 前端JavaScript示例
const requestData = {
    periodId: 123,
    imageUrls: [
        'https://example.com/image1.png',
        'https://example.com/image2.png'
    ],
    namingRule: 'AUTO',
    startNumber: 1,
    imagePrefix: 'pfp'
};

fetch('/v1/pfp/image/batch_upload_urls', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
    },
    body: JSON.stringify(requestData)
}).then(response => response.json())
  .then(data => console.log(data));
```

## 技术实现

### 1. 文件存储
- 使用阿里云OSS存储
- 自动生成UUID文件名避免冲突
- 支持多种图片格式（jpg, jpeg, png, gif, bmp, webp）

### 2. 数据库存储
- 自动保存到`collection_pfp_pic`表
- 包含排序号、OSS URL、IPFS URL等信息

### 3. 错误处理
- 单个文件上传失败不影响其他文件
- 详细的错误信息反馈
- 支持部分成功的场景

## 注意事项

1. **文件大小限制**:
   - 单个图片文件不超过10MB
   - ZIP文件建议不超过100MB
2. **数量限制**:
   - 单次上传文件数量不超过100个（直接上传）
   - ZIP文件内最多1000个图片文件
3. **格式支持**:
   - 图片格式：jpg, jpeg, png, gif, bmp, webp
   - 压缩格式：标准ZIP格式
4. **权限验证**: 需要有效的用户token
5. **期数验证**: 确保期数ID存在且有权限操作
6. **ZIP文件要求**:
   - 必须是有效的ZIP格式
   - 支持嵌套目录结构
   - 自动过滤非图片文件
   - 文件名支持中文（UTF-8编码）

## 迁移指南

### 从旧方式迁移到新方式

1. **停用旧方法**: 原有的`create(Long collectionId, Integer number, Integer orderNumber)`方法已标记为废弃
2. **使用新接口**: 改用批量上传接口
3. **更新脚本**: 如果有自动化脚本，需要更新为调用新的API接口

### 示例迁移脚本

```python
# Python示例：批量上传本地图片
import requests
import os

def upload_pfp_images(period_id, image_folder, token):
    url = "http://localhost:8080/core/v1/pfp/image/batch_upload_files"
    
    files = []
    for filename in os.listdir(image_folder):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
            file_path = os.path.join(image_folder, filename)
            files.append(('imageFiles', open(file_path, 'rb')))
    
    data = {
        'periodId': period_id,
        'namingRule': 'AUTO',
        'startNumber': 1,
        'imagePrefix': 'pfp'
    }
    
    headers = {
        'Authorization': f'Bearer {token}'
    }
    
    response = requests.post(url, files=files, data=data, headers=headers)
    
    # 关闭文件
    for _, file in files:
        file.close()
    
    return response.json()

# 使用示例
result = upload_pfp_images(123, '/path/to/images', 'your-token')
print(f"成功上传: {result['data']['successCount']} 个文件")
print(f"失败: {result['data']['failCount']} 个文件")
```

## 优势总结

1. **自动化**: 无需手动操作服务器文件
2. **批量处理**: 支持一次上传多个文件
3. **多种方式**: 支持文件上传、URL上传、ZIP文件上传三种方式
4. **智能处理**:
   - 自动解压ZIP文件
   - 自动过滤非图片文件
   - 智能文件命名
5. **可靠性**: 完善的错误处理和状态反馈
6. **灵活性**:
   - 支持保持目录结构
   - 支持自定义文件格式
   - 支持自定义命名规则
7. **可扩展**: 易于扩展支持更多存储方式
8. **标准化**: 统一的API接口规范

通过这次改造，PFP图片上传流程变得更加高效和自动化，大大减少了运营工作量。
