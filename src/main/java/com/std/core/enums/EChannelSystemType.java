package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;

import java.util.HashMap;
import java.util.Map;

/**
* 渠道系统Enum
*
* <AUTHOR> ycj
* @since : 2022-03-20 14:17
*/
public enum EChannelSystemType {

// 数据字典执行SQL
// INSERT INTO "public"."tsys_dict"(key, value, remark) VALUES ('channel_system.status', '{"0":"关闭","1":"开启"}', '渠道系统状态');

    /**
    * 关闭
    */
    E_CHANNEL_SYSTEM_TYPE_0("0", "转出"),

    /**
    * 开启
    */
    E_CHANNEL_SYSTEM_TYPE_1("1", "导出"),

    ;

    private String code;
    private String value;

    EChannelSystemType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EChannelSystemType> getChannelSystemTypeResultMap() {
        Map<String, EChannelSystemType> map = new HashMap<String, EChannelSystemType>();
        for (EChannelSystemType type : EChannelSystemType.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EChannelSystemType getChannelSystemType(String code) {
        Map<String, EChannelSystemType> map = getChannelSystemTypeResultMap();
        EChannelSystemType result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EChannelSystemType=" + code);
        }

        return result;
    }

}
