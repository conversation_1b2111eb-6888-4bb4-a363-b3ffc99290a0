package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
 * 藏品涉及类型
 *
 * <AUTHOR> xieyj
 * @since : 2021-07-08 09:42
 */
public enum ECollectionDetailSellType {


    /**
     * 一口价
     */
    COLLECTION_DETAIL_RECORD_TRADE_TYPE_0("0", "一口价"),

    /**
     * 竞价
     */
    COLLECTION_DETAIL_RECORD_TRADE_TYPE_1("1", "竞价"),

    /**
     * 导出
     */
    COLLECTION_DETAIL_RECORD_TRADE_TYPE_2("2", "导出"),

    /**
     * 转赠中
     */
    COLLECTION_DETAIL_RECORD_TRADE_TYPE_3("3", "转赠"),

    /**
     * XMeta寄售
     */
    COLLECTION_DETAIL_RECORD_TRADE_TYPE_4("4", "XMeta寄售"),
    ;

    private String code;
    private String value;

    ECollectionDetailSellType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, ECollectionDetailSellType> getCollectionSellTypeResultMap() {
        Map<String, ECollectionDetailSellType> map = new HashMap<String, ECollectionDetailSellType>();
        for (ECollectionDetailSellType type : ECollectionDetailSellType.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static ECollectionDetailSellType getCollectionDetailSellType(String code) {
        Map<String, ECollectionDetailSellType> map = getCollectionSellTypeResultMap();
        ECollectionDetailSellType result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "ECollectionDetailSellType=" + code);
        }

        return result;
    }

}
