package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: xieyj
 * @since: 2016年12月24日 下午1:51:38
 * @history:
 */
public enum EClient {

    /**
     * H5 {0}表示交易所区分
     */
    H5("h5", "H5"),

    APP("app", "APP"),

    Android("Android", "Android"),

    iOS("iOS", "iOS"),

    ;

    EClient(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public static Map<String, EClient> getMap() {
        Map<String, EClient> map = new HashMap<String, EClient>();
        for (EClient currency : EClient.values()) {
            map.put(currency.getCode(), currency);
        }
        return map;
    }

    public static EClient get(String code) {
        Map<String, EClient> map = getMap();
        EClient result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "KEY=" + code);
        }

        return result;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
