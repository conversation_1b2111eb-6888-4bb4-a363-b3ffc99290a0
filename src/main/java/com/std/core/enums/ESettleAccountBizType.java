package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;

import java.util.HashMap;
import java.util.Map;

/**
 * 支付记录Enum
 *
 * <AUTHOR> LEO
 * @since : 2020-09-12 16:58
 */
public enum ESettleAccountBizType {

// 数据字典执行SQL
// INSERT INTO "public"."tsys_dict"(key, value, remark) VALUES ('pay_record.biztype', '0:充值,1:一口价订单支付', '支付记录业务类型');

    /**
     * 充值
     */
    SETTLE_ACCOUNT_BIZ_TYPE_0("0", "充值"),

    SETTLE_ACCOUNT_BIZ_TYPE_2("2", "寄售订单支付"),

    SETTLE_ACCOUNT_BIZ_TYPE_3("3", "竞拍支付保证金"),

    SETTLE_ACCOUNT_BIZ_TYPE_4("4", "竞拍成功支付"),

    SETTLE_ACCOUNT_BIZ_TYPE_5("5", "竞拍成功违约扣除保证金"),

    SETTLE_ACCOUNT_BIZ_TYPE_6("6", "导出藏品支付"),

    SETTLE_ACCOUNT_BIZ_TYPE_7("7", "diy支付"),

    SETTLE_ACCOUNT_BIZ_TYPE_8("8", "一级市场购买藏品"),

    SETTLE_ACCOUNT_BIZ_TYPE_9("9", "转赠藏品支付手续费"),

    SETTLE_ACCOUNT_BIZ_TYPE_10("10", "报名期数抽奖"),

    SETTLE_ACCOUNT_BIZ_TYPE_11("11", "渠道流转"),

    SETTLE_ACCOUNT_BIZ_TYPE_12("12", "一级市场竞拍保证金"),

    SETTLE_ACCOUNT_BIZ_TYPE_13("13", "一级市场竞拍中拍支付"),

    SETTLE_ACCOUNT_BIZ_TYPE_14("14", "幸运抽奖"),

    SETTLE_ACCOUNT_BIZ_TYPE_15("15", "降价竞拍保证金"),

    SETTLE_ACCOUNT_BIZ_TYPE_16("16", "降价竞拍"),

    SETTLE_ACCOUNT_BIZ_TYPE_17("17", "申请结算"),

    SETTLE_ACCOUNT_BIZ_TYPE_18("18", "结算成功，已到账"),

    SETTLE_ACCOUNT_BIZ_TYPE_19("19", "结算失败打回"),

    SETTLE_ACCOUNT_BIZ_TYPE_20("20", "每日自主结算"),



    ;

    private final String code;
    private final String value;

    ESettleAccountBizType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, ESettleAccountBizType> getSettleAccountBizTypeResultMap() {
        Map<String, ESettleAccountBizType> map = new HashMap<String, ESettleAccountBizType>();
        for (ESettleAccountBizType type : ESettleAccountBizType.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static ESettleAccountBizType getSettleAccountBizType(String code) {
        Map<String, ESettleAccountBizType> map = getSettleAccountBizTypeResultMap();
        ESettleAccountBizType result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "ESettleAccountBizType=" + code);
        }

        return result;
    }

}
