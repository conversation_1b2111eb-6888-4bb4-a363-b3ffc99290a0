package com.std.core.enums;

/**
 * 上下架处理状态
 */
public enum EGoodsTransferResultStatus {

    /**
     * 失败
     */
    FAILURE(0, "失败"),

    /**
     * 成功
     */
    SUCCESS(1, "成功"),

    /**
     * 转增中
     */
    TRANFERING(2, "转增中"),
    ;

    EGoodsTransferResultStatus(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    private Integer code;

    private String value;

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
