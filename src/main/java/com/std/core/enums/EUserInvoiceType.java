package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 用户开票抬头Enum
*
* <AUTHOR> ycj
* @since : 2022-08-18 15:59
*/
public enum EUserInvoiceType {

// 数据字典执行SQL
// INSERT INTO "public"."tsys_dict"(key, value, remark) VALUES ('user_invoice.type', '{"0":"个人","1":"企业单位"}', '用户开票抬头类型');

    /**
    * 个人
    */
    USER_INVOICE_TYPE_0("0", "个人"),

    /**
    * 企业单位
    */
    USER_INVOICE_TYPE_1("1", "企业单位"),

    ;

    private String code;
    private String value;

    EUserInvoiceType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EUserInvoiceType> getUserInvoiceTypeResultMap() {
        Map<String, EUserInvoiceType> map = new HashMap<String, EUserInvoiceType>();
        for (EUserInvoiceType type : EUserInvoiceType.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EUserInvoiceType getUserInvoiceType(String code) {
        Map<String, EUserInvoiceType> map = getUserInvoiceTypeResultMap();
        EUserInvoiceType result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EUserInvoiceType=" + code);
        }

        return result;
    }

}
