package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
 * 权益具体类型(0=免提现手续费 1=免费抽盲盒)
 */
public enum ECollectionRightDetailType {

    /**
     * 免提现手续费
     */
    COLLECTION_RIGHT_DETAIL_TYPE_0("0", "免提现手续费"),

    /**
     * 免费抽盲盒
     */
    COLLECTION_RIGHT_DETAIL_TYPE_1("1", "免费抽盲盒");

    private String code;
    private String value;

    ECollectionRightDetailType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, ECollectionRightDetailType> getCollectionRightDetailTypeResultMap() {
        Map<String, ECollectionRightDetailType> map = new HashMap<String, ECollectionRightDetailType>();
        for (ECollectionRightDetailType type : ECollectionRightDetailType.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static ECollectionRightDetailType getCollectionRightDetailType(String code) {
        Map<String, ECollectionRightDetailType> map = getCollectionRightDetailTypeResultMap();
        ECollectionRightDetailType result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "ECollectionRightDetail=" + code);
        }

        return result;
    }

}
