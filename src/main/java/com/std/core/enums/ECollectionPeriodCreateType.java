package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;

import java.util.HashMap;
import java.util.Map;

/**
 * 作品Enum
 *
 * <AUTHOR> xieyj
 * @since : 2021-07-08 09:36
 */
public enum ECollectionPeriodCreateType {

// 数据字典执行SQL
// INSERT INTO "public"."tsys_dict"(key, value, remark) VALUES ('collection.type', '0:自创,1:外来', '作品类型');

    /**
     * 自创作品
     */
    E_COLLECTION_PERIOD_CREATE_TYPE_0("0", "平台代创"),

    /**
     * 发行方创建
     */
    E_COLLECTION_PERIOD_CREATE_TYPE_1("1", "发行方创建"),

    ;

    private String code;
    private String value;

    ECollectionPeriodCreateType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, ECollectionPeriodCreateType> getCollectionPeriodCreateTypeResultMap() {
        Map<String, ECollectionPeriodCreateType> map = new HashMap<String, ECollectionPeriodCreateType>();
        for (ECollectionPeriodCreateType type : ECollectionPeriodCreateType.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static ECollectionPeriodCreateType getCollectionPeriodCreateType(String code) {
        Map<String, ECollectionPeriodCreateType> map = getCollectionPeriodCreateTypeResultMap();
        ECollectionPeriodCreateType result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "ECollectionPeriodCreateType=" + code);
        }

        return result;
    }

}
