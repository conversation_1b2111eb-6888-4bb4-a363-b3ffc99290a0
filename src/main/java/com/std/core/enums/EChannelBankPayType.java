package com.std.core.enums;

/**
 * 渠道银行类型
 */
public enum EChannelBankPayType {

    /**
     * 支付宝
     */
    PAY("0", "支付宝"),

    /**
     * 微信
     */
    WITHDRAW("1", "微信"),

    /**
     * 银行卡
     */
    BANK("2", "银行卡"),

    /**
     * 易宝钱包
     */
    YEEPAY_WALLET("3", "易宝钱包"),
    ;

    EChannelBankPayType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
