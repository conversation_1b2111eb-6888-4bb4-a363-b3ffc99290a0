package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
 * 新春活动Enum
 *
 * <AUTHOR> ycj
 * @since : 2022-01-21 17:43
 */
public enum EContractTokenExportDetailBroadStatus {


    /**
     * 待广播
     */
    TO_BROADCAST("0", "待广播"),

    /**
     * 广播中
     */
    BROADCASTING("1", "广播中"),

    /**
     * 广播成功
     */
    SUCCESS("2", "广播成功"),

    FAIL("3", "广播失败"),

    ;

    private String code;
    private String value;

    EContractTokenExportDetailBroadStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EContractTokenExportDetailBroadStatus> getContractTokenExportDetailStatusResultMap() {
        Map<String, EContractTokenExportDetailBroadStatus> map = new HashMap<String, EContractTokenExportDetailBroadStatus>();
        for (EContractTokenExportDetailBroadStatus type : EContractTokenExportDetailBroadStatus.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static EContractTokenExportDetailBroadStatus getContractTokenExportDetailStatus(String code) {
        Map<String, EContractTokenExportDetailBroadStatus> map = getContractTokenExportDetailStatusResultMap();
        EContractTokenExportDetailBroadStatus result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "EContractTokenExportDetailStatus=" + code);
        }

        return result;
    }

}
