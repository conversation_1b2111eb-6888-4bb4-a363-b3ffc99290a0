package com.std.core.enums;


public enum EContractTokenNumType {

    /**
     * 单个
     */
    ONE("0", "单个"),

    /**
     * 多个
     */
    MORE("1", "多个");

    EContractTokenNumType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
