package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 元宇宙坑位Enum
*
* <AUTHOR> ycj
* @since : 2022-05-06 15:07
*/
public enum EPitStatus {

// 数据字典执行SQL
// INSERT INTO "public"."tsys_dict"(key, value, remark) VALUES ('pit.status', '{"0":"待使用","1":"使用中"}', '元宇宙坑位状态');

    /**
    * 待使用
    */
    PIT_STATUS_0("0", "待使用"),

    /**
    * 使用中
    */
    PIT_STATUS_1("1", "使用中"),

    ;

    private String code;
    private String value;

    EPitStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EPitStatus> getPitStatusResultMap() {
        Map<String, EPitStatus> map = new HashMap<String, EPitStatus>();
        for (EPitStatus type : EPitStatus.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EPitStatus getPitStatus(String code) {
        Map<String, EPitStatus> map = getPitStatusResultMap();
        EPitStatus result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EPitStatus=" + code);
        }

        return result;
    }

}
