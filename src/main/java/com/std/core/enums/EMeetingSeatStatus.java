package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 会议室座位Enum
*
* <AUTHOR> ycj
* @since : 2023-05-10 16:08
*/
public enum EMeetingSeatStatus {

// 数据字典执行SQL
// INSERT INTO "public"."tsys_dict"(key, value, remark) VALUES ('meeting_seat.status', '{"0":"未使用","1":"已使用"}', '会议室座位状态');

    /**
    * 未使用
    */
    MEETING_SEAT_STATUS_0("0", "未使用"),

    /**
    * 已使用
    */
    MEETING_SEAT_STATUS_1("1", "已使用"),

    ;

    private String code;
    private String value;

    EMeetingSeatStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EMeetingSeatStatus> getMeetingSeatStatusResultMap() {
        Map<String, EMeetingSeatStatus> map = new HashMap<String, EMeetingSeatStatus>();
        for (EMeetingSeatStatus type : EMeetingSeatStatus.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EMeetingSeatStatus getMeetingSeatStatus(String code) {
        Map<String, EMeetingSeatStatus> map = getMeetingSeatStatusResultMap();
        EMeetingSeatStatus result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EMeetingSeatStatus=" + code);
        }

        return result;
    }

}
