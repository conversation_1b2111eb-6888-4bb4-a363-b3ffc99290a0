package com.std.core.enums;

/**
 * 作品机构口令状态
 */
public enum EPeriodChannelWordStatus {

    /**
     * 可使用
     */
    USE("0", "可使用"),

    /**
     * 已作废
     */
    DROP("1", "已作废");

    EPeriodChannelWordStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
