package com.std.core.enums;

/**
 * 顶部banner Action
 */
public enum ECnavAction {

    ACTION_0("0", "不能点击"),

    ACTION_1("1", "跳转本系统"),

    ACTION_2("2", "跳转外部链接(需过渡界面)"),

    ACTION_3("3", "跳转外部链接(需授权,需过渡界面)"),

    ACTION_4("4", "跳转外部链接(直接跳过去,无授权,无过渡界面)"),

    ;

    ECnavAction(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
