package com.std.core.enums;

/**
 * <AUTHOR> Silver
 * @since : 2019-01-11 22:10
 */
public enum EDirection {

    /**
     * 增加
     */
    INCREASE("1", "增加"),

    /**
     * 扣减
     */
    DECREASE("0", "扣减");

    EDirection(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
