package com.std.core.enums;

/**
 * 渠道类型分两大类：外部渠道和唯一的内部渠道（内部账）
 *
 * @author: lei
 * @since: 2018年8月23日 下午9:48:56
 * @history:
 */
public enum EChangeSeriesType {
    /**
     * 百变大咖
     */
    ZERO("0", "百变大咖"),

    /**
     * 幸运抽奖
     */
    ONE("1", "幸运抽奖");

    EChangeSeriesType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

}
