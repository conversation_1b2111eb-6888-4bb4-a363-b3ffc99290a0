package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 直播会议申请Enum
*
* <AUTHOR> wzh
* @since : 2023-05-04 10:08
*/
public enum EMeetingApplicationStatus {

// 数据字典执行SQL
// INSERT INTO "public"."tsys_dict"(key, value, remark) VALUES ('meeting_application.status', '{"0":"待开始","1":"进行中","2":"已结束"}', '直播会议申请状态');

    /**
    * 待开始
    */
    MEETING_APPLICATION_STATUS_0("0", "待开始"),

    /**
    * 进行中
    */
    MEETING_APPLICATION_STATUS_1("1", "进行中"),

    /**
    * 已结束
    */
    MEETING_APPLICATION_STATUS_2("2", "已结束"),

    MEETING_APPLICATION_STATUS_3("3", "已作废"),
    MEETING_APPLICATION_STATUS_4("4", "强制结束"),
    ;

    private String code;
    private String value;

    EMeetingApplicationStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EMeetingApplicationStatus> getMeetingApplicationStatusResultMap() {
        Map<String, EMeetingApplicationStatus> map = new HashMap<String, EMeetingApplicationStatus>();
        for (EMeetingApplicationStatus type : EMeetingApplicationStatus.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EMeetingApplicationStatus getMeetingApplicationStatus(String code) {
        Map<String, EMeetingApplicationStatus> map = getMeetingApplicationStatusResultMap();
        EMeetingApplicationStatus result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EMeetingApplicationStatus=" + code);
        }

        return result;
    }

}
