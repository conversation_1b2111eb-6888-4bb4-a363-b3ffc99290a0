package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Silver
 * @since : 2019-01-11 22:10
 */
public enum ECnavigateState {

    /**
     * 待发布
     */
    wait("0", "待发布"),
    /**
     * 已发布
     */
    up("1", "已发布"),

    /**
     * 否
     */
    down("2", "已下架");

    ECnavigateState(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, ECnavigateState> getCnavigateStatusResultMap() {
        Map<String, ECnavigateState> map = new HashMap<String, ECnavigateState>();
        for (ECnavigateState type : ECnavigateState.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static ECnavigateState getCnavigateStatus(String code) {
        Map<String, ECnavigateState> map = getCnavigateStatusResultMap();
        ECnavigateState result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "ECnavigateState=" + code);
        }

        return result;
    }
}
