package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;

import java.util.HashMap;
import java.util.Map;

/**
* 作品期数开始状态Enum
*
* <AUTHOR> ycj
* @since : 2021-11-03 20:27
*/
public enum EPeriodAuctionMyrecordBondStatus {

// 数据字典执行SQL
// INSERT INTO "public"."tsys_dict"(key, value, remark) VALUES ('collection_period.status', '{0:待上架,1:已上架,2:已下架}', '作品期数状态');

    /**
     * 锁定中
     */
    E_PERIOD_AUCTION_MYRECORD_BOND_STATUS_0("0", "锁定中"),

    /**
    * 已退回
    */
    E_PERIOD_AUCTION_MYRECORD_BOND_STATUS_1("1", "已退回"),

    /**
    * 违约已扣除
    */
    E_PERIOD_AUCTION_MYRECORD_BOND_STATUS_2("2", "违约已扣除"),

    /**
     * 退还处理中
     */
    E_PERIOD_AUCTION_MYRECORD_BOND_STATUS_3("3", "退还处理中"),
    ;

    private String code;
    private String value;

    EPeriodAuctionMyrecordBondStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EPeriodAuctionMyrecordBondStatus> getPeriodAuctionMyrecordStatusResultMap() {
        Map<String, EPeriodAuctionMyrecordBondStatus> map = new HashMap<String, EPeriodAuctionMyrecordBondStatus>();
        for (EPeriodAuctionMyrecordBondStatus type : EPeriodAuctionMyrecordBondStatus.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EPeriodAuctionMyrecordBondStatus getPeriodAuctionMyrecordStatus(String code) {
        Map<String, EPeriodAuctionMyrecordBondStatus> map = getPeriodAuctionMyrecordStatusResultMap();
        EPeriodAuctionMyrecordBondStatus result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "ECollectionPeriodSoldStatus=" + code);
        }

        return result;
    }

}
