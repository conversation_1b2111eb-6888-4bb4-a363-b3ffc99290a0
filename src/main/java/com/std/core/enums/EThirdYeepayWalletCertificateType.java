package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户易宝钱包Enum
 *
 * <AUTHOR> wzh
 * @since : 2023-08-15 15:06
 */
public enum EThirdYeepayWalletCertificateType {

// 数据字典执行SQL
// INSERT INTO "public"."tsys_dict"(key, value, remark) VALUES ('third_yeepay_wallet.certificatetype', '{"0":"身份证"}', '用户易宝钱包证件类型');

    /**
     * 身份证
     */
    THIRD_YEEPAY_WALLET_CERTIFICATETYPE_0("0", "身份证"),

    ;

    private String code;
    private String value;

    EThirdYeepayWalletCertificateType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EThirdYeepayWalletCertificateType> getThirdYeepayWalletCertificateTypeResultMap() {
        Map<String, EThirdYeepayWalletCertificateType> map = new HashMap<String, EThirdYeepayWalletCertificateType>();
        for (EThirdYeepayWalletCertificateType type : EThirdYeepayWalletCertificateType.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static EThirdYeepayWalletCertificateType getThirdYeepayWalletCertificateType(String code) {
        Map<String, EThirdYeepayWalletCertificateType> map = getThirdYeepayWalletCertificateTypeResultMap();
        EThirdYeepayWalletCertificateType result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "EThirdYeepayWalletCertificateType=" + code);
        }

        return result;
    }

}
