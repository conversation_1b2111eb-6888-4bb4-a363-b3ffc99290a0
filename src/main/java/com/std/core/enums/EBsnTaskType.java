package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
 * bsn分类铸造/nft铸造Enum
 *
 * <AUTHOR> xieyj
 * @since : 2022-10-12 16:42
 */
public enum EBsnTaskType {

// 数据字典执行SQL
// INSERT INTO "public"."tsys_dict"(key, value, remark) VALUES ('bsn_task.type', '{"0":"新建分类","1":"铸造NFT"}', 'bsn分类铸造/nft铸造类型');

    /**
     * 新建分类
     */
    BSN_TASK_TYPE_0("0", "分类"),

    /**
     * 铸造NFT
     */
    BSN_TASK_TYPE_1("1", "NFT"),


    /**
     * 转赠NFT
     */
    BSN_TASK_TYPE_2("2", "tranfer"),

    /**
     * 三方转出
     */
    BSN_TASK_TYPE_3("3", "三方转出"),

    /**
     * 三方转入
     */
    BSN_TASK_TYPE_4("4", "三方转入"),
    ;

    private String code;
    private String value;

    EBsnTaskType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EBsnTaskType> getBsnTaskTypeResultMap() {
        Map<String, EBsnTaskType> map = new HashMap<String, EBsnTaskType>();
        for (EBsnTaskType type : EBsnTaskType.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static EBsnTaskType getBsnTaskType(String code) {
        Map<String, EBsnTaskType> map = getBsnTaskTypeResultMap();
        EBsnTaskType result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "EBsnTaskType=" + code);
        }

        return result;
    }

}
