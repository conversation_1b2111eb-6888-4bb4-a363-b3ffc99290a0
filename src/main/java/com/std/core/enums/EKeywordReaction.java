package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 关键字Enum
*
* <AUTHOR> xieyj
* @since : 2021-07-05 21:02
*/
public enum EKeywordReaction {

// 数据字典执行SQL
// INSERT INTO "public"."tsys_dict"(key, value, remark) VALUES ('keyword.reaction', '1:直接拦截,2:替换,3:审核', '关键字反应');

    /**
    * 直接拦截
    */
    KEYWORD_REACTION_0("1", "直接拦截"),

    /**
    * 替换
    */
    KEYWORD_REACTION_1("2", "替换"),

    /**
    * 审核
    */
    KEYWORD_REACTION_2("3", "审核"),

    ;

    private String code;
    private String value;

    EKeywordReaction(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EKeywordReaction> getKeywordReactionResultMap() {
        Map<String, EKeywordReaction> map = new HashMap<String, EKeywordReaction>();
        for (EKeywordReaction type : EKeywordReaction.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EKeywordReaction getKeywordReaction(String code) {
        Map<String, EKeywordReaction> map = getKeywordReactionResultMap();
        EKeywordReaction result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EKeywordReaction=" + code);
        }

        return result;
    }

}
