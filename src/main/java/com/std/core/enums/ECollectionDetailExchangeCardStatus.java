package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 藏品型号兑换Xmeta上架卡Enum
*
* <AUTHOR> xieyj
* @since : 2022-12-13 17:32
*/
public enum ECollectionDetailExchangeCardStatus {

// 数据字典执行SQL
// INSERT INTO "public"."tsys_dict"(key, value, remark) VALUES ('collection_detail_exchange_card.status', '{"0":"待使用","1":"已使用"}', '藏品型号兑换Xmeta上架卡');

    /**
    * 待使用
    */
    COLLECTION_DETAIL_EXCHANGE_CARD_STATUS_0("0", "待使用"),

    /**
    * 已使用
    */
    COLLECTION_DETAIL_EXCHANGE_CARD_STATUS_1("1", "已使用"),

    ;

    private String code;
    private String value;

    ECollectionDetailExchangeCardStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, ECollectionDetailExchangeCardStatus> getCollectionDetailExchangeCardStatusResultMap() {
        Map<String, ECollectionDetailExchangeCardStatus> map = new HashMap<String, ECollectionDetailExchangeCardStatus>();
        for (ECollectionDetailExchangeCardStatus type : ECollectionDetailExchangeCardStatus.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static ECollectionDetailExchangeCardStatus getCollectionDetailExchangeCardStatus(String code) {
        Map<String, ECollectionDetailExchangeCardStatus> map = getCollectionDetailExchangeCardStatusResultMap();
        ECollectionDetailExchangeCardStatus result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "ECollectionDetailExchangeCardStatus=" + code);
        }

        return result;
    }

}
