package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
 * 作品Enum
 *
 * <AUTHOR> xieyj
 * @since : 2021-07-08 09:36
 */
public enum ECollectionPayStatus {

    /**
     * 待支付
     */
    COLLECTION_PAYSTATUS_0("0", "待支付"),

    /**
     * 已支付
     */
    COLLECTION_PAYSTATUS_1("1", "已支付"),

    /**
     * 支付失败
     */
    COLLECTION_PAYSTATUS_2("2", "支付失败"),

    /**
     * 无需支付
     */
    COLLECTION_PAYSTATUS_3("3", "无需支付"),

    /**
     * 无需支付
     */
    COLLECTION_PAYSTATUS_4("4", "支付中"),

    ;

    private String code;
    private String value;

    ECollectionPayStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, ECollectionPayStatus> getCollectionPayStatusResultMap() {
        Map<String, ECollectionPayStatus> map = new HashMap<String, ECollectionPayStatus>();
        for (ECollectionPayStatus type : ECollectionPayStatus.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static ECollectionPayStatus getCollectionPayStatus(String code) {
        Map<String, ECollectionPayStatus> map = getCollectionPayStatusResultMap();
        ECollectionPayStatus result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "ECollectionPayStatus=" + code);
        }

        return result;
    }

}
