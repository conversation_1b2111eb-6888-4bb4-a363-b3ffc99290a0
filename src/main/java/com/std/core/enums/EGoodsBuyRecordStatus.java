package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 兑现记录Enum
*
* <AUTHOR> ycj
* @since : 2022-04-26 01:43
*/
public enum EGoodsBuyRecordStatus {

// 数据字典执行SQL
// INSERT INTO "public"."tsys_dict"(key, value, remark) VALUES ('goods_buy_record.status', '{"0":"待发货","1":"已发货","2":"已完成"}', '兑现记录状态');

    /**
    * 待发货
    */
    GOODS_BUY_RECORD_STATUS_0("0", "待发货"),

    /**
    * 已发货
    */
    GOODS_BUY_RECORD_STATUS_1("1", "已发货"),

    /**
    * 已完成
    */
    GOODS_BUY_RECORD_STATUS_2("2", "已完成"),

    /**
     * 已取消
     */
    GOODS_BUY_RECORD_STATUS_3("3", "已取消"),
    ;

    private String code;
    private String value;

    EGoodsBuyRecordStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EGoodsBuyRecordStatus> getGoodsBuyRecordStatusResultMap() {
        Map<String, EGoodsBuyRecordStatus> map = new HashMap<String, EGoodsBuyRecordStatus>();
        for (EGoodsBuyRecordStatus type : EGoodsBuyRecordStatus.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EGoodsBuyRecordStatus getGoodsBuyRecordStatus(String code) {
        Map<String, EGoodsBuyRecordStatus> map = getGoodsBuyRecordStatusResultMap();
        EGoodsBuyRecordStatus result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EGoodsBuyRecordStatus=" + code);
        }

        return result;
    }

}
