package com.std.core.enums;

/**
 * 接口类型
 *
 * @author: haiqingzheng
 * @since: 2019/1/14 19:43
 */
public enum EActionType {

    OPERATE("operate", "操作"),

    QUERY("query", "查询");

    EActionType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
