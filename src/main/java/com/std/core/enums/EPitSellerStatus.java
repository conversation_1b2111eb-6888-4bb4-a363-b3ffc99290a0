package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 坑位售卖Enum
*
* <AUTHOR> ycj
* @since : 2022-05-06 16:19
*/
public enum EPitSellerStatus {

// 数据字典执行SQL
// INSERT INTO "public"."tsys_dict"(key, value, remark) VALUES ('pit_seller.status', '{"0":"进行中","1":"已结束","2":"失败"}', '坑位售卖状态');

    /**
    * 进行中
    */
    PIT_SELLER_STATUS_0("0", "进行中"),

    /**
    * 已结束
    */
    PIT_SELLER_STATUS_1("1", "已结束"),

    /**
    * 失败
    */
    PIT_SELLER_STATUS_2("2", "失败"),

    /**
     * 强制下架
     */
    PIT_SELLER_STATUS_3("3","强制下架"),
    ;

    private String code;
    private String value;

    EPitSellerStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EPitSellerStatus> getPitSellerStatusResultMap() {
        Map<String, EPitSellerStatus> map = new HashMap<String, EPitSellerStatus>();
        for (EPitSellerStatus type : EPitSellerStatus.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EPitSellerStatus getPitSellerStatus(String code) {
        Map<String, EPitSellerStatus> map = getPitSellerStatusResultMap();
        EPitSellerStatus result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EPitSellerStatus=" + code);
        }

        return result;
    }

}
