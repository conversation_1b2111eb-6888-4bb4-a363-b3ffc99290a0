package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
 * 审核记录Enum
 *
 * <AUTHOR> ycj
 * @since : 2022-06-14 20:06
 */
public enum EApproveRecordRefType {

// 数据字典执行SQL
// INSERT INTO "public"."tsys_dict"(key, value, remark) VALUES ('approve_record.reftype', '{"0":"发行方申请","1":"作品申请"}', '审核记录类型');

    /**
     * 发行方申请
     */
    APPROVE_RECORD_REFTYPE_0("0", "发行方申请"),

    /**
     * 作品申请
     */
    APPROVE_RECORD_REFTYPE_1("1", "作品申请"),

    /**
     * 挑战申请
     */
    APPROVE_RECORD_REFTYPE_2("2", "挑战申请"),

    APPROVE_RECORD_REFTYPE_3("3", "发行方修改信息申请"),
    APPROVE_RECORD_REFTYPE_4("4", "发行方期数申请"),

    APPROVE_RECORD_REFTYPE_5("5", "发行方公告申请"),

    APPROVE_RECORD_REFTYPE_6("6", "发行方实物申请"),
    ;

    private String code;
    private String value;

    EApproveRecordRefType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EApproveRecordRefType> getApproveRecordRefTypeResultMap() {
        Map<String, EApproveRecordRefType> map = new HashMap<String, EApproveRecordRefType>();
        for (EApproveRecordRefType type : EApproveRecordRefType.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static EApproveRecordRefType getApproveRecordRefType(String code) {
        Map<String, EApproveRecordRefType> map = getApproveRecordRefTypeResultMap();
        EApproveRecordRefType result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "EApproveRecordRefType=" + code);
        }

        return result;
    }

}
