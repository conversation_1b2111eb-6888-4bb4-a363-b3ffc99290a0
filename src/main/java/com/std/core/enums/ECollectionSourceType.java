package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
 * 作品Enum
 *
 * <AUTHOR> xieyj
 * @since : 2021-07-08 09:36
 */
public enum ECollectionSourceType {

    /**
     * 自创作品
     */
    TYPE_0("0", "自创作品"),

    /**
     * 外部导入
     */
    YPE_1("1", "外部导入");

    private String code;
    private String value;

    ECollectionSourceType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, ECollectionSourceType> getCollectionTypeResultMap() {
        Map<String, ECollectionSourceType> map = new HashMap<String, ECollectionSourceType>();
        for (ECollectionSourceType type : ECollectionSourceType.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static ECollectionSourceType getCollectionType(String code) {
        Map<String, ECollectionSourceType> map = getCollectionTypeResultMap();
        ECollectionSourceType result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "ECollectionType=" + code);
        }

        return result;
    }

}
