package com.std.core.enums;

/**
 * @author: xieyj
 * @since: 2016年11月11日 上午10:54:16
 * @history:
 */
public enum ESettleAccountJourType {
    /**
     * 待结算金额
     */
    E_SETTLE_ACCOUNT_JOUR_TYPE_0("0", "待结算金额"),

    /**
     * 结算金额
     */
    E_SETTLE_ACCOUNT_JOUR_TYPE_1("1", "结算金额"),

    /**
     * 结算中金额
     */
    E_SETTLE_ACCOUNT_JOUR_TYPE_2("2", "结算中金额"),

    ;

    ESettleAccountJourType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
