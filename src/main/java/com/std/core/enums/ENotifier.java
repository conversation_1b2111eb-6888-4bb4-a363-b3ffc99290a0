package com.std.core.enums;

/**
 * <AUTHOR> Silver
 * @since : 2019-01-11 22:10
 */
public enum ENotifier {

    /**
     * 是
     */
    TYPE_0("0", "申请提币通知"),

    /**
     * 否
     */
    TYPE_1("1", "广播通知");

    ENotifier(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
