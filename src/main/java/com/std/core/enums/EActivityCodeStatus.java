package com.std.core.enums;

/**
 * @author: xieyj
 * @since: 2016年11月11日 上午10:54:16
 * @history:
 */
public enum EActivityCodeStatus {

    /**
     * 状态（0未使用 1已使用 2已作废）
     *
     */
    /**
     * 未使用
     */
    NOT_USE("0", "未使用"),

    /**
     * 已使用
     */
    USED("1", "已使用"),

    /**
     * 已作废
     */
    TO_VOID("2", "已作废"),

    ;

    EActivityCodeStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

}
