package com.std.core.AgoraIO.agora;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.std.core.config.AgoraConfig;
import com.std.core.pojo.domain.AgoraPrivileges;
import com.std.core.util.HttpClientUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.URI;
//import java.net.http.HttpClient;
//import java.net.http.HttpRequest;
//import java.net.http.HttpResponse;
import java.util.Base64;


// 基于 Java 实现的 HTTP 基本认证示例，使用 RTC 的服务端 RESTful API
public class Base64Encoding {

    private static Logger logger = LoggerFactory.getLogger(HttpClientUtils.class); // 日志记录

    private static RequestConfig requestConfig = null;

    static {
        // 设置请求和传输超时时间
        requestConfig = RequestConfig.custom().setSocketTimeout(10000).setConnectTimeout(10000).build();
    }

    public static JSONObject httpPost(String cname, Integer uid, String appId, String customerKey, String customerSecret) {

        AgoraPrivileges agoraPrivileges = new AgoraPrivileges();
        agoraPrivileges.setAppid(appId);
        String[] privileges = new String[1];
        privileges[0] = "join_channel";
//        privileges[1] = "publish_audio";
//        privileges[2] = "publish_video";
        agoraPrivileges.setPrivileges(privileges);
        agoraPrivileges.setCname(cname);
        agoraPrivileges.setUid(uid);
        agoraPrivileges.setTime(0);
        agoraPrivileges.setTime_in_seconds(0);

        String url = "https://api.agora.io/dev/v1/kicking-rule";
//        // 客户 ID
//        final String customerKey = agoraConfig.getCustomerKey();
//        // 客户密钥
//        final String customerSecret = agoraConfig.getCustomerSecret();

        // 拼接客户 ID 和客户密钥并使用 base64 编码
        String plainCredentials = customerKey + ":" + customerSecret;
        String base64Credentials = new String(Base64.getEncoder().encode(plainCredentials.getBytes()));
        // 创建 authorization header
        String authorizationHeader = "Basic " + base64Credentials;

        CloseableHttpClient httpClient = HttpClients.createDefault();
        JSONObject jsonResult = null;
        HttpPost httpPost = new HttpPost(url);

        // 设置请求和传输超时时间
        httpPost.setConfig(requestConfig);
        httpPost.setHeader("Authorization", authorizationHeader);
        httpPost.setHeader("Content-Type", "application/json");

        String jsonString = JSON.toJSONString(agoraPrivileges);
        JSONObject jsonParam = JSON.parseObject(jsonString);
        try {
            if (null != jsonParam) {
                // 解决中文乱码问题
                StringEntity entity = new StringEntity(jsonParam.toString(), "utf-8");
                entity.setContentEncoding("UTF-8");
                entity.setContentType("application/json");
                httpPost.setEntity(entity);
            }
            CloseableHttpResponse result = httpClient.execute(httpPost);
            // 请求发送成功，并得到响应
            if (result.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                String str = "";
                try {
                    // 读取服务器返回过来的json字符串数据
                    str = EntityUtils.toString(result.getEntity(), "utf-8");
                    // 把json字符串转换成json对象
                    jsonResult = JSONObject.parseObject(str);
                } catch (Exception e) {
                    logger.error("post请求提交失败:" + url, e);
                }
            }
        } catch (IOException e) {
            logger.error("post请求提交失败:" + url, e);
        } finally {
            httpPost.releaseConnection();
        }

        return jsonResult;
    }

    /**
     * 发送get请求
     */
    public static JSONObject httpGet(String cname, String appId, String customerKey, String customerSecret) {
        // get请求返回结果
        JSONObject jsonResult = null;
        CloseableHttpClient client = HttpClients.createDefault();

        String plainCredentials = customerKey + ":" + customerSecret;
        String base64Credentials = new String(Base64.getEncoder().encode(plainCredentials.getBytes()));
        // 创建 authorization header
        String authorizationHeader = "Basic " + base64Credentials;
        // 发送get请求
        HttpGet request = new HttpGet("https://api.agora.io/dev/v1/channel/user/" + appId + "/" + cname);
        request.setConfig(requestConfig);
        request.setHeader("Authorization", authorizationHeader);
        request.setHeader("Content-Type", "application/json");
        try {
            CloseableHttpResponse response = client.execute(request);

            // 请求发送成功，并得到响应
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                // 读取服务器返回过来的json字符串数据
                HttpEntity entity = response.getEntity();
                String strResult = EntityUtils.toString(entity, "utf-8");
                // 把json字符串转换成json对象
                jsonResult = JSONObject.parseObject(strResult);
            } else {
                logger.error("get请求提交失败:" + "https://api.agora.io/dev/v1/channel/user/");
            }
        } catch (IOException e) {
            logger.error("get请求提交失败:" + "https://api.agora.io/dev/v1/channel/user/", e);
        } finally {
            request.releaseConnection();
        }


        return jsonResult;
    }

    public static void main(String[] args) {
        JSONObject res = httpGet("0311568e34d24b86bdf8533f2774eac4", "8a38fac59b5f4f8d96aae9b14aec176e", "cb3942726cc3450e8e258fa94b993042", "12380624a1e345b0933a1b6553f7b475");
        System.out.println(res.toJSONString());
        System.out.println( res.getJSONObject("data").getString("channel_exist"));
    }
}