package com.std.core.util;

import com.alibaba.fastjson.JSONObject;
import org.java_websocket.WebSocket;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft_6455;
import org.java_websocket.handshake.ServerHandshake;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.ByteBuffer;
import java.time.Instant;
import java.util.zip.GZIPInputStream;

public class WebsocketClient {
    public static WebSocketClient client;

    /* 主方法 */
    public static void main(String[] args) throws InterruptedException {
        try {
            client = new WebSocketClient(new URI("wss://api.huobiasia.vip/ws"), new Draft_6455()) {
                @Override
                public void onOpen(ServerHandshake serverHandshake) {
                    System.out.println("<<<握手-成功>>>");
                }

                @Override
                public void onMessage(String msg) {
                    System.out.println("<<<收到-消息>>>" + msg);
                }

                @Override
                public void onMessage(ByteBuffer bytes) {
                    try {
                        String message = new String(ZipUtil.解压GZIP数据(bytes.array()), "UTF-8");/* 解开GZIP压缩 */
                        JSONObject jsonObject = JSONObject.parseObject(message);/* 转换成Json */
                        if (null != jsonObject.getString("ping")) {/* 判断是不是Ping */
                            System.out.println("收到的消息：" + message);/* 收到Ping消息 */
                            JSONObject json = new JSONObject();
                            json.put("pong", 获取秒数());
                            System.out.println("发送消息：" + json.toString());
                            send(json.toString());/* 发送Pong回应 */
                        } else {
                            System.err.println("收到的消息：" + message);
                            //市场概要
                            if (message.contains("market.btcusdt.detail")) {
                                JSONObject json = JSONObject.parseObject(message).getJSONObject("tick");
                                BigDecimal close = json.getBigDecimal("close");
                                BigDecimal open = json.getBigDecimal("open");
                                System.out.println(close);
                                System.out.println(open);
                                System.out.println(close.subtract(open).divide(open, 4, BigDecimal.ROUND_DOWN).multiply(new BigDecimal(100)));
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                @Override
                public void onClose(int i, String s, boolean b) {
                    System.out.println("<<<连接-关闭>>>");
                }

                @Override
                public void onError(Exception e) {
                    e.printStackTrace();
                    System.out.println("<<<错误-关闭>>>");
                }

                long 获取秒数() {
                    return Instant.now().getEpochSecond();
                }
            };
        } catch (URISyntaxException e) {
            e.printStackTrace();
        }

        client.connect();
        System.out.println("正在连接");
        while (!client.getReadyState().equals(WebSocket.READYSTATE.OPEN)) {
            System.out.print(".");
            Thread.sleep(500);
        }


        //请求历史数据，
        client.send("{\"req\":\"market.btcusdt.kline.1min\",\"symbol\":\"btcusdt\",\"period\":\"1min\"}");


        if (true) {
            return;
        }

        //订阅市场概要
        client.send("{\"sub\":\"market.btcusdt.detail\",\"symbol\":\"btcusdt\"}");


        if (true) {
            return;
        }

        //请求成交明细
        client.send("{\"req\":\"market.btcusdt.trade.detail\",\"symbol\":\"btcusdt\"}");
        Thread.sleep(100);
        //订阅成交明细
        client.send("{\"sub\":\"market.btcusdt.trade.detail\",\"symbol\":\"btcusdt\"}");
        Thread.sleep(100);

        //订阅盘口
        client.send("{\"sub\":\"market.btcusdt.depth.step0\",\"symbol\":\"btcusdt\",\"pick\":[\"bids.29\",\"asks.29\"],\"step\":\"step0\"}");
        Thread.sleep(100);


        //演示btc 1分钟k线 切换到 5分钟k线
        //请求历史数据，
        client.send("{\"req\":\"market.btcusdt.kline.1min\",\"symbol\":\"btcusdt\",\"period\":\"1min\"}");
        Thread.sleep(100);
        //订阅1分钟的实时推送
        client.send("{\"sub\":\"market.btcusdt.kline.1min\",\"symbol\":\"btcusdt\",\"period\":\"1min\"}");
        Thread.sleep(10000);
        //取消订阅
        client.send("{\"unsub\":\"market.btcusdt.kline.1min\"}");

        System.out.println("切换到5分钟");
        //请求历史数据，
        client.send("{\"req\":\"market.btcusdt.kline.5min\",\"symbol\":\"btcusdt\",\"period\":\"5min\"}");
        Thread.sleep(100);
        //订阅5分钟的实时推送
        client.send("{\"sub\":\"market.btcusdt.kline.5min\",\"symbol\":\"btcusdt\",\"period\":\"5min\"}");

        //切换币队 需要吧上次币队的所有订阅给取消掉
        /**
         * 比如取消 订阅 挂单===  {"unsub":"market.btcusdt.depth.step0"}
         * 取消       成交  ====  {"unsub":"market.btcusdt.trade.detail"}
         * 取消  k线    ====   {"unsub":"market.btcusdt.kline.1min"}
         */


    }
}

/* Zip工具类 */
class ZipUtil {
    public static byte[] 解压GZIP数据(byte[] depressData) throws Exception {
        ByteArrayInputStream is = new ByteArrayInputStream(depressData);
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        GZIPInputStream gis = new GZIPInputStream(is);
        int count;
        byte data[] = new byte[1024];
        while ((count = gis.read(data, 0, 1024)) != -1) {
            os.write(data, 0, count);
        }
        gis.close();
        depressData = os.toByteArray();
        os.flush();
        os.close();
        is.close();
        return depressData;
    }
}