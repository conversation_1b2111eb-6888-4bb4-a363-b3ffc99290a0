package com.std.core.util;

import com.std.common.utils.PhoneUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class PhoneCheck {

    public static void main(String[] args) {
        String remark = "[17605811948]购买[五弦琵琶]藏品消费[88.80]";
        List<String> stringList = checkCellphone(remark);

        for (String phone : stringList) {
            System.out.println(phone);
//            remark = remark.replaceAll(phone, LoginNameDealUtil.loginDeal(phone));
            remark = remark.replaceAll(phone, PhoneUtil.hideMobile(phone));
        }

        System.out.println(remark);


    }

    public static String phoneDeal(String str){
        List<String> stringList = checkCellphone(str);

        for (String phone : stringList) {
            System.out.println(phone);
            str = str.replaceAll(phone, PhoneUtil.hideMobile(phone));
        }

        System.out.println(str);
        return str;
    }

    /**
     * 查询符合的手机号码
     *
     * @param str
     */
    public static List<String> checkCellphone(String str) {
        // 将给定的正则表达式编译到模式中
//        Pattern pattern = Pattern.compile("((13[0-9])|(14[5|7])|(15([0-3]|[5-9]))|(18[0,5-9]))\\d{8}");
        Pattern pattern = Pattern.compile("[1][35678][0-9]{9}");
        // 创建匹配给定输入与此模式的匹配器。
        Matcher matcher = pattern.matcher(str);
        List<String> phoneList = new ArrayList<>();
        //查找字符串中是否有符合的子字符串
        while (matcher.find()) {
            phoneList.add(matcher.group());
            //查找到符合的即输出
//            System.out.println("查询到一个符合的手机号码："+matcher.group());
        }

        return phoneList;
    }

    /**
     * 查询符合的固定电话
     *
     * @param str
     */
    public static void checkTelephone(String str) {
        // 将给定的正则表达式编译到模式中
        Pattern pattern = Pattern.compile("(0\\d{2}-\\d{8}(-\\d{1,4})?)|(0\\d{3}-\\d{7,8}(-\\d{1,4})?)");
        // 创建匹配给定输入与此模式的匹配器。
        Matcher matcher = pattern.matcher(str);
        //查找字符串中是否有符合的子字符串
        while (matcher.find()) {
            //查找到符合的即输出
            System.out.println("查询到一个符合的固定号码：" + matcher.group());
        }
    }
}
