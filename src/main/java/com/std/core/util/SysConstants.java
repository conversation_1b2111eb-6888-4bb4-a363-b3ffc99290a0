package com.std.core.util;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2020/5/26 3:39 下午
 */
public class SysConstants {

    public static final Integer FILE_SIZE_MB = 1024 * 1024;

    public static final String CURRDAY_LOGIN = "currday_login_";

    public static final String WX_ACCESS_TOKEN = "wx_access_token";

    public static final String XMLY_ACCESS_TOKEN = "xmly_access_token";

    /**
     * 【全局配置】默认在线人数
     */
    public static final String ONLINE_COUNT = "online_count";

    /**
     * 【全局配置】在线人数放大倍数
     */
    public static final String ONLINE_COUNT_MULTIPLE = "online_count_multiple";

    /**
     * 【全局配置】系统编号
     */
    public static final String SYSTEM_CODE = "system_code";

    /**
     * 【全局配置】云wallet对接签名私钥
     */
    public static final String CLOUD_WALLET_PRIVATE_KEY = "cloud_wallet_private_key";

    /**
     * 【全局配置】邀请码是否必填
     */
    public static final String INVITE_FLAG = "invite_flag";

    /**
     * 邀请注册页
     */
    public static final String INVITE_URL = "invite_url";

    /**
     * 【全局配置】默认推荐码
     */
    public static final String DEFAULT_INVITE_CODE = "default_invite_code";

    /**
     * 用户默认个人介绍
     */
    public static final String USER_DEFAULT_INTRODUCE = "user_default_introduce";

    /**
     * 充值登陆名
     */
    public static final String CHARGE_LOGINNAME = "charge_loginname";

    /**
     * 衍生区期数购买须知
     */
    public static final String COLLECTION_PERIOD_BUY_NOTE = "collection_period_buy_note";

    /**
     * 版权区期数购买须知
     */
    public static final String COLLECTION_COPYRIGHT_PERIOD_BUY_NOTE = "collection_copyright_period_buy_note";

    /**
     * 盲盒区期数购买须知
     */
    public static final String COLLECTION_PERIOD_BLIND_BOX_BUY_NOTE = "collection_period_blind_box_buy_note";

    /**
     * 抽签区期数购买须知
     */
    public static final String COLLECTION_PERIOD_DRAW_STRAWS_BUY_NOTE = "collection_period_draw_straws_buy_note";

    /**
     * 竞拍区期数购买须知
     */
    public static final String COLLECTION_PERIOD_AUCTION_BUY_NOTE = "collection_period_auction_buy_note";

    /**
     * 一级市场锁仓小时数
     */
    public static final String FIRST_MARKET_LOCK_HOUR = "sell_time";

    /**
     * 订单自动解冻时间
     */
    public static final String ORDER_CLOSE_TIME = "order_close_time";

    /**
     * 分享说明
     */
    public static final String SHARE_NOTE = "share_note";

    /**
     * 安卓升级配置
     */
    public static final String ANDROID_C = "android-c";

    /**
     * 安卓升级版本
     */
    public static final String VERSION = "version";


    /**
     * 安卓应用市场审核标志1审核中0非审核
     */
    public static final String MARKET_APPROVE_FLAG = "market_approve_flag";

    /**
     * 安卓应用市场升级标志
     */
    public static final String MARKET_CHANNEL = "market_channel";

    /**
     * 安卓当前升级版本号
     */
    public static final String MARKET_UPGRADE_VERSION = "market_upgrade_version";

    /**
     * 安卓当前升级bannerUrl
     */
    public static final String MARKET_BANNER_URL = "market_banner_url";

    /**
     * 转赠三方支付是否打开
     */
    public static final String TRANSFER_THIRD_PAY_OPEN_FLAG = "transfer_third_pay_open_flag";

    /**
     * 一级市场抢购三方支付是否打开
     */
    public static final String FIRST_MARKET_THIRD_PAY_OPEN_FLAG = "first_market_third_pay_open_flag";

    /**
     * 转赠手续费
     */
    public static final String NFT_TRANSFER_FEE = "nft_transfer_fee";

    /**
     * 报名须知
     */
    public static final String PERIOD_DRAW_STRAWS_JOIN_NOTE = "period_draw_straws_join_note";

    /**
     * 参拍须知
     */
    public static final String PERIOD_AUCTION_JOIN_NOTE = "period_auction_join_note";

    /**
     * 保证金的说明
     */
    public static final String PERIOD_AUCTION_AUCTION_BOND_NOTE = "period_auction_auction_bond_note";

    /**
     * 用户注册协议
     */
    public static final String REGISTERED_AGREEMENT_TEXTAREA = "registered_agreement_textarea";

    /**
     * 隐私条款
     */
    public static final String PRIVACY_AGREEMENT_TEXTAREA = "privacy_agreement_textarea";

    /**
     * 人脸识别每人发起次数
     */
    public static final String FACE_PERSON_TIME = "face_person_time";

    /**
     * 发起人脸识别失败提示
     */
    public static final String FACE_FAIL_NOTE = "face_fail_note";

    /**
     * 一个身份证可认证成功次数
     */
    public static final String FACE_ID_NO_TIME = "face_id_no_time";

    /**
     * 人脸认证失败是否开通人工通道
     */
    public static final String FACE_FAIL_OPEN_MANUAL_FLAG = "face_fail_open_manual_flag";

    /**
     * 一口价出价上限
     */
    public static final String BUYOUT_PRICE_MAX = "buyout_price_max";

    /**
     * 竞拍最小起拍价格
     */
    public static final String AUCTION_MIN_START_PRICE = "system_nft_auction_min_start_price";

    /**
     * 竞拍保证金上限
     */
    public static final String AUCTION_BOND_MAX = "auction_bond_max";

    /**
     * 竞拍最大分钟时限
     */
    public static final String AUCTION_MAX_MINUTES_TIME = "system_nft_auction_max_minutes_time";

    /**
     * 竞拍延拍最大分钟时限
     */
    public static final String AUCTION_DELAYED_MINUTES_TIME = "system_nft_auction_delayed_minutes_time";

    /**
     * 拉新活动规则说明
     */
    public static final String INVITATION_ACTIVITY_PLAY_RULE = "invitation_activity_play_rule_img";

//    /**
//     * 敏感关键字
//     */
//    public static final String SENSITIVE_KEYWORD = "system_sensitive_keyword";

    /**
     * 平台分账比例
     */
    public static final String FLAT_DIVIDE_RATE = "flat_divide_rate";

    /**
     * 结算说明
     */
    public static final String SETTLE_NOTE = "settle_note";

    /**
     * 一次转赠最大数量
     */
    public static final String TRANSFER_ONE_MAX_COUNT = "transfer_one_max_count";

    /**
     * 当天转赠最大藏品数量
     */
    public static final String TRANSFER_DAY_MAX_COUNT = "transfer_day_max_count";

    /**
     * 一次导出最大数量
     */
    public static final String EXPORT_ONE_MAX_COUNT = "export_one_max_count";


    /**
     * 幸运抽奖抽取权重规则 0:按总数,1:按剩余数量
     */
    public static final String OBTAIN_CONFIG = "obtain_config";

    /**
     * 一级市场盲盒区抽取权重规则 0:按总数,1:按剩余数量
     */
    public static final String COLLECTION_PERIOD_BLIND_BOX_BUY_RULE = "collection_period_blind_box_buy_rule";

    /**
     * 易宝身份证前6位替换
     */
    public static final String ID_NO_REPLACE = "id_no_replace";

    /**
     * 易宝身份证前4位替换
     */
    public static final String ID_NO_REPLACE_4 = "id_no_replace_4";

    /**
     * 银行编号替换
     */
    public static final String BANK_CODE_REPLACE = "bank_code_replace";

    /**
     * 易宝结算申请备注替换
     */
    public static final String SETTLE_APPLY_REMARK_REPLACE = "settle_apply_remark_replace";

    /**
     * 积分单次最大兑换藏品数量
     */
    public static final String INTEGRAL_EXCHANGE_MAX_SIZE = "integral_exchange_max_size";

    /**
     * 积分排行榜展示数据条数
     */
    public static final String INTEGRAL_LIST_SIZE = "integral_list_size";

    /**
     * 赋能商城终极大奖解锁后用户冲刺时间
     */
    public static final String UNLOCK_FINISH_DAY = "unlock_finish_day";

    /**
     * 赋能商城天梯排行结束后用户元气值消耗时间
     */
    public static String CLOSE_FINISH_DAY = "close_finish_day";

    /**
     * 赋能商城元气值兑换最迟截止时间
     */
    public static String CLOSE_MAX_TIME = "close_max_time";

    /**
     * 元宇宙坑位售卖最大时间（小时）
     */
    public static String PIT_SELLER_TIME_OUT = "pit_seller_time_out";

    /**
     * 允许多少周岁的人可使用本平台
     */
    public static String ALLOW_USE_DETAIL_AGE = "allow_use_detail_age";


    /**
     * 元宇宙坑位售卖单人最大次数
     */
    public static String PIT_SELLER_MAX = "pit_seller_max";

    /**
     * 赋能商城终极大奖解锁规则
     */
    public static String RULE_PIC_IMG = "rule_pic_img";

    /**
     * 元宇宙坑位出兑藏品等级限制
     */
    public static String PIT_SELLER_COLLECTION_LEVEL = "pit_seller_collection_level";

    /**
     * 积分抵扣比例
     */
    public static String INTEGRAL_RATE = "integral_rate";


    /**
     * 权益商城出价须知
     */
    public static String DEGRESSION_PAY_NOTE = "degression_pay_note";

    /**
     * 权益商城首页出价须知
     */
    public static String DEGRESSION_HOME_PAY_NOTE = "degression_home_pay_note";

    /**
     * 权益商城暂无资格说明
     */
    public static String DEGRESSION_TEMPORARILY_INELIGIBLE_NOTE = "degression_temporarily_ineligible_note";

    /**
     * 权益商城降价拍支付截止时间（小时整数）
     */
    public static String DEGRESSION_AUCTION_TO_PAY_TIME = "degression_auction_to_pay_time";


    /**
     * 应用市场是否审核中后缀
     */
    public static String _IS_VERIFY = "_IS_VERIFY";

    /**
     * 抽奖活动报名须知
     */
    public static String LOTTERY_ACTIVITY_JOIN_NOTE = "lottery_activity_join_note";

    /**
     * 抽奖活动参与规则
     */
    public static String LOTTERY_ACTIVITY_RULE_PIC = "lottery_activity_rule_pic";

    /**
     * 多少可用余额时显示账户
     */
    public static String ACCOUNT_SHOW_AMOUNT = "account_show_amount";

    /**
     * 拉新活动盲盒赠送权重规则 0:按总数量,1:按剩余数量
     */
    public static String INVITATION_ACTIVITY_BLIND_BOX_BUY_RULE = "invitation_activity_blind_box_buy_rule";

    /**
     * 发行方发售计划单日条数
     */
    public static String TREASURE_PLAN_DAY_SIZE = "treasure_plan_day_size";

    /**
     * 首页上新标志显示多少天内有发布新作
     */
    public static String HOME_NEW_DAY_NUMBER = "home_new_day_number";

    /**
     * 发行方发布作品时至少要留给平台的作品数量
     */
    public static String GIVE_PLAT_MIN_NUMBER = "give_plat_min_number";

    /**
     * 发行方发布作品时至少要留给平台的作品数量比例
     */
    public static String GIVE_PLAT_MIN_RATE = "give_plat_min_rate";

    /**
     * 发行方发布盲盒期数时作品至少要留给平台的作品数量
     */
    public static String GIVE_PLAT_BLIND_MIN_NUMBER = "give_plat_blind_min_number";

    /**
     * 发行方发布盲盒期数时作品至少要留给平台的作品数量比例
     */
    public static String GIVE_PLAT_BLIND_MIN_RATE = "give_plat_blind_min_rate";

    /**
     * 发行方提交期数需要提早多少小时
     */
    public static String PUSH_PERIOD_NEED_EARLY_HOUR = "push_period_need_early_hour";

    /**
     * 协议阅读秒数
     */
    public static String AGREEMENT_DEFAULT_READ_SECONDS = "agreement_default_read_seconds";


    /**
     * 发行方登陆密码输入错误重置时间
     */
    public static String COMPANY_USER_LOGIN_ERROR_RESET_HOUR = "company_user_login_error_reset_hour";

    /**
     * 数字藏品分类
     */
    public static String COLLECTION_CATEGORY = "11";

    /**
     * 限制藏品转赠标志(0=不限制 1=限制)
     */
    public static String TRANSFER_LIMIT_COLLECTION_FLAG = "transfer_limit_collection_flag";

    /**
     * 文章默认typeId
     */
    public static Long ARTICLE_TYPE_ID_DEFAULT = new Long("387766973498662912");

    /**
     * BSN第一个标题
     */
    public static String CHAIN_CONFIG_BSN_FIRST_TITLE = "chain_config_bsn_first_title";

    /**
     * BSN第一个内容
     */
    public static String CHAIN_CONFIG_BSN_FIRST_CONTENT = "chain_config_bsn_first_content";

    /**
     * BSN第二个标题
     */
    public static String CHAIN_CONFIG_BSN_SECOND_TITLE = "chain_config_bsn_second_title";

    /**
     * BSN第二个内容
     */
    public static String CHAIN_CONFIG_BSN_SECOND_CONTENT = "chain_config_bsn_second_content";

    /**
     * BSN第三个标题
     */
    public static String CHAIN_CONFIG_BSN_THIRD_TITLE = "chain_config_bsn_third_title";

    /**
     * BSN第三个内容
     */
    public static String CHAIN_CONFIG_BSN_THIRD_CONTENT = "chain_config_bsn_third_content";

    /**
     * eth sit
     */
    public static String CHAIN_CONFIG_ETH_SIT = "chain_config_eth_sit";

    /**
     * eth opensea
     */
    public static String CHAIN_CONFIG_ETH_OPENSEA = "chain_config_eth_opensea";

    /**
     * 发行方所属用户数量上限
     */
    public static String COMPANY_USER_SIZE = "company_user_size";

    /**
     * XMeta系统授权协议
     */
    public static String XMETA_CHANNEL_AUTHORIZATION_TEXTAREA = "xmeta_channel_authorization_textarea";

    /**
     * 亨通系统授权协议
     */
    public static String HT_CHANNEL_AUTHORIZATION_TEXTAREA = "ht_channel_authorization_textarea";

    /**
     * 太亿数艺系统授权协议
     */
    public static String BUYOUT_CHANNEL_AUTHORIZATION_TEXTAREA = "buyout_channel_authorization_textarea";

    /**
     * 麦塔平台
     */
    public static String INVOICE_DEFAULT_NAME = "麦塔平台";

    /**
     * 发票查询的区间天数
     */
    public static String INVOICE_SEARCH_DAY = "invoice_search_day";

    /**
     * 申请成功后的提示
     */
    public static String INVOICE_APPLY_NOTE = "invoice_apply_note";

    /**
     * 藏品数量默认单位
     */
    public static String COLLECTION_QUANTITY_DEFAULT_UNIT = "个";

    /**
     * 机器人挖宝间隔时间
     */
    public static String ROBOT_UNIT_TIME_POSITIVEINTEGER = "robot_unit_time_positiveInteger";

    /**
     * 机器人最大派遣数量
     */
    public static String ROBOT_MAX_NUMBER_POSITIVEINTEGER = "robot_max_number_positiveInteger";

    /**
     * 机器人挖宝位置最大值
     */
    public static String ROBOT_LOCATION_MAX_POSITIVEINTEGER = "robot_location_max_positiveInteger";

    /**
     * 易宝分账最低比例
     */
    public static String YEEPAY_DIVIDE_MIN_RATE = "yeepay_divide_min_rate";

    /**
     * 访问人民币子账户url
     */
    public static final String CNY_ACCOUNT_URL = "cny_account_url";

    /**
     * 人民币子账户签名
     */
    public static final String ACCOUNT_SYSTEM_SIGN = "account_system_sign";

    /**
     * 人民币子账户登陆地址
     */
    public static final String CNY_ACCOUNT_H5_URL = "cny_account_h5_url";


    /**
     * 芯片活动h5访问地址
     */
    public static final String CHIP_ACTIVITY_FRONT_URL = "chip_activity_front_url";

    /**
     * 芯片活动单个ip最大领取次数
     */
    public static final String CHIP_ACTIVITY_MAX_RECEIVE_COUNT = "chip_activity_max_receive_count";

    /**
     * 期数发布后在线销售最大天数
     */
    public static final String COLLECTION_PERIOD_SELL_MAX_DAY = "collection_period_sell_max_day";

    /**
     * 钻石兑爻比例
     */
    public static final String DIAMOND_EXCHANGE_YAO_RATE = "diamond_exchange_yao_rate";

    /**
     * 钻石兑换爻规则说明
     */
    public static final String DIAMOND_EXCHANGE_YAO_RULE_NOTE = "diamond_exchange_yao_rule_note";

    /**
     * xmeta的导航id
     */
    public static final Long XMETA_CNAV = 500000000000000000L;

    /**
     * 亨通的导航id
     */
    public static final Long HT_CNAV = 600000000000000000L;

    /**
     * 盲盒期数最大展示数量
     */
    public static final String PERIOD_BLIND_SHOW_MAX_NUMBER = "period_blind_show_max_number";

    /**
     * 兑换XMeta流转藏品规则说明
     */
    public static final String XMETA_EXCHANGE_NOTE = "xmeta_exchange_note";

    /**
     * 马术比赛房间最大人数
     */
    public static final String HORSE_MATCH_MAX_NUMBER = "horse_match_max_number";

    /**
     * 阴爻数量
     */
    public static final String YAO_YIN_QUANTITY = "yao_yin_quantity";

    /**
     * 阳爻数量
     */
    public static final String YAO_YANG_QUANTITY = "yao_yang_quantity";

    /**
     * 安卓应用市场参数配置后缀
     */
    public static final String MARKET = "_market";

    /**
     * 安卓应用市场参数配置是否在审核中的标志(1是0否)
     */
    public static final String APPROVE_FLAG = "_approve_flag";

    /**
     * 安卓应用市场参数配置中的版本号
     */
    public static final String APPROVE_VERSION = "_approve_version";

    /**
     * 邀请排名文本说明
     */
    public static final String LXA_RANK_TEXTAREA = "lxa_rank_textarea";

//    /**
//     * 直播会议可提前预约天数
//     */
//    public static final String MEETING_RESERVE_DAYS = "meeting_reserve_days";

    /**
     * 直播会议开始前通知用户时间(分钟)
     */
    public static final String MEETING_PUSH_MINUTES = "meeting_push_minutes";

    /**
     * 直播会议可提前开播时间(分钟)
     */
    public static final String MEETING_START_EARLY_MINUTES = "meeting_start_early_minutes";

    /**
     * 直播会议开始前提前展示会议内容天数
     */
    public static final String MEETING_DISPLAY_EARLY_DAYS = "meeting_display_early_days";

    /**
     * 直播连麦确认倒计时
     */
    public static final String MEETING_HOSTING_CONFIRMATION_COUNTDOWN_CONFIG = "meeting_hosting_confirmation_countdown_config";

    /**
     * 发行方端开通会议直播联系客服
     */
    public static final String COMPANY_LIVE_CONTACT_PIC = "company_live_contact_pic";

    /**
     * 藏品导出说明
     */
    public static final String EXPORT_NOTE = "export_note";

    /**
     * 亨通藏品导出说明
     */
    public static final String HT_EXPORT_NOTE = "ht_export_note";
}

    
    