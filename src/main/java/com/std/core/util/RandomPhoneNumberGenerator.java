package com.std.core.util;

import java.util.Random;

public class RandomPhoneNumberGenerator {
    // 生成随机手机号的方法
    public static String generateRandomPhoneNumber() {
        Random rand = new Random();
        // 手机号码的前三位
        String[] prefix = {"130", "131", "132", "133", "134", "135", "136", "137", "138", "139",
                           "150", "151", "152", "153", "155", "156", "157", "158", "159",
                           "180", "181", "182", "183", "185", "186", "187", "188", "189"};
        // 随机选择一个前缀
        String phoneNumber = prefix[rand.nextInt(prefix.length)];
        // 生成后8位随机数字
        for (int i = 0; i < 8; i++) {
            phoneNumber += rand.nextInt(10);
        }
        return phoneNumber;
    }

    // 测试生成随机手机号的方法
    public static void main(String[] args) {
        String randomPhoneNumber = generateRandomPhoneNumber();
        System.out.println("随机手机号：" + randomPhoneNumber);
    }
}
