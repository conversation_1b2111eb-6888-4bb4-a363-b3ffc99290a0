package com.std.core.util;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.net.URI;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> xieyj
 * @since : 2020/8/28 16:07
 */
@Slf4j
public class HttpUtil {

    static OkHttpClient okHttpClient = new OkHttpClient();

    public static String postJson(String url, Map map) {
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpPost post = new HttpPost(url);
            post.setHeader("Content-Type", "application/json;");
            StringEntity entity1 = new StringEntity(JSON.toJSON(map).toString(), "utf-8");
            post.setEntity(entity1);

            CloseableHttpResponse res = httpClient.execute(post);

            int retCode = res.getStatusLine().getStatusCode();
            if (retCode != 200) {
                System.out.println("连接不成功！状态码 ：" + retCode);
                return "";
            }
            HttpEntity entity = res.getEntity();
            String resStr = entity != null ? EntityUtils.toString(entity, "utf-8") : null;
            return resStr;
        } catch (Exception e) {
            return null;
        }
    }

    public static String postByHead(String url, Map map, Map<String, String> headMap) {
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpPost post = new HttpPost(url);
            post.setHeader("Content-Type", "application/json;");
            if (headMap != null) {
                for (String key : headMap.keySet()) {
                    post.setHeader(key, headMap.get(key));
                }
            }
            if (map != null) {
                String toString = JSON.toJSON(map).toString();
                toString = toString.replace("[", "");
                toString = toString.replace("]", "");
                System.out.println(toString);
                StringEntity entity1 = new StringEntity(toString, "utf-8");
                post.setEntity(entity1);
            }
            CloseableHttpResponse res = httpClient.execute(post);

            int retCode = res.getStatusLine().getStatusCode();
            if (retCode != 200) {
                System.out.println("连接不成功！状态码 ：" + retCode);
                return "";
            }
            HttpEntity entity = res.getEntity();
            return entity != null ? EntityUtils.toString(entity, "utf-8") : null;
        } catch (Exception e) {
            return null;
        }
    }

    public static String getMethod(String urll, HttpClientContext context) throws Exception {
        CloseableHttpClient loginHttpClient = HttpClients.createDefault();
        URL url = new URL(urll);
        URI uri = new URI(url.getProtocol(), null, url.getHost(), url.getPort(), url.getPath(), url.getQuery(), null);
        HttpGet httpGet = new HttpGet(uri);
        CloseableHttpResponse res = null;
        if (context == null) {
            res = loginHttpClient.execute(httpGet);
        } else {
            res = loginHttpClient.execute(httpGet, context);
        }
        HttpEntity entity = res.getEntity();
        return entity != null ? EntityUtils.toString(entity, "UTF-8") : null;
    }


    public static String execute(Request executeRequest) {
        Response response = null;
        String str = null;
        try {
            response = okHttpClient.newCall(executeRequest).execute();
            str = response.body().string();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return str;
    }


    /**
     * 　　* 把数组所有元素排序，并按照“参数=参数值”的模式用“&”字符拼接成字符串
     * 　　* @param params 需要排序并参与字符拼接的参数组
     * 　　* @return 拼接后字符串
     * 　　* @throws UnsupportedEncodingException
     */
    public static String createLinkStringByGet(Map<String, String> params) {
        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);
        StringBuilder prestr = new StringBuilder();
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            String value = params.get(key);
            //value = URLEncoder.encode(value, "UTF-8");
            if (i == keys.size() - 1) {//拼接时，不包括最后一个&字符
                prestr.append(key).append("=").append(value);
            } else {
                prestr.append(key).append("=").append(value).append("&");
            }
        }
        return prestr.toString();
    }

    /**
     * http post
     *
     * @param url
     * @param parameters
     * @return response
     * @throws IOException
     */
    public static String postForm(String url, Map parameters)  {

        HttpPost httpPost = new HttpPost(url);
        httpPost.setHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36");
        List<NameValuePair> nvps = toNameValuePairList(parameters);
        CloseableHttpResponse response = null;

        try {
            httpPost.setEntity(new UrlEncodedFormEntity(nvps, "UTF-8"));
            CloseableHttpClient httpClient = HttpClients.createDefault();
            response = httpClient.execute(httpPost);
            int retCode = response.getStatusLine().getStatusCode();
            if (retCode != 200) {
                System.out.println("连接不成功！状态码 ：" + retCode);
                return "";
            }
            HttpEntity entity = response.getEntity();
            return entity != null ? EntityUtils.toString(entity, "utf-8") : null;
        } catch (Exception e) {
            log.error("请求出错:", e);
            return "";
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }


    @SuppressWarnings("unused")
    private static List<NameValuePair> toNameValuePairList(Map<String,Object> parameters) {
        List<NameValuePair> nvps = new ArrayList<NameValuePair>();

        parameters.forEach((k,v)->{
            nvps.add(new BasicNameValuePair(k, v.toString()));
        });
        return nvps;
    }
}
