package com.std.core.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * ZIP文件处理工具类
 *
 * <AUTHOR> system
 * @since : 2024-07-30
 */
@Slf4j
public class ZipFileUtil {

    /**
     * ZIP文件中的图片信息
     */
    public static class ZipImageInfo {
        private String fileName;
        private String relativePath;
        private byte[] data;
        private long size;

        public ZipImageInfo(String fileName, String relativePath, byte[] data, long size) {
            this.fileName = fileName;
            this.relativePath = relativePath;
            this.data = data;
            this.size = size;
        }

        // Getters
        public String getFileName() { return fileName; }
        public String getRelativePath() { return relativePath; }
        public byte[] getData() { return data; }
        public long getSize() { return size; }
    }

    /**
     * 解压ZIP文件并提取图片
     *
     * @param zipFile ZIP文件
     * @param supportedFormats 支持的图片格式
     * @param maxFileSize 单个文件最大大小（字节）
     * @param maxTotalFiles 最大文件数量
     * @return 图片信息列表
     */
    public static List<ZipImageInfo> extractImagesFromZip(MultipartFile zipFile, 
                                                         Set<String> supportedFormats,
                                                         long maxFileSize,
                                                         int maxTotalFiles) throws IOException {
        
        List<ZipImageInfo> imageList = new ArrayList<>();
        
        try (ZipInputStream zipInputStream = new ZipInputStream(zipFile.getInputStream())) {
            ZipEntry entry;
            int fileCount = 0;
            
            while ((entry = zipInputStream.getNextEntry()) != null) {
                // 检查文件数量限制
                if (fileCount >= maxTotalFiles) {
                    log.warn("ZIP文件中的文件数量超过限制: {}", maxTotalFiles);
                    break;
                }
                
                // 跳过目录
                if (entry.isDirectory()) {
                    continue;
                }
                
                String fileName = entry.getName();
                
                // 检查是否为图片文件
                if (!isImageFile(fileName, supportedFormats)) {
                    log.debug("跳过非图片文件: {}", fileName);
                    continue;
                }
                
                // 检查文件大小
                if (entry.getSize() > maxFileSize) {
                    log.warn("文件大小超过限制，跳过: {} ({}字节)", fileName, entry.getSize());
                    continue;
                }
                
                // 读取文件数据
                byte[] fileData = readZipEntryData(zipInputStream, maxFileSize);
                
                if (fileData != null && fileData.length > 0) {
                    // 提取文件名（去除路径）
                    String simpleFileName = getSimpleFileName(fileName);
                    String relativePath = getRelativePath(fileName);
                    
                    ZipImageInfo imageInfo = new ZipImageInfo(simpleFileName, relativePath, fileData, fileData.length);
                    imageList.add(imageInfo);
                    fileCount++;
                    
                    log.debug("提取图片文件: {} ({}字节)", fileName, fileData.length);
                }
                
                zipInputStream.closeEntry();
            }
        }
        
        log.info("从ZIP文件中提取了 {} 个图片文件", imageList.size());
        return imageList;
    }

    /**
     * 检查是否为图片文件
     */
    private static boolean isImageFile(String fileName, Set<String> supportedFormats) {
        if (fileName == null || fileName.isEmpty()) {
            return false;
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return false;
        }
        
        String extension = fileName.substring(lastDotIndex + 1).toLowerCase();
        return supportedFormats.contains(extension);
    }

    /**
     * 读取ZIP条目数据
     */
    private static byte[] readZipEntryData(ZipInputStream zipInputStream, long maxFileSize) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] buffer = new byte[8192];
        int bytesRead;
        long totalBytesRead = 0;
        
        while ((bytesRead = zipInputStream.read(buffer)) != -1) {
            totalBytesRead += bytesRead;
            
            // 检查文件大小限制
            if (totalBytesRead > maxFileSize) {
                log.warn("文件大小超过限制，停止读取");
                return null;
            }
            
            baos.write(buffer, 0, bytesRead);
        }
        
        return baos.toByteArray();
    }

    /**
     * 获取简单文件名（去除路径）
     */
    private static String getSimpleFileName(String fullPath) {
        if (fullPath == null) {
            return null;
        }
        
        // 处理不同操作系统的路径分隔符
        String normalizedPath = fullPath.replace('\\', '/');
        int lastSlashIndex = normalizedPath.lastIndexOf('/');
        
        if (lastSlashIndex >= 0) {
            return normalizedPath.substring(lastSlashIndex + 1);
        }
        
        return fullPath;
    }

    /**
     * 获取相对路径（保留目录结构）
     */
    private static String getRelativePath(String fullPath) {
        if (fullPath == null) {
            return "";
        }
        
        // 处理不同操作系统的路径分隔符
        String normalizedPath = fullPath.replace('\\', '/');
        int lastSlashIndex = normalizedPath.lastIndexOf('/');
        
        if (lastSlashIndex >= 0) {
            return normalizedPath.substring(0, lastSlashIndex);
        }
        
        return "";
    }

    /**
     * 解析支持的文件格式
     */
    public static Set<String> parseSupportedFormats(String supportedFormats) {
        Set<String> formats = new HashSet<>();
        
        if (supportedFormats != null && !supportedFormats.trim().isEmpty()) {
            String[] formatArray = supportedFormats.toLowerCase().split(",");
            for (String format : formatArray) {
                String trimmedFormat = format.trim();
                if (!trimmedFormat.isEmpty()) {
                    formats.add(trimmedFormat);
                }
            }
        }
        
        // 如果没有指定格式，使用默认格式
        if (formats.isEmpty()) {
            formats.addAll(Arrays.asList("jpg", "jpeg", "png", "gif", "bmp", "webp"));
        }
        
        return formats;
    }

    /**
     * 验证ZIP文件
     */
    public static boolean isValidZipFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return false;
        }
        
        // 检查文件扩展名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !originalFilename.toLowerCase().endsWith(".zip")) {
            return false;
        }
        
        // 检查MIME类型
        String contentType = file.getContentType();
        if (contentType != null && 
            !contentType.equals("application/zip") && 
            !contentType.equals("application/x-zip-compressed")) {
            log.warn("ZIP文件MIME类型不匹配: {}", contentType);
        }
        
        // 尝试读取ZIP文件头
        try (ZipInputStream zipInputStream = new ZipInputStream(file.getInputStream())) {
            ZipEntry entry = zipInputStream.getNextEntry();
            return entry != null; // 如果能读取到至少一个条目，说明是有效的ZIP文件
        } catch (IOException e) {
            log.error("验证ZIP文件时发生错误", e);
            return false;
        }
    }
}
