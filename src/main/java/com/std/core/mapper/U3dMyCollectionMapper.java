package com.std.core.mapper;

import com.std.core.pojo.domain.U3dMyCollection;
import com.std.core.pojo.response.U3dMyCollectionListRes;
import java.util.List;

/**
 * u3d我的作品位置Mapper
 *
 * <AUTHOR> ycj
 * @since : 2022-03-31 16:33
 */
public interface U3dMyCollectionMapper {

    /**
     * 选择性插入
     *
     * @param record u3d我的作品位置
     * @return 影响行数
     */
    int insertSelective(U3dMyCollection record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record u3d我的作品位置
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(U3dMyCollection record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return u3d我的作品位置
     */
    U3dMyCollection selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return u3d我的作品位置列表
     */
    List<U3dMyCollection> selectByCondition(U3dMyCollection condition);

    List<U3dMyCollectionListRes> selectByConditionFront(U3dMyCollection condition);

    void deleteByCollectionDetailId(Long collectionDetailId);

}
