package com.std.core.mapper;

import com.std.core.pojo.domain.RobotCollectionDetailConfig;
import java.util.List;

/**
 * 机器人挖宝藏品配置Mapper
 *
 * <AUTHOR> wzh
 * @since : 2023-05-25 10:55
 */
public interface RobotCollectionDetailConfigMapper {

    /**
     * 选择性插入
     *
     * @param record 机器人挖宝藏品配置
     * @return 影响行数
     */
    int insertSelective(RobotCollectionDetailConfig record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 机器人挖宝藏品配置
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(RobotCollectionDetailConfig record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 机器人挖宝藏品配置
     */
    RobotCollectionDetailConfig selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 机器人挖宝藏品配置列表
     */
    List<RobotCollectionDetailConfig> selectByCondition(RobotCollectionDetailConfig condition);

    void batchInsert(List<RobotCollectionDetailConfig> list);
}
