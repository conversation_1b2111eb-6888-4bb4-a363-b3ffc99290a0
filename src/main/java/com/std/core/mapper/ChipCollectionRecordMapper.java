package com.std.core.mapper;

import com.std.core.pojo.domain.ChipCollectionRecord;
import java.util.List;

/**
 * 芯片领藏品活动记录Mapper
 *
 * <AUTHOR> ycj
 * @since : 2022-08-24 14:23
 */
public interface ChipCollectionRecordMapper {

    /**
     * 选择性插入
     *
     * @param record 芯片领藏品活动记录
     * @return 影响行数
     */
    int insertSelective(ChipCollectionRecord record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 芯片领藏品活动记录
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(ChipCollectionRecord record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 芯片领藏品活动记录
     */
    ChipCollectionRecord selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 芯片领藏品活动记录列表
     */
    List<ChipCollectionRecord> selectByCondition(ChipCollectionRecord condition);

    List<ChipCollectionRecord> selectToDistribution();

    ChipCollectionRecord selectForUpdate(Long id);

    int selectByConditionCount(ChipCollectionRecord condition);
}
