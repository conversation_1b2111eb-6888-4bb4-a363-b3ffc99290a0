package com.std.core.mapper;

import com.std.core.pojo.domain.Express;
import java.util.List;

/**
 * 快递公司Mapper
 *
 * <AUTHOR> xieyj
 * @since : 2021-08-11 13:54
 */
public interface ExpressMapper {

    /**
     * 选择性插入
     *
     * @param record 快递公司
     * @return 影响行数
     */
    int insertSelective(Express record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 快递公司
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(Express record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 快递公司
     */
    Express selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 快递公司列表
     */
    List<Express> selectByCondition(Express condition);

}
