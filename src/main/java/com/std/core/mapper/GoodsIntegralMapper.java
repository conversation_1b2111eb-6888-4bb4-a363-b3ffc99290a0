package com.std.core.mapper;

import com.std.core.pojo.domain.GoodsIntegral;
import com.std.core.pojo.response.GoodsIntegralPageRes;

import java.math.BigDecimal;
import java.util.List;

/**
 * 积分商品Mapper
 *
 * <AUTHOR> ycj
 * @since : 2022-04-25 22:51
 */
public interface GoodsIntegralMapper {

    /**
     * 选择性插入
     *
     * @param record 积分商品
     * @return 影响行数
     */
    int insertSelective(GoodsIntegral record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 积分商品
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(GoodsIntegral record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 积分商品
     */
    GoodsIntegral selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 积分商品列表
     */
    List<GoodsIntegral> selectByCondition(GoodsIntegral condition);

    List<GoodsIntegralPageRes> selectByConditionFront(GoodsIntegral condition);

    int updateGoodsRemainQuantity(Long id, Integer quantity);

    void updateIntegralGoodsEnd();

    GoodsIntegral selectForUpdate(Long id);

    int updateRemainQuantity(Long id, int quantity);
    int updateRemainQuantityAdd(Long id, int quantity);

    int updateTotalQuantityAdd(Long id, int quantity);

    BigDecimal getMaxUnlockCondition();
}
