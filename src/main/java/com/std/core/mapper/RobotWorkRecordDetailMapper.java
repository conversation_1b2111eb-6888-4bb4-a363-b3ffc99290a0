package com.std.core.mapper;

import com.std.core.pojo.domain.RobotWorkRecordDetail;
import com.std.core.pojo.response.RobotWorkRecordDetailListRes;
import com.std.core.pojo.response.RobotWorkRecordDetailOssPageRes;

import java.util.Date;
import java.util.List;

/**
 * 机器人挖宝明细Mapper
 *
 * <AUTHOR> ycj
 * @since : 2022-08-29 21:21
 */
public interface RobotWorkRecordDetailMapper {

    /**
     * 选择性插入
     *
     * @param record 机器人挖宝明细
     * @return 影响行数
     */
    int insertSelective(RobotWorkRecordDetail record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 机器人挖宝明细
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(RobotWorkRecordDetail record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 机器人挖宝明细
     */
    RobotWorkRecordDetail selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 机器人挖宝明细列表
     */
    List<RobotWorkRecordDetail> selectByCondition(RobotWorkRecordDetail condition);

    void insertBatchSelective(List<RobotWorkRecordDetail> list);

    List<RobotWorkRecordDetailOssPageRes> selectByConditionOss(RobotWorkRecordDetail condition);

    List<Integer> selectLocation(Long operatorId);

    List<RobotWorkRecordDetailListRes> selectRobotLocaltion(Long operatorId, Date date);

    List<RobotWorkRecordDetail> selectListEndRecord(Date date);

    List<RobotWorkRecordDetail> selectRobotCollection(Long ownerId, Long robotCollectionId);
}
