package com.std.core.mapper;

import com.std.core.pojo.domain.UserChainAddress;
import java.util.List;

/**
 * 用户链上地址Mapper
 *
 * <AUTHOR> ycj
 * @since : 2023-05-04 15:18
 */
public interface UserChainAddressMapper {

    /**
     * 选择性插入
     *
     * @param record 用户链上地址
     * @return 影响行数
     */
    int insertSelective(UserChainAddress record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 用户链上地址
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(UserChainAddress record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 用户链上地址
     */
    UserChainAddress selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 用户链上地址列表
     */
    List<UserChainAddress> selectByCondition(UserChainAddress condition);

}
