package com.std.core.mapper;

import com.std.core.pojo.domain.YaoMilletConfigRecord;

import java.math.BigDecimal;
import java.util.List;

/**
 * 元粟每日配置记录Mapper
 *
 * <AUTHOR> ycj
 * @since : 2022-11-14 15:08
 */
public interface YaoMilletConfigRecordMapper {

    /**
     * 选择性插入
     *
     * @param record 元粟每日配置记录
     * @return 影响行数
     */
    int insertSelective(YaoMilletConfigRecord record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 元粟每日配置记录
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(YaoMilletConfigRecord record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 元粟每日配置记录
     */
    YaoMilletConfigRecord selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 元粟每日配置记录列表
     */
    List<YaoMilletConfigRecord> selectByCondition(YaoMilletConfigRecord condition);

    YaoMilletConfigRecord selectByDateNumberForUpdate(int dateNumber, String currency);

    int updateSubtractQuantity(Long id, BigDecimal quantity);

    YaoMilletConfigRecord selectByDateNumber(int dateNumber, String currency);
}
