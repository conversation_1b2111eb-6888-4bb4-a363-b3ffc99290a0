package com.std.core.mapper;

import com.std.core.pojo.domain.CurrencyRate;
import java.util.List;

/**
 * 法币汇率Mapper
 *
 * <AUTHOR> xieyj
 * @since : 2021-02-03 20:38
 */
public interface CurrencyRateMapper {

    /**
     * 选择性插入
     *
     * @param record 法币汇率
     * @return 影响行数
     */
    int insertSelective(CurrencyRate record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 选择性更新
     *
     * @param record 法币汇率
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(CurrencyRate record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 法币汇率
     */
    CurrencyRate selectByPrimaryKey(Integer id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 法币汇率列表
     */
    List<CurrencyRate> selectByCondition(CurrencyRate condition);

}
