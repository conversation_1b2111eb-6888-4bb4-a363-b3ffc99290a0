package com.std.core.mapper;

import com.std.core.pojo.domain.OperatorLog;
import java.util.List;

/**
 * 操作日志Mapper
 *
 * <AUTHOR> ycj
 * @since : 2022-02-20 18:26
 */
public interface OperatorLogMapper {

    /**
     * 选择性插入
     *
     * @param record 操作日志
     * @return 影响行数
     */
    int insertSelective(OperatorLog record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 操作日志
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(OperatorLog record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 操作日志
     */
    OperatorLog selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 操作日志列表
     */
    List<OperatorLog> selectByCondition(OperatorLog condition);

    /**
     * 查询用户访问数量
     */
    Integer selectUserCount(OperatorLog condition);

}
