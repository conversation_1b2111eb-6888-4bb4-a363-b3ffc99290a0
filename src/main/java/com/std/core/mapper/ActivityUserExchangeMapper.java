package com.std.core.mapper;

import com.std.core.pojo.domain.ActivityUserExchange;
import com.std.core.pojo.response.ActivitySumListRes;

import java.util.List;

/**
 * 活动用户兑换记录Mapper
 *
 * <AUTHOR> ycj
 * @since : 2022-01-22 15:40
 */
public interface ActivityUserExchangeMapper {

    /**
     * 选择性插入
     *
     * @param record 活动用户兑换记录
     * @return 影响行数
     */
    int insertSelective(ActivityUserExchange record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 活动用户兑换记录
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(ActivityUserExchange record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 活动用户兑换记录
     */
    ActivityUserExchange selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 活动用户兑换记录列表
     */
    List<ActivityUserExchange> selectByCondition(ActivityUserExchange condition);

    List<ActivitySumListRes> selectActivitySum(Long id);
    List<ActivitySumListRes> selectActivitySumEnd(Long activityId);

    List<Long> selectUser(Long activityId);

    Integer selectOneSelfNumber(ActivityUserExchange activityUserExchange);

    Integer selectExchangeNumber(ActivityUserExchange activityUserExchange);

}
