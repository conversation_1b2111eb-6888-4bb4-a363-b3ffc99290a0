package com.std.core.controller;

import com.std.common.base.Result;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.PfpImageBatchUploadReq;
import com.std.core.pojo.request.PfpImageUrlBatchUploadReq;
import com.std.core.pojo.request.PfpOssZipUploadReq;
import com.std.core.pojo.request.PfpZipUploadReq;
import com.std.core.pojo.response.OssUploadTokenRes;
import com.std.core.pojo.response.PfpImageUploadRes;
import com.std.core.service.ICollectionPfpPicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * PFP图片批量上传Controller
 *
 * <AUTHOR> system
 * @since : 2024-07-30
 */
@ApiVersion(1)
@RestController
@Api(value = "PFP图片批量上传", tags = "PFP图片批量上传")
@RequestMapping(value = "{version}/pfp/image")
@Slf4j
public class PfpImageUploadController extends BaseController {

    @Resource
    private ICollectionPfpPicService collectionPfpPicService;

    @ApiOperation(value = "批量上传PFP图片文件")
    @ApiOperationSupport(order = 1)
    @PostMapping("/batch_upload_files")
    public Result<PfpImageUploadRes> batchUploadFiles(
            @RequestHeader(value = "Authorization") String token,
            @RequestParam("periodId") Long periodId,
            @RequestParam("imageFiles") List<MultipartFile> imageFiles,
            @RequestParam(value = "namingRule", defaultValue = "AUTO") String namingRule,
            @RequestParam(value = "startNumber", defaultValue = "1") Integer startNumber,
            @RequestParam(value = "imagePrefix", defaultValue = "pfp") String imagePrefix) {

        User operator = getUserByToken(token);
        
        // 构建请求对象
        PfpImageBatchUploadReq request = new PfpImageBatchUploadReq();
        request.setPeriodId(periodId);
        request.setImageFiles(imageFiles);
        request.setNamingRule(namingRule);
        request.setStartNumber(startNumber);
        request.setImagePrefix(imagePrefix);

        log.info("批量上传PFP图片文件，期数ID: {}, 文件数量: {}, 操作人: {}", 
                periodId, imageFiles.size(), operator.getId());

        PfpImageUploadRes response = collectionPfpPicService.batchUploadImages(request, operator);

        log.info("批量上传PFP图片文件完成，期数ID: {}, 成功: {}, 失败: {}", 
                periodId, response.getSuccessCount(), response.getFailCount());

        return new Result<>(response);
    }

    @ApiOperation(value = "批量从URL上传PFP图片")
    @ApiOperationSupport(order = 2)
    @PostMapping("/batch_upload_urls")
    public Result<PfpImageUploadRes> batchUploadFromUrls(@RequestHeader(value = "Authorization") String token, @RequestBody PfpImageUrlBatchUploadReq request) {

        User operator = getUserByToken(token);

        log.info("批量从URL上传PFP图片，期数ID: {}, URL数量: {}, 操作人: {}", 
                request.getPeriodId(), request.getImageUrls().size(), operator.getId());

        PfpImageUploadRes response = collectionPfpPicService.batchUploadFromUrls(request, operator);

        log.info("批量从URL上传PFP图片完成，期数ID: {}, 成功: {}, 失败: {}", 
                request.getPeriodId(), response.getSuccessCount(), response.getFailCount());

        return new Result<>(response);
    }

    @ApiOperation(value = "从ZIP文件批量上传PFP图片")
    @ApiOperationSupport(order = 3)
    @PostMapping("/batch_upload_zip")
    public Result<PfpImageUploadRes> batchUploadFromZip(
            @RequestHeader(value = "Authorization") String token,
            @RequestParam("periodId") Long periodId,
            @RequestParam("zipFile") MultipartFile zipFile,
            @RequestParam(value = "namingRule", defaultValue = "AUTO") String namingRule,
            @RequestParam(value = "startNumber", defaultValue = "1") Integer startNumber,
            @RequestParam(value = "imagePrefix", defaultValue = "pfp") String imagePrefix,
            @RequestParam(value = "keepDirectoryStructure", defaultValue = "false") Boolean keepDirectoryStructure,
            @RequestParam(value = "supportedFormats", defaultValue = "jpg,jpeg,png,gif,bmp,webp") String supportedFormats) {

        User operator = getUserByToken(token);

        // 构建请求对象
        PfpZipUploadReq request = new PfpZipUploadReq();
        request.setPeriodId(periodId);
        request.setZipFile(zipFile);
        request.setNamingRule(namingRule);
        request.setStartNumber(startNumber);
        request.setImagePrefix(imagePrefix);
        request.setKeepDirectoryStructure(keepDirectoryStructure);
        request.setSupportedFormats(supportedFormats);

        log.info("从ZIP文件批量上传PFP图片，期数ID: {}, ZIP文件: {}, 操作人: {}",
                periodId, zipFile.getOriginalFilename(), operator.getId());

        PfpImageUploadRes response = collectionPfpPicService.batchUploadFromZip(request, operator);

        log.info("ZIP文件批量上传完成，期数ID: {}, 成功: {}, 失败: {}",
                periodId, response.getSuccessCount(), response.getFailCount());

        return new Result<>(response);
    }

    @ApiOperation(value = "获取OSS上传Token")
    @ApiOperationSupport(order = 4)
    @PostMapping("/get_oss_token")
    public Result getOssToken() {
        // 这里可以返回OSS的STS临时凭证，供前端直接上传使用
        // 具体实现可以调用CommonService的getAliOssToken方法
        return new Result<>("OSS Token获取功能待实现");
    }

}
