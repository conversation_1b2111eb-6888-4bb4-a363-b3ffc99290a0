package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.PeriodAuctionRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.PeriodAuctionRecordCreateReq;
import com.std.core.pojo.request.PeriodAuctionRecordListReq;
import com.std.core.pojo.request.PeriodAuctionRecordListFrontReq;
import com.std.core.pojo.request.PeriodAuctionRecordModifyReq;
import com.std.core.pojo.request.PeriodAuctionRecordPageReq;
import com.std.core.pojo.request.PeriodAuctionRecordPageFrontReq;
import com.std.core.pojo.response.PeriodAuctionMyRecordRes;
import com.std.core.pojo.response.PeriodAuctionRecordDetailRes;
import com.std.core.pojo.response.PeriodAuctionRecordListRes;
import com.std.core.pojo.response.PeriodAuctionRecordPageRes;
import com.std.core.service.IPeriodAuctionRecordService;
import com.std.core.util.RedisLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;

import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 期数竞拍出价记录Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-03-29 21:43
 */
@ApiVersion(1)
@RestController
@Api(value = "期数竞拍出价记录管理", tags = "期数竞拍出价记录管理")
@RequestMapping("{version}/period_auction_record")
public class PeriodAuctionRecordController extends BaseController {

    @Resource
    private IPeriodAuctionRecordService periodAuctionRecordService;
    @Resource
    private RedisLock redisLock;

    @Value("${meta.lock.timeout}")
    private Long metaLockTimeout;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增期数竞拍出价记录', NULL, '/core/v1/period_auction_record/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "出价")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodAuctionRecordCreateReq request) {

        User operator = getUserByToken(token, EUserKind.C);

        String lockId = "period_auction_record_create:" + request.getPeriodId();
        Long time = System.currentTimeMillis() + metaLockTimeout;
        try {
            if (!redisLock.lock(lockId, String.valueOf(time))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "其他用户出价中");
            }
            periodAuctionRecordService.create(request, operator);
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }

        return new Result();
    }

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除期数竞拍出价记录', NULL, '/core/v1/period_auction_record/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除期数竞拍出价记录")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        periodAuctionRecordService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改期数竞拍出价记录', NULL, '/core/v1/period_auction_record/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改期数竞拍出价记录")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodAuctionRecordModifyReq request) {
//        User operator = getUserByToken(token);
//        periodAuctionRecordService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询期数竞拍出价记录', NULL, '/core/v1/period_auction_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询期数竞拍出价记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<PeriodAuctionRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(periodAuctionRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询期数竞拍出价记录', NULL, '/core/v1/period_auction_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询期数竞拍出价记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<PeriodAuctionRecord>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodAuctionRecordPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), PeriodAuctionRecord.class));

        return PageUtil.pageResult(periodAuctionRecordService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询期数竞拍出价记录', NULL, '/core/v1/period_auction_record/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询期数竞拍出价记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<PeriodAuctionRecord>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodAuctionRecordListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(periodAuctionRecordService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询期数竞拍出价记录', NULL, '/core/v1/period_auction_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询期数竞拍出价记录")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<PeriodAuctionRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(periodAuctionRecordService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询期数竞拍出价记录', NULL, '/core/v1/period_auction_record/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "全部出价记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/public/page_front")
    public Result<PageInfo<PeriodAuctionRecordPageRes>> pageFront(@RequestHeader(value = "Authorization", required = false) String token, @RequestBody @Valid PeriodAuctionRecordPageFrontReq request) {
//        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(periodAuctionRecordService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询期数竞拍出价记录', NULL, '/core/v1/period_auction_record/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "详情页列表")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/public/list_front")
    public Result<List<PeriodAuctionRecordListRes>> listFront(@RequestHeader(value = "Authorization", required = false) String token, @RequestBody @Valid PeriodAuctionRecordListFrontReq request) {

        return new Result<>(periodAuctionRecordService.listFront(request));
    }

    @ApiOperation(value = "查询我的出价")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/my_list_front")
    public Result<PeriodAuctionMyRecordRes> myListFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodAuctionRecordListFrontReq request) {
        User operator = getUserByToken(token, EUserKind.C);

        return new Result<>(periodAuctionRecordService.myListFront(request,operator));
    }

}