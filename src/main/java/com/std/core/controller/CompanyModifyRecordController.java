package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.CompanyModifyRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.CompanyModifyRecordCreateReq;
import com.std.core.pojo.request.CompanyModifyRecordListReq;
import com.std.core.pojo.request.CompanyModifyRecordListFrontReq;
import com.std.core.pojo.request.CompanyModifyRecordModifyReq;
import com.std.core.pojo.request.CompanyModifyRecordPageReq;
import com.std.core.pojo.request.CompanyModifyRecordPageFrontReq;
import com.std.core.pojo.response.CompanyModifyRecordDetailRes;
import com.std.core.pojo.response.CompanyModifyRecordListRes;
import com.std.core.pojo.response.CompanyModifyRecordPageRes;
import com.std.core.service.ICompanyModifyRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 发行方信息修改记录Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-07-01 13:56
 */
@ApiVersion(1)
@RestController
@Api(value = "发行方信息修改记录管理", tags = "发行方信息修改记录管理")
@RequestMapping("{version}/company_modify_record")
public class CompanyModifyRecordController extends BaseController {

    @Resource
    private ICompanyModifyRecordService companyModifyRecordService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增发行方信息修改记录', NULL, '/core/v1/company_modify_record/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增发行方信息修改记录")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CompanyModifyRecordCreateReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        companyModifyRecordService.create(request, operator);

        return new Result();
    }

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除发行方信息修改记录', NULL, '/core/v1/company_modify_record/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除发行方信息修改记录")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        companyModifyRecordService.remove(id);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改发行方信息修改记录', NULL, '/core/v1/company_modify_record/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "审核修改")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CompanyModifyRecordModifyReq request) {
        User operator = getUserByToken(token,EUserKind.SYS);
        companyModifyRecordService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询发行方信息修改记录', NULL, '/core/v1/company_modify_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询发行方信息修改记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<CompanyModifyRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(companyModifyRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询发行方信息修改记录', NULL, '/core/v1/company_modify_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询发行方信息修改记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<CompanyModifyRecord>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CompanyModifyRecordPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(companyModifyRecordService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询发行方信息修改记录', NULL, '/core/v1/company_modify_record/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询发行方信息修改记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<CompanyModifyRecord>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CompanyModifyRecordListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(companyModifyRecordService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询发行方信息修改记录', NULL, '/core/v1/company_modify_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询发行方信息修改记录")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<CompanyModifyRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(companyModifyRecordService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询发行方信息修改记录', NULL, '/core/v1/company_modify_record/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询发行方信息修改记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<CompanyModifyRecordPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CompanyModifyRecordPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), CompanyModifyRecord.class));

        return PageUtil.pageResult(companyModifyRecordService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询发行方信息修改记录', NULL, '/core/v1/company_modify_record/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询发行方信息修改记录")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<CompanyModifyRecordListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CompanyModifyRecordListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(companyModifyRecordService.listFront(request));
    }

}