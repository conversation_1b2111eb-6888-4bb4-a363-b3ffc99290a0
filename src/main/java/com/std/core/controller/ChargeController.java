package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.Charge;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ChargeAppleReq;
import com.std.core.pojo.request.ChargeApproveReq;
import com.std.core.pojo.request.ChargeListReq;
import com.std.core.pojo.request.ChargeOfflineReq;
import com.std.core.pojo.request.ChargeOnlineAppReq;
import com.std.core.pojo.request.ChargePageReq;
import com.std.core.pojo.request.ChargeToBankcardReq;
import com.std.core.service.IChargeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 充值订单Controller
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-26 10:01
 */
@ApiVersion(1)
@RestController
@Api(value = "充值订单管理", tags = "12、充值订单管理", position = 13)
@RequestMapping("{version}/charge")
public class ChargeController extends BaseController {

    @Resource
    private IChargeService chargeService;

    @ApiOperation(value = "front:app线上充值", position = 13)
    @PostMapping(value = "/charge_online_app")
    public Result<Object> chargeOnlineApp(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ChargeOnlineAppReq request) {
        User operator = getUserByToken(token);
        return new Result<>(chargeService.create(request, operator));
    }

    @ApiOperation(value = "线下充值", position = 10)
    @PostMapping(value = "/charge_offline")
    public Result chargeOffline(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChargeOfflineReq request) {
        User operator = getUserByToken(token);
        chargeService.create(request, operator);
        return new Result();
    }

    @ApiOperation(value = "苹果充值", position = 10)
    @PostMapping(value = "/apple_charge")
    public Result appleCharge(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChargeAppleReq request) {
//        User operator = getUserByToken(token, EUserKind.C);
//        String chargeLoginName = configService.getStringValue(SysConstants.CHARGE_LOGINNAME);
//        if (StringUtils.isBlank(chargeLoginName)) {
//            throw new BizException(EBizErrorCode.UNDONE.getCode(), "该功能暂不支持");
//        } else {
//            if (operator.getLoginName().equals(chargeLoginName)) {
//                AccountManualChangeReq accountManualChangeReq = new AccountManualChangeReq();
//                accountManualChangeReq.setUserId(operator.getId());
//                accountManualChangeReq.setCurrency(ECurrency.CNY.getCode());
//                //加钱
//                accountManualChangeReq.setType(EBoolean.YES.getCode());
//                accountManualChangeReq.setAmount(request.getAmount());
//
//                accountService.changeAccount(operator, accountManualChangeReq, null, AssetBiz.AppleChange);
//            } else {
//                throw new BizException(EBizErrorCode.UNDONE.getCode(), "该功能暂不支持");
//            }
//        }

        return new Result();
    }

    @ApiOperation(value = "充值到银行卡", position = 11)
    @PostMapping(value = "/charge_to_bankcard")
    public Result chargeToBankcard(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ChargeToBankcardReq request) {
        User operator = getUserByToken(token);
        chargeService.create(request, operator);
        return new Result();
    }

    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "线下充值审核", position = 12)
    @PostMapping(value = "/charge_approve")
    public Result chargeApprove(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChargeApproveReq request) {
        User operator = getUserByToken(token);
        chargeService.approve(request, operator);
        return new Result();
    }

    @ApiOperation(value = "查询充值订单对象", position = 40)
    @PostMapping("/detail/{id}")
    public Result<Charge> detail(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(chargeService.detail(id));
    }

    @ApiOperation(value = "分页条件查询充值订单", position = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<Charge>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ChargePageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), Charge.class));

        return PageUtil.pageResult(chargeService.page(request, operator));
    }

    @ApiOperation(value = "front:前端分页条件查询充值订单", position = 50)
    @PostMapping(value = "/pageFront")
    public Result<PageInfo<Charge>> pageFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ChargePageReq request) {
        User operator = getUserByToken(token, EUserKind.C);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), Charge.class));

        return PageUtil.pageResult(chargeService.pageFront(request, operator));
    }

    @ApiOperation(value = "列表条件查询充值订单", position = 70)
    @PostMapping(value = "/list")
    public Result<List<Charge>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChargeListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(chargeService.list(request));
    }
}