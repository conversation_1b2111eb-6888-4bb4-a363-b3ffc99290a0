package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.UserSettleAccount;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.UserSettleAccountCreateReq;
import com.std.core.pojo.request.UserSettleAccountListReq;
import com.std.core.pojo.request.UserSettleAccountListFrontReq;
import com.std.core.pojo.request.UserSettleAccountModifyReq;
import com.std.core.pojo.request.UserSettleAccountPageReq;
import com.std.core.pojo.request.UserSettleAccountPageFrontReq;
import com.std.core.pojo.response.UserSettleAccountDetailRes;
import com.std.core.pojo.response.UserSettleAccountListRes;
import com.std.core.pojo.response.UserSettleAccountPageRes;
import com.std.core.service.IUserSettleAccountService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户结算账户Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-04-21 15:49
 */
@ApiVersion(1)
@RestController
@Api(value = "用户结算账户管理", tags = "用户结算账户管理")
@RequestMapping("{version}/user_settle_account")
public class UserSettleAccountController extends BaseController {

    @Resource
    private IUserSettleAccountService userSettleAccountService;

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增用户结算账户', NULL, '/core/v1/user_settle_account/create', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "新增用户结算账户")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserSettleAccountCreateReq request) {
//        User operator = getUserByToken(token);
//        userSettleAccountService.create(request, operator);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除用户结算账户', NULL, '/core/v1/user_settle_account/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除用户结算账户")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        userSettleAccountService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改用户结算账户', NULL, '/core/v1/user_settle_account/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改用户结算账户")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserSettleAccountModifyReq request) {
//        User operator = getUserByToken(token);
//        userSettleAccountService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询用户结算账户', NULL, '/core/v1/user_settle_account/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询用户结算账户")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<UserSettleAccount> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(userSettleAccountService.detail(id));
    }

    @ApiOperation(value = "查询平台结算账户")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail_by_plat")
    public Result<UserSettleAccount> detailByPlat(@RequestHeader(value = "Authorization") String token) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(userSettleAccountService.getPlatAccount());
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询用户结算账户', NULL, '/core/v1/user_settle_account/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询用户结算账户")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<UserSettleAccount>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserSettleAccountPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(userSettleAccountService.page(request));
    }

    @ApiOperation(value = "分页条件查询发行方结算账户")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page_company")
    public Result<PageInfo<UserSettleAccount>> pageCompany(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserSettleAccountPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(userSettleAccountService.pageCompany(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询用户结算账户', NULL, '/core/v1/user_settle_account/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询用户结算账户")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<UserSettleAccount>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserSettleAccountListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(userSettleAccountService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询用户结算账户', NULL, '/core/v1/user_settle_account/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询用户结算账户")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<UserSettleAccountDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(userSettleAccountService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询用户结算账户', NULL, '/core/v1/user_settle_account/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询用户结算账户")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<UserSettleAccountPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserSettleAccountPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), UserSettleAccount.class));

        return PageUtil.pageResult(userSettleAccountService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询用户结算账户', NULL, '/core/v1/user_settle_account/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询用户结算账户")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<UserSettleAccountListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserSettleAccountListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(userSettleAccountService.listFront(request));
    }

}