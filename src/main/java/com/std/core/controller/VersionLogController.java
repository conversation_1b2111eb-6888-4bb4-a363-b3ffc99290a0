package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.domain.VersionLog;
import com.std.core.pojo.request.VersionLogCreateReq;
import com.std.core.pojo.request.VersionLogListReq;
import com.std.core.pojo.request.VersionLogModifyReq;
import com.std.core.pojo.request.VersionLogPageReq;
import com.std.core.pojo.response.VersionLogDetailRes;
import com.std.core.pojo.response.VersionLogListRes;
import com.std.core.pojo.response.VersionLogPageRes;
import com.std.core.service.IVersionLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 版本日志Controller
 *
 * <AUTHOR> LEO
 * @since : 2021-02-09 13:36
 */
@ApiVersion(1)
@RestController
@Api(value = "版本日志管理", tags = "版本日志管理")
@RequestMapping("{version}/version_log")
public class VersionLogController extends BaseController {

    @Resource
    private IVersionLogService versionLogService;

    @ApiOperation(value = "新增版本日志")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid VersionLogCreateReq request) {
        User operator = getUserByToken(token);
        versionLogService.create(request, operator);

        return new Result();
    }

    @ApiOperation(value = "删除版本日志")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        versionLogService.remove(id);

        return new Result();
    }

    @ApiOperation(value = "修改版本日志")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid VersionLogModifyReq request) {
        User operator = getUserByToken(token);
        versionLogService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "查询版本日志")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<VersionLog> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(versionLogService.detail(id));
    }

    @ApiOperation(value = "分页条件查询版本日志")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<VersionLog>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid VersionLogPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), VersionLog.class));

        return PageUtil.pageResult(versionLogService.page(request));
    }

    @ApiOperation(value = "列表条件查询版本日志")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<VersionLog>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid VersionLogListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(versionLogService.list(request));
    }

    @ApiOperation(value = "查询版本日志")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<VersionLogDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(versionLogService.detailFront(id));
    }

    @ApiOperation(value = "前端分页条件查询版本日志")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<VersionLogPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid VersionLogPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), VersionLog.class));

        return PageUtil.pageResult(versionLogService.pageFront(request));
    }

    @ApiOperation(value = "前端列表条件查询版本日志")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<VersionLogListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid VersionLogListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(versionLogService.listFront(request));
    }

}