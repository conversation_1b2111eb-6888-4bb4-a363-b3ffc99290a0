package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.ChangeCollection;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ChangeCollectionCreateReq;
import com.std.core.pojo.request.ChangeCollectionListReq;
import com.std.core.pojo.request.ChangeCollectionListFrontReq;
import com.std.core.pojo.request.ChangeCollectionModifyReq;
import com.std.core.pojo.request.ChangeCollectionPageReq;
import com.std.core.pojo.request.ChangeCollectionPageFrontReq;
import com.std.core.pojo.response.ChangeCollectionDetailRes;
import com.std.core.pojo.response.ChangeCollectionListRes;
import com.std.core.pojo.response.ChangeCollectionPageRes;
import com.std.core.service.IChangeCollectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 藏品组件构成Controller
 *
 * <AUTHOR> xieyj
 * @since : 2021-10-27 14:47
 */
@ApiVersion(1)
@RestController
@Api(value = "藏品组件构成管理", tags = "藏品组件构成管理")
@RequestMapping("{version}/change_collection")
public class ChangeCollectionController extends BaseController {

    @Resource
    private IChangeCollectionService changeCollectionService;

//    @ApiOperation(value = "新增藏品组件构成")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChangeCollectionCreateReq request) {
//        User operator = getUserByToken(token);
//        changeCollectionService.create(request, operator);
//
//        return new Result();
//    }
//
//    @ApiOperation(value = "删除藏品组件构成")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        changeCollectionService.remove(id);
//
//        return new Result();
//    }

//    @ApiOperation(value = "修改藏品组件构成")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChangeCollectionModifyReq request) {
//        User operator = getUserByToken(token);
//        changeCollectionService.modify(request, operator);
//
//        return new Result();
//    }

    @ApiOperation(value = "查询藏品组件构成")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<ChangeCollection> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(changeCollectionService.detail(id));
    }

    @ApiOperation(value = "分页条件查询藏品组件构成")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<ChangeCollection>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChangeCollectionPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), ChangeCollection.class));

        return PageUtil.pageResult(changeCollectionService.page(request));
    }

    @ApiOperation(value = "列表条件查询藏品组件构成")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<ChangeCollection>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChangeCollectionListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(changeCollectionService.list(request));
    }

    @ApiOperation(value = "查询藏品组件构成")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<ChangeCollectionDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(changeCollectionService.detailFront(id));
    }

    @ApiOperation(value = "前端分页条件查询藏品组件构成")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<ChangeCollectionPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChangeCollectionPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), ChangeCollection.class));

        return PageUtil.pageResult(changeCollectionService.pageFront(request));
    }

    @ApiOperation(value = "前端列表条件查询藏品组件构成")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<ChangeCollectionListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChangeCollectionListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(changeCollectionService.listFront(request));
    }

}