package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.BaseIdReq;
import com.std.common.base.Result;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.*;
import com.std.core.pojo.domain.UserBindCard;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.*;
import com.std.core.service.IConfigService;
import com.std.core.service.IUserBindCardService;
import com.std.core.util.PrivacyUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;

import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户绑定快捷支付Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-03-09 17:36
 */
@ApiVersion(1)
@RestController
@Api(value = "用户绑定快捷支付管理", tags = "用户绑定快捷支付管理")
@RequestMapping("{version}/user_bind_card")
public class UserBindCardController extends BaseController {

    @Resource
    private IUserBindCardService userBindCardService;

    @Resource
    private IConfigService configService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增用户绑定快捷支付', NULL, '/core/v1/user_bind_card/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "用户预绑卡")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/ready_sign")
    public Result<UserBindCardCeateRes> create(@RequestHeader(value = "Authorization") String token,
                                               @RequestBody @Valid UserBindCardCreateReq request) {
        User operator = getUserByToken(token, EUserKind.C);
        if (!EUserIdentifyStatus.IDENTIFY_1.getCode().equals(operator.getIdentifyStatus()) &&
                !EUserIdentifyStatus.IDENTIFY_4.getCode().equals(operator.getIdentifyStatus())) {
            throw new com.std.common.exception.BizException(com.std.common.enums.ECommonErrorCode.BIZ_DEFAULT.getCode(), "请先完成实名认证");
        }
        UserBindCardCeateRes res = new UserBindCardCeateRes();
//        String payChannel = configService.getStringValue("bank_pay_channel");
        String payChannel = request.getChannelType();
        if (EBankChannelChannelType.BANK_CHANNEL_CHANNELTYPE_6.getCode().equals(payChannel)) {
            res = userBindCardService.create(request, operator);
            res.setSmsSize(6);
        } else if (EBankChannelChannelType.BANK_CHANNEL_CHANNELTYPE_7.getCode().equals(payChannel)) {
            res = userBindCardService.yeepayCreate(request, operator);
            res.setSmsSize(4);
        }
        return new Result<>(res);
    }

    @ApiOperation(value = "用户预绑卡")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/ready_sign_h5")
    public Result<UserBindCardCeateRes> createH5(@RequestHeader(value = "Authorization") String token,
                                                 @RequestBody @Valid UserBindCardCreateReq request) {
        User operator = getUserByToken(token, EUserKind.C);
        if (!EUserIdentifyStatus.IDENTIFY_1.getCode().equals(operator.getIdentifyStatus()) &&
                !EUserIdentifyStatus.IDENTIFY_4.getCode().equals(operator.getIdentifyStatus()) &&
                !EUserIdentifyStatus.IDENTIFY_6.getCode().equals(operator.getIdentifyStatus())) {
            throw new com.std.common.exception.BizException(com.std.common.enums.ECommonErrorCode.BIZ_DEFAULT.getCode(), "请先完成实名认证");
        }
        UserBindCardCeateRes res = new UserBindCardCeateRes();
//        String payChannel = configService.getStringValue("bank_pay_channel");
        String payChannel = request.getChannelType();
        if (EBankChannelChannelType.BANK_CHANNEL_CHANNELTYPE_6.getCode().equals(payChannel)) {
            res = userBindCardService.create(request, operator);
            res.setSmsSize(6);
        } else if (EBankChannelChannelType.BANK_CHANNEL_CHANNELTYPE_7.getCode().equals(payChannel)) {
            res = userBindCardService.yeepayCreate(request, operator);
            res.setSmsSize(4);
        }
        return new Result<>(res);
    }

    @ApiOperation(value = "用户确认绑卡")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/confirm_sign")
    public Result confirmSign(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserBindCardConfirmSignReq request) {
        User operator = getUserByToken(token, EUserKind.C);
//        String payChannel = configService.getStringValue("bank_pay_channel");
        String payChannel = request.getChannelType();
        if (EBankChannelChannelType.BANK_CHANNEL_CHANNELTYPE_6.getCode().equals(payChannel)) {
            userBindCardService.confirmSign(request, operator);
        } else if (EBankChannelChannelType.BANK_CHANNEL_CHANNELTYPE_7.getCode().equals(payChannel)) {
            userBindCardService.yeePayconfirmSign(request, operator);
        }
        return new Result();
    }

    @ApiOperation(value = "用户解除绑卡")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/abolish_Bind")
    public Result abolishBind(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid BaseIdReq request) {
        User operator = getUserByToken(token, EUserKind.C);
        userBindCardService.abolishBind(request, operator);
        return new Result();
    }


    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询用户绑定快捷支付', NULL, '/core/v1/user_bind_card/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询用户绑定快捷支付")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<UserBindCard> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        UserBindCard userBindCard = userBindCardService.detail(id);
        userBindCard.setCardNo(PrivacyUtil.encryptBankAcct(userBindCard.getCardNo()));
        userBindCard.setCardIdNo(PrivacyUtil.encryptIdNo(userBindCard.getCardIdNo()));
        userBindCard.setMobile(PrivacyUtil.encryptPhoneNo(userBindCard.getMobile()));
        return new Result<>(userBindCard);
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询用户绑定快捷支付', NULL, '/core/v1/user_bind_card/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询用户绑定快捷支付")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<UserBindCard>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserBindCardPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), UserBindCard.class));

        return PageUtil.pageResult(userBindCardService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询用户绑定快捷支付', NULL, '/core/v1/user_bind_card/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询用户绑定快捷支付")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<UserBindCard>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserBindCardListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(userBindCardService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询用户绑定快捷支付', NULL, '/core/v1/user_bind_card/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "根据卡号信息获取银行信息")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail_bin_info")
    public Result<UserBindCardDetailBinInfoRes> detail(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserBindCardDetailBinInfoReq request) {
        User operator = getUserByToken(token, EUserKind.C);
//        String payChannel = configService.getStringValue("bank_pay_channel");
        String payChannel = request.getChannelType();
        return new Result<>(userBindCardService.detailBinInfo(request.getCardNo(), payChannel));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询用户绑定快捷支付', NULL, '/core/v1/user_bind_card/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询用户绑定快捷支付")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<UserBindCardDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(userBindCardService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询用户绑定快捷支付', NULL, '/core/v1/user_bind_card/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询用户绑定快捷支付")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<UserBindCardPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserBindCardPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), UserBindCard.class));

        return PageUtil.pageResult(userBindCardService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询用户绑定快捷支付', NULL, '/core/v1/user_bind_card/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询用户绑定快捷支付")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<UserBindCardListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserBindCardListFrontReq request) {
        User operator = getUserByToken(token, EUserKind.C);

        return new Result<>(userBindCardService.listFront(request, operator));
    }

}