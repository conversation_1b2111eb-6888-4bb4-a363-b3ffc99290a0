package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.AuctionBond;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.AuctionBondCreateReq;
import com.std.core.pojo.request.AuctionBondListReq;
import com.std.core.pojo.request.AuctionBondListFrontReq;
import com.std.core.pojo.request.AuctionBondModifyReq;
import com.std.core.pojo.request.AuctionBondPageReq;
import com.std.core.pojo.request.AuctionBondPageFrontReq;
import com.std.core.pojo.response.AuctionBondDetailRes;
import com.std.core.pojo.response.AuctionBondListRes;
import com.std.core.pojo.response.AuctionBondPageRes;
import com.std.core.service.IAuctionBondService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 竞拍保证金缴纳记录Controller
 *
 * <AUTHOR> xieyj
 * @since : 2021-07-25 20:11
 */
@ApiVersion(1)
@RestController
@Api(value = "竞拍保证金缴纳记录管理", tags = "竞拍保证金缴纳记录管理")
@RequestMapping("{version}/auction_bond")
public class AuctionBondController extends BaseController {

    @Resource
    private IAuctionBondService auctionBondService;

//    @ApiOperation(value = "新增竞拍保证金缴纳记录")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid AuctionBondCreateReq request) {
//        User operator = getUserByToken(token);
//        auctionBondService.create(request, operator);
//
//        return new Result();
//    }

//    @ApiOperation(value = "删除竞拍保证金缴纳记录")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        auctionBondService.remove(id);
//
//        return new Result();
//    }

//    @ApiOperation(value = "修改竞拍保证金缴纳记录")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid AuctionBondModifyReq request) {
//        User operator = getUserByToken(token);
//        auctionBondService.modify(request, operator);
//
//        return new Result();
//    }

    @ApiOperation(value = "查询竞拍保证金缴纳记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<AuctionBond> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(auctionBondService.detail(id));
    }

    @ApiOperation(value = "分页条件查询竞拍保证金缴纳记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<AuctionBond>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid AuctionBondPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(auctionBondService.page(request));
    }

    @ApiOperation(value = "列表条件查询竞拍保证金缴纳记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<AuctionBond>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid AuctionBondListReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(auctionBondService.list(request));
    }

    @ApiOperation(value = "查询竞拍保证金缴纳记录")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<AuctionBondDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.C);

        return new Result<>(auctionBondService.detailFront(id));
    }

    @ApiOperation(value = "前端分页条件查询竞拍保证金缴纳记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<AuctionBondPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid AuctionBondPageFrontReq request) {
        User operator = getUserByToken(token, EUserKind.C);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), AuctionBond.class));

        return PageUtil.pageResult(auctionBondService.pageFront(request));
    }

}