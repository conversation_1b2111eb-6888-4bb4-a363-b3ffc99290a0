package com.std.core.controller;

import com.ais.common.enums.ECommonErrorCode;
import com.ais.common.exception.BizException;
import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EBigOrderPayType;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.AuctionRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.AuctionRecordBondReq;
import com.std.core.pojo.request.AuctionRecordCreateReq;
import com.std.core.pojo.request.AuctionRecordPageFrontReq;
import com.std.core.pojo.request.AuctionRecordPageReq;
import com.std.core.pojo.request.AuctionRecordPersonPageFrontReq;
import com.std.core.pojo.response.AuctionProductsPageRes;
import com.std.core.pojo.response.AuctionRecordDetailRes;
import com.std.core.pojo.response.AuctionRecordPageRes;
import com.std.core.service.IAuctionRecordService;
import com.std.core.util.RedisLock;
import com.std.core.util.RowLockUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;

import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商品竞拍出价记录Controller
 *
 * <AUTHOR> xieyj
 * @since : 2021-07-11 17:12
 */
@ApiVersion(1)
@RestController
@Api(value = "商品竞拍出价记录管理", tags = "商品竞拍出价记录管理")
@RequestMapping("{version}/auction_record")
public class AuctionRecordController extends BaseController {

    @Resource
    private IAuctionRecordService auctionRecordService;

//    @Resource
//    private RowLockUtil rowLockUtil;

    @Resource
    private RedisLock redisLock;

    @Value("${meta.lock.timeout}")
    private Long metaLockTimeout;

    @ApiOperation(value = "front: 出价")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid AuctionRecordCreateReq request) {
        User operator = getUserByToken(token, EUserKind.C);
        auctionRecordService.create(request, operator);

        return new Result();
    }

    @ApiOperation(value = "查询商品竞拍出价记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<AuctionRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(auctionRecordService.detail(id));
    }

    @ApiOperation(value = "分页条件查询商品竞拍出价记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<AuctionRecord>> page(@RequestHeader(value = "Authorization") String token,
                                                @RequestBody @Valid AuctionRecordPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), AuctionRecord.class));

        return PageUtil.pageResult(auctionRecordService.page(request));
    }

    @ApiOperation(value = "查询我的竞拍出价记录(和列表一致)")
    @ApiOperationSupport(order = 70)
    @PostMapping("/public/my_auction/{id}")
    public Result<AuctionRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token,
                                                      @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.C);
        return new Result<>(auctionRecordService.detailFront(id, operator));
    }

    @ApiOperation(value = "front:分页查询个人出价记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_person_front")
    public Result<PageInfo<AuctionProductsPageRes>> pagePersonFront(@RequestHeader(value = "Authorization") String token,
                                                                    @RequestBody @Valid AuctionRecordPersonPageFrontReq request) {
        User operator = getUserByToken(token, EUserKind.C);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), AuctionRecord.class));

        List<AuctionProductsPageRes> resList = null;
        if (StringUtils.isNotBlank(request.getStatus())) {
            resList = auctionRecordService.pagePersonFront(request, operator);
        } else {
            resList = auctionRecordService.pagePersonFrontAll(request, operator);
        }
        return PageUtil.pageResult(resList);
    }

    @ApiOperation(value = "front:分页查询商品竞拍出价记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/public/page_front")
    public Result<PageInfo<AuctionRecordPageRes>> pageFront(@RequestHeader(value = "Authorization", required = false) String token,
                                                            @RequestBody @Valid AuctionRecordPageFrontReq request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), AuctionRecord.class));

        return PageUtil.pageResult(auctionRecordService.pageFront(request));
    }

    @ApiOperation(value = "front:缴纳保证金")
    @ApiOperationSupport(order = 11)
    @PostMapping(value = "/payBond")
    public Result payBond(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid AuctionRecordBondReq request) {
        User operator = getUserByToken(token, EUserKind.C);

//        if (!EBigOrderPayType.ACCOUNT.getCode().equals(request.getPayType())) {
//            throw new com.std.common.exception.BizException(com.std.common.enums.ECommonErrorCode.BIZ_DEFAULT.getCode(), "暂不支持的支付方式");
//        }

        String id = "payBondOrProductOff" + request.getId();

//        rowLockUtil.lock(id);

        Long time = System.currentTimeMillis() + metaLockTimeout;
        if (!redisLock.lock(id, String.valueOf(time))) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "缴纳保证金进行中，请再试一次");
        }

        try {
            return new Result(auctionRecordService.payBond(request, operator));
        } finally {
//            rowLockUtil.unlock(id);
            redisLock.unlock(id, String.valueOf(time));
        }

    }
}