package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.DropOrder;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.DropOrderCreateReq;
import com.std.core.pojo.request.DropOrderListReq;
import com.std.core.pojo.request.DropOrderListFrontReq;
import com.std.core.pojo.request.DropOrderModifyReq;
import com.std.core.pojo.request.DropOrderPageReq;
import com.std.core.pojo.request.DropOrderPageFrontReq;
import com.std.core.pojo.response.DropOrderDetailRes;
import com.std.core.pojo.response.DropOrderListRes;
import com.std.core.pojo.response.DropOrderPageRes;
import com.std.core.service.IDropOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 空投记录Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-07-05 19:28
 */
@ApiVersion(1)
@RestController
@Api(value = "空投记录管理", tags = "空投记录管理")
@RequestMapping("{version}/drop_order")
public class DropOrderController extends BaseController {

    @Resource
    private IDropOrderService dropOrderService;

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增空投记录', NULL, '/core/v1/drop_order/create', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "新增空投记录")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid DropOrderCreateReq request) {
//        User operator = getUserByToken(token);
//        dropOrderService.create(request, operator);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除空投记录', NULL, '/core/v1/drop_order/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除空投记录")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        dropOrderService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改空投记录', NULL, '/core/v1/drop_order/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改空投记录")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid DropOrderModifyReq request) {
//        User operator = getUserByToken(token);
//        dropOrderService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询空投记录', NULL, '/core/v1/drop_order/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询空投记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<DropOrder> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(dropOrderService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询空投记录', NULL, '/core/v1/drop_order/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询空投记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<DropOrder>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid DropOrderPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), DropOrder.class));

        return PageUtil.pageResult(dropOrderService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询空投记录', NULL, '/core/v1/drop_order/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询空投记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<DropOrder>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid DropOrderListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(dropOrderService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询空投记录', NULL, '/core/v1/drop_order/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询空投记录")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<DropOrderDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(dropOrderService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询空投记录', NULL, '/core/v1/drop_order/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询空投记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<DropOrderPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid DropOrderPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), DropOrder.class));

        return PageUtil.pageResult(dropOrderService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询空投记录', NULL, '/core/v1/drop_order/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询空投记录")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<DropOrderListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid DropOrderListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(dropOrderService.listFront(request));
    }

}