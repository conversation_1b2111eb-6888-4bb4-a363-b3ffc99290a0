package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.RobotActivityAwardPoolRobot;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.RobotActivityAwardPoolRobotCreateReq;
import com.std.core.pojo.request.RobotActivityAwardPoolRobotListReq;
import com.std.core.pojo.request.RobotActivityAwardPoolRobotListFrontReq;
import com.std.core.pojo.request.RobotActivityAwardPoolRobotModifyReq;
import com.std.core.pojo.request.RobotActivityAwardPoolRobotPageReq;
import com.std.core.pojo.request.RobotActivityAwardPoolRobotPageFrontReq;
import com.std.core.pojo.response.RobotActivityAwardPoolRobotDetailRes;
import com.std.core.pojo.response.RobotActivityAwardPoolRobotListRes;
import com.std.core.pojo.response.RobotActivityAwardPoolRobotPageRes;
import com.std.core.service.IRobotActivityAwardPoolRobotService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 机器人挖宝活动奖池机器人关联Controller
 *
 * <AUTHOR> wzh
 * @since : 2023-05-25 10:41
 */
@ApiVersion(1)
@RestController
@Api(value = "机器人挖宝活动奖池机器人关联管理", tags = "机器人挖宝活动奖池机器人关联管理")
@RequestMapping("{version}/robot_activity_award_pool_robot")
public class RobotActivityAwardPoolRobotController extends BaseController {

    @Resource
    private IRobotActivityAwardPoolRobotService robotActivityAwardPoolRobotService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增机器人挖宝活动奖池机器人关联', NULL, '/core/v1/robot_activity_award_pool_robot/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增机器人挖宝活动奖池机器人关联")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid RobotActivityAwardPoolRobotCreateReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        robotActivityAwardPoolRobotService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除机器人挖宝活动奖池机器人关联', NULL, '/core/v1/robot_activity_award_pool_robot/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除机器人挖宝活动奖池机器人关联")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        robotActivityAwardPoolRobotService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改机器人挖宝活动奖池机器人关联', NULL, '/core/v1/robot_activity_award_pool_robot/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改机器人挖宝活动奖池机器人关联")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid RobotActivityAwardPoolRobotModifyReq request) {
        User operator = getUserByToken(token);
        robotActivityAwardPoolRobotService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询机器人挖宝活动奖池机器人关联', NULL, '/core/v1/robot_activity_award_pool_robot/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询机器人挖宝活动奖池机器人关联")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<RobotActivityAwardPoolRobot> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(robotActivityAwardPoolRobotService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询机器人挖宝活动奖池机器人关联', NULL, '/core/v1/robot_activity_award_pool_robot/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询机器人挖宝活动奖池机器人关联")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<RobotActivityAwardPoolRobot>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid RobotActivityAwardPoolRobotPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), RobotActivityAwardPoolRobot.class));

        return PageUtil.pageResult(robotActivityAwardPoolRobotService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询机器人挖宝活动奖池机器人关联', NULL, '/core/v1/robot_activity_award_pool_robot/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询机器人挖宝活动奖池机器人关联")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<RobotActivityAwardPoolRobot>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid RobotActivityAwardPoolRobotListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(robotActivityAwardPoolRobotService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询机器人挖宝活动奖池机器人关联', NULL, '/core/v1/robot_activity_award_pool_robot/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询机器人挖宝活动奖池机器人关联")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<RobotActivityAwardPoolRobotDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(robotActivityAwardPoolRobotService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询机器人挖宝活动奖池机器人关联', NULL, '/core/v1/robot_activity_award_pool_robot/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询机器人挖宝活动奖池机器人关联")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<RobotActivityAwardPoolRobotPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid RobotActivityAwardPoolRobotPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), RobotActivityAwardPoolRobot.class));

        return PageUtil.pageResult(robotActivityAwardPoolRobotService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询机器人挖宝活动奖池机器人关联', NULL, '/core/v1/robot_activity_award_pool_robot/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询机器人挖宝活动奖池机器人关联")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<RobotActivityAwardPoolRobotListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid RobotActivityAwardPoolRobotListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(robotActivityAwardPoolRobotService.listFront(request));
    }

}