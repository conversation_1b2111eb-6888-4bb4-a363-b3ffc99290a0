package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.ContractTokenInPool15;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ContractTokenInPool15CreateReq;
import com.std.core.pojo.request.ContractTokenInPool15ListReq;
import com.std.core.pojo.request.ContractTokenInPool15ListFrontReq;
import com.std.core.pojo.request.ContractTokenInPool15ModifyReq;
import com.std.core.pojo.request.ContractTokenInPool15PageReq;
import com.std.core.pojo.request.ContractTokenInPool15PageFrontReq;
import com.std.core.pojo.response.ContractTokenInPool15DetailRes;
import com.std.core.pojo.response.ContractTokenInPool15ListRes;
import com.std.core.pojo.response.ContractTokenInPool15PageRes;
import com.std.core.service.IContractTokenInPool15Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 代币池信息Controller
 *
 * <AUTHOR> xieyj
 * @since : 2022-04-17 01:15
 */
@ApiVersion(1)
@RestController
@Api(value = "代币池信息管理", tags = "代币池信息管理")
@RequestMapping("{version}/contract_token_in_pool15")
public class ContractTokenInPool15Controller extends BaseController {

    @Resource
    private IContractTokenInPool15Service contractTokenInPool15Service;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增代币池信息', NULL, '/core/v1/contract_token_in_pool15/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增代币池信息")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ContractTokenInPool15CreateReq request) {
        User operator = getUserByToken(token);
        contractTokenInPool15Service.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除代币池信息', NULL, '/core/v1/contract_token_in_pool15/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除代币池信息")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        contractTokenInPool15Service.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改代币池信息', NULL, '/core/v1/contract_token_in_pool15/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改代币池信息")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ContractTokenInPool15ModifyReq request) {
        User operator = getUserByToken(token);
        contractTokenInPool15Service.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询代币池信息', NULL, '/core/v1/contract_token_in_pool15/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询代币池信息")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<ContractTokenInPool15> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(contractTokenInPool15Service.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询代币池信息', NULL, '/core/v1/contract_token_in_pool15/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询代币池信息")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<ContractTokenInPool15>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ContractTokenInPool15PageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), ContractTokenInPool15.class));

        return PageUtil.pageResult(contractTokenInPool15Service.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询代币池信息', NULL, '/core/v1/contract_token_in_pool15/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询代币池信息")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<ContractTokenInPool15>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ContractTokenInPool15ListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(contractTokenInPool15Service.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询代币池信息', NULL, '/core/v1/contract_token_in_pool15/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询代币池信息")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<ContractTokenInPool15DetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(contractTokenInPool15Service.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询代币池信息', NULL, '/core/v1/contract_token_in_pool15/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询代币池信息")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<ContractTokenInPool15PageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ContractTokenInPool15PageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), ContractTokenInPool15.class));

        return PageUtil.pageResult(contractTokenInPool15Service.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询代币池信息', NULL, '/core/v1/contract_token_in_pool15/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询代币池信息")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<ContractTokenInPool15ListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ContractTokenInPool15ListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(contractTokenInPool15Service.listFront(request));
    }

}