package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.PeriodWhitelistConfig;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.PeriodWhitelistConfigCreateReq;
import com.std.core.pojo.request.PeriodWhitelistConfigListReq;
import com.std.core.pojo.request.PeriodWhitelistConfigListFrontReq;
import com.std.core.pojo.request.PeriodWhitelistConfigModifyReq;
import com.std.core.pojo.request.PeriodWhitelistConfigPageReq;
import com.std.core.pojo.request.PeriodWhitelistConfigPageFrontReq;
import com.std.core.pojo.response.PeriodWhitelistConfigDetailRes;
import com.std.core.pojo.response.PeriodWhitelistConfigListRes;
import com.std.core.pojo.response.PeriodWhitelistConfigPageRes;
import com.std.core.service.IPeriodWhitelistConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 白名单配置Controller
 *
 * <AUTHOR> wzh
 * @since : 2023-08-18 13:24
 */
@ApiVersion(1)
@RestController
@Api(value = "白名单配置管理", tags = "白名单配置管理")
@RequestMapping("{version}/period_whitelist_config")
public class PeriodWhitelistConfigController extends BaseController {

    @Resource
    private IPeriodWhitelistConfigService periodWhitelistConfigService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增白名单配置', NULL, '/core/v1/period_whitelist_config/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增白名单配置")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodWhitelistConfigCreateReq request) {
        User operator = getUserByToken(token);
        periodWhitelistConfigService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除白名单配置', NULL, '/core/v1/period_whitelist_config/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除白名单配置")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        periodWhitelistConfigService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改白名单配置', NULL, '/core/v1/period_whitelist_config/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改白名单配置")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodWhitelistConfigModifyReq request) {
        User operator = getUserByToken(token);
        periodWhitelistConfigService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询白名单配置', NULL, '/core/v1/period_whitelist_config/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询白名单配置")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<PeriodWhitelistConfig> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(periodWhitelistConfigService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询白名单配置', NULL, '/core/v1/period_whitelist_config/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询白名单配置")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<PeriodWhitelistConfig>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodWhitelistConfigPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), PeriodWhitelistConfig.class));

        return PageUtil.pageResult(periodWhitelistConfigService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询白名单配置', NULL, '/core/v1/period_whitelist_config/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询白名单配置")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<PeriodWhitelistConfig>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodWhitelistConfigListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(periodWhitelistConfigService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询白名单配置', NULL, '/core/v1/period_whitelist_config/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询白名单配置")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<PeriodWhitelistConfigDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.C);

        return new Result<>(periodWhitelistConfigService.detailFront(id,operator));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询白名单配置', NULL, '/core/v1/period_whitelist_config/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询白名单配置")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<PeriodWhitelistConfigPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodWhitelistConfigPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), PeriodWhitelistConfig.class));

        return PageUtil.pageResult(periodWhitelistConfigService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询白名单配置', NULL, '/core/v1/period_whitelist_config/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询白名单配置")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<PeriodWhitelistConfigListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodWhitelistConfigListFrontReq request) {
        User operator = getUserByToken(token,EUserKind.C);

        return new Result<>(periodWhitelistConfigService.listFront(request,operator));
    }

}