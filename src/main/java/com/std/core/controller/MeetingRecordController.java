package com.std.core.controller;

import com.ais.common.enums.ECommonErrorCode;
import com.ais.common.exception.BizException;
import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EMeetingSeatUserType;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.MeetingRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.AgoraTokenRes;
import com.std.core.pojo.response.MeetingRecordDetailRes;
import com.std.core.pojo.response.MeetingRecordListRes;
import com.std.core.pojo.response.MeetingRecordPageRes;
import com.std.core.service.IMeetingRecordService;
import com.std.core.util.RedisLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;

import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 直播会议参与记录Controller
 *
 * <AUTHOR> wzh
 * @since : 2023-05-04 10:14
 */
@ApiVersion(1)
@RestController
@Api(value = "直播会议参与记录管理", tags = "直播会议参与记录管理")
@RequestMapping("{version}/meeting_record")
public class MeetingRecordController extends BaseController {

    @Resource
    private IMeetingRecordService meetingRecordService;

    @Resource
    private RedisLock redisLock;

    @Value("${meta.lock.timeout}")
    private Long metaLockTimeout;

    @ApiOperation(value = "front:加入会议")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/join")
    public Result<AgoraTokenRes> join(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MeetingRecordCreateReq request) {
        User operator = getUserByToken(token, EUserKind.C);
        String lockId = "meetingRecord" + operator.getId();
        long time = System.currentTimeMillis() + metaLockTimeout;
        if (!redisLock.lock(lockId, String.valueOf(time))) {
//                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "已锁定，请稍后再试");
            return null;
        }
        try {
            return new Result<>(meetingRecordService.create(request, operator));
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }


    }

    @ApiOperation(value = "front:退出会议")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/exit")
    public Result exit(@RequestHeader(value = "Authorization") String token) {
        User operator = getUserByToken(token, EUserKind.C);

        meetingRecordService.exit(operator);

        return new Result<>();

    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除直播会议参与记录', NULL, '/core/v1/meeting_record/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除直播会议参与记录")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        meetingRecordService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改直播会议参与记录', NULL, '/core/v1/meeting_record/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改直播会议参与记录")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MeetingRecordModifyReq request) {
        User operator = getUserByToken(token);
        meetingRecordService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询直播会议参与记录', NULL, '/core/v1/meeting_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询直播会议参与记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<MeetingRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(meetingRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询直播会议参与记录', NULL, '/core/v1/meeting_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询直播会议参与记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<MeetingRecord>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MeetingRecordPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), MeetingRecord.class));
        request.setParticipantType(EMeetingSeatUserType.MEETING_SEAT_USERTYPE_0.getCode());
        return PageUtil.pageResult(meetingRecordService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询直播会议参与记录', NULL, '/core/v1/meeting_record/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询直播会议参与记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<MeetingRecord>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MeetingRecordListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(meetingRecordService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询直播会议参与记录', NULL, '/core/v1/meeting_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询直播会议参与记录")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<MeetingRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(meetingRecordService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询直播会议参与记录', NULL, '/core/v1/meeting_record/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询直播会议参与记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<MeetingRecordPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MeetingRecordPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), MeetingRecord.class));

        return PageUtil.pageResult(meetingRecordService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询直播会议参与记录', NULL, '/core/v1/meeting_record/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询直播会议参与记录")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<MeetingRecordListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MeetingRecordListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(meetingRecordService.listFront(request));
    }

}