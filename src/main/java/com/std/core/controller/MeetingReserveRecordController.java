package com.std.core.controller;

import com.ais.common.enums.ECommonErrorCode;
import com.ais.common.exception.BizException;
import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.MeetingReserveRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.MeetingReserveRecordCreateReq;
import com.std.core.pojo.request.MeetingReserveRecordListReq;
import com.std.core.pojo.request.MeetingReserveRecordListFrontReq;
import com.std.core.pojo.request.MeetingReserveRecordModifyReq;
import com.std.core.pojo.request.MeetingReserveRecordPageReq;
import com.std.core.pojo.request.MeetingReserveRecordPageFrontReq;
import com.std.core.pojo.response.MeetingReserveRecordDetailRes;
import com.std.core.pojo.response.MeetingReserveRecordListRes;
import com.std.core.pojo.response.MeetingReserveRecordPageRes;
import com.std.core.service.IMeetingReserveRecordService;
import com.std.core.util.RedisLock;
import com.std.core.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 直播会议预约记录Controller
 *
 * <AUTHOR> wzh
 * @since : 2023-05-04 10:15
 */
@ApiVersion(1)
@RestController
@Api(value = "直播会议预约记录管理", tags = "直播会议预约记录管理")
@RequestMapping("{version}/meeting_reserve_record")
public class MeetingReserveRecordController extends BaseController {

    @Resource
    private IMeetingReserveRecordService meetingReserveRecordService;

    @Resource
    private RedisLock redisLock;

    @Value("${meta.lock.timeout}")
    private Long metaLockTimeout;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增直播会议预约记录', NULL, '/core/v1/meeting_reserve_record/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增直播会议预约记录")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MeetingReserveRecordCreateReq request) {
        User operator = getUserByToken(token, EUserKind.C);
        String lockId = "meetingReserve" + operator.getId();
        long time = System.currentTimeMillis() + metaLockTimeout;

        try {
            if (!redisLock.lock(lockId, String.valueOf(time))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "已锁定，请稍后再试");
            }
            meetingReserveRecordService.create(request, operator);
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }
        return new Result<>();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除直播会议预约记录', NULL, '/core/v1/meeting_reserve_record/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除直播会议预约记录")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        meetingReserveRecordService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改直播会议预约记录', NULL, '/core/v1/meeting_reserve_record/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改直播会议预约记录")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MeetingReserveRecordModifyReq request) {
        User operator = getUserByToken(token);
        meetingReserveRecordService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询直播会议预约记录', NULL, '/core/v1/meeting_reserve_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询直播会议预约记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<MeetingReserveRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token,EUserKind.SYS);

        return new Result<>(meetingReserveRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询直播会议预约记录', NULL, '/core/v1/meeting_reserve_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询直播会议预约记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<MeetingReserveRecord>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MeetingReserveRecordPageReq request) {
        User operator = getUserByToken(token,EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), MeetingReserveRecord.class));

        return PageUtil.pageResult(meetingReserveRecordService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询直播会议预约记录', NULL, '/core/v1/meeting_reserve_record/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询直播会议预约记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<MeetingReserveRecord>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MeetingReserveRecordListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(meetingReserveRecordService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询直播会议预约记录', NULL, '/core/v1/meeting_reserve_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询直播会议预约记录")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<MeetingReserveRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(meetingReserveRecordService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询直播会议预约记录', NULL, '/core/v1/meeting_reserve_record/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询直播会议预约记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<MeetingReserveRecordPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MeetingReserveRecordPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), MeetingReserveRecord.class));

        return PageUtil.pageResult(meetingReserveRecordService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询直播会议预约记录', NULL, '/core/v1/meeting_reserve_record/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询直播会议预约记录")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<MeetingReserveRecordListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MeetingReserveRecordListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(meetingReserveRecordService.listFront(request));
    }

}