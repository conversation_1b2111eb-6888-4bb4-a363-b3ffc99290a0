package com.std.core.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> silver
 * @since : 2020-04-11 12:40
 */
@Data
@Component
public class ChannelAuctionConfig {

    @Value("${channleAction.System.url}")
    private String systemUrl;

    @Value("${channleAction.System.get.token}")
    private String getToken;

    @Value("${channleAction.System.user.apply.authorization}")
    private String userApplyAuthorization;

    @Value("${channleAction.System.user.collection.info}")
    private String userCollectionInfo;

    @Value("${channleAction.System.collection.pass.on}")
    private String collectionPassOn;



    @Value("${channleAction.System.roll.out.callback}")
    private String rollOutCallback;

    //    @Value("${wechat.merchant.name}")
    //写在配置文件会出现乱码问题
    private String merchantName = "麦塔";

}
