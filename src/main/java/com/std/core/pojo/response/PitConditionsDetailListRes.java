package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> ycj
 * @since : 2022-05-06 17:35
 */
@Data
public class PitConditionsDetailListRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 条件序号
     */
    @ApiModelProperty(name = "conditionId", value = "条件序号", position = 20)
    private Long conditionId;

    /**
     * 藏品序号
     */
    @ApiModelProperty(name = "collectionId", value = "藏品序号", position = 30)
    private Long collectionId;

    /**
     * 封面文件地址
     */
    @ApiModelProperty(name = "coverFileUrl", value = "封面文件地址", position = 40)
    private String coverFileUrl;

}
