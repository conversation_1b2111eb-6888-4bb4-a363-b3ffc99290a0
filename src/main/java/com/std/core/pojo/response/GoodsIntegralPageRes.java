package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import com.std.core.enums.EGoodsIntegralGoodsType;
import com.std.core.enums.EGoodsIntegralType;
import com.std.core.enums.EGoodsIntegralStatus;
import com.std.core.enums.EGoodsIntegralSoldStatus;

/**
 * <AUTHOR> ycj
 * @since : 2022-04-25 22:51
 */
@Data
public class GoodsIntegralPageRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 商品名称
     */
    @ApiModelProperty(name = "name", value = "商品名称", position = 30)
    private String name;

    /**
     * 封面图
     */
    @ApiModelProperty(name = "pic", value = "封面图", position = 40)
    private String pic;

    /**
     * 价值
     */
    @ApiModelProperty(name = "price", value = "价值", position = 50)
    private BigDecimal price;

    /**
     * 积分价格
     */
    @ApiModelProperty(name = "integralPrice", value = "积分价格", position = 60)
    private BigDecimal integralPrice;

    /**
     * 商品类型 {0:实物商品,1:虚拟商品}
     */
    @ApiModelProperty(name = "goodsType", value = "商品类型 {0:实物商品,1:虚拟商品}", position = 90)
    private String goodsType;

    /**
     * 类型 {0:正常商品,1:特殊商品,2:神秘商品}
     */
    @ApiModelProperty(name = "type", value = "类型 {0:正常商品,1:特殊商品,2:神秘商品}", position = 100)
    private String type;

    /**
     * 售卖状态 {0:未解锁,1:售卖中,2:已售罄}
     */
    @ApiModelProperty(name = "soldStatus", value = "售卖状态 {0:未解锁,1:售卖中,2:已售罄}", position = 120)
    private String soldStatus;

}
