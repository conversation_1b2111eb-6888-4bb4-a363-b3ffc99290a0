package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


@Data
public class MyAuctionRecordsRes3 {

    @ApiModelProperty(name = "sellerName", value = "商家名称")
    private String sellerName;

    @ApiModelProperty(name = "id", value = "乐拍商品编号")
    private Long id;

    @ApiModelProperty(name = "thumb", value = "缩略图")
    private String thumb;

    @ApiModelProperty(name = "name", value = "产品名")
    private String name;

    @ApiModelProperty(name = "currentPrice", value = "中拍价")
    private BigDecimal currentPrice;

    @ApiModelProperty(name = "priceAuction", value = "加价幅度")
    private BigDecimal priceAuction;

    @ApiModelProperty(name = "price", value = "你的出价")
    private BigDecimal price;


}
