package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import com.std.core.enums.EMetaTicketRecordStatus;

/**
 * <AUTHOR> ycj
 * @since : 2022-10-13 15:17
 */
@Data
public class MetaTicketRecordPageRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 门票配置类型
     */
    @ApiModelProperty(name = "ticketId", value = "门票配置类型", position = 20)
    private Long ticketId;

    /**
     * 门票类型
     */
    @ApiModelProperty(name = "ticketType", value = "门票类型", position = 30)
    private String ticketType;

    /**
     * 藏品
     */
    @ApiModelProperty(name = "collectionDetailId", value = "藏品", position = 40)
    private Long collectionDetailId;

    /**
     * 状态 {0:失效,1:生效中}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:失效,1:生效中}", position = 50)
    private String status;

    /**
     * 失效时间
     */
    @ApiModelProperty(name = "failureDatetime", value = "失效时间", position = 60)
    private Date failureDatetime;

    /**
     * 使用人
     */
    @ApiModelProperty(name = "creater", value = "使用人", position = 70)
    private Long creater;

    /**
     * 使用时间
     */
    @ApiModelProperty(name = "createDatetime", value = "使用时间", position = 80)
    private Date createDatetime;

    /**** Properties ****/

    /**
     * 状态 {0:失效,1:生效中}
     */
    @ApiModelProperty(name = "statusName", value = "状态 {0:失效,1:生效中}")
    private String statusName;

    public String getStatusName() {
        if (StringUtils.isNotBlank(status)) {
            statusName = EMetaTicketRecordStatus.getMetaTicketRecordStatus(status).getValue();
        }

        return statusName;
    }

}
