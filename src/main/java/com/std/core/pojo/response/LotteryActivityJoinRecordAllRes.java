package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> ycj
 * @since : 2022-06-02 11:03
 */
@Data
public class LotteryActivityJoinRecordAllRes {


    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 活动序号
     */
    @ApiModelProperty(name = "activityId", value = "活动序号", position = 20)
    private Long activityId;

    /**
     * 活动 {0:已报名,1:已中奖,2:未中奖}
     */
    @ApiModelProperty(name = "status", value = "活动 {0:已报名,1:已中奖,2:未中奖}", position = 40)
    private String status;

    /**
     * 昵称
     */
    @ApiModelProperty(name = "nickname", value = "昵称")
    private String nickname;

    /**
     * 头像
     */
    @ApiModelProperty(name = "photo", value = "头像")
    private String photo;


    /**
     * 顺序
     */
    @ApiModelProperty(name = "orderNo", value = "顺序", position = 60)
    private Integer orderNo;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 70)
    private Date createDatetime;

    /**
     * 创建时间戳
     */
    @ApiModelProperty(name = "createTime", value = "创建时间戳", position = 80)
    private Long createTime;

}
