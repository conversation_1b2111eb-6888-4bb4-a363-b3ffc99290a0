package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> xieyj
 * @since : 2023-02-14 14:45
 */
@Data
public class BlindboxUserListRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号", position = 20)
    private Long userId;

    /**
     * 用户
     */
    private User user;

    /**
     * 数量
     */
    @ApiModelProperty(name = "quantity", value = "数量", position = 30)
    private Integer quantity;

}
