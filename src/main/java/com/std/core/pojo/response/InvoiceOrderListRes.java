package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import com.std.core.enums.EInvoiceOrderOrderType;
import com.std.core.enums.EInvoiceOrderStatus;

/**
 * <AUTHOR> xieyj
 * @since : 2022-08-19 10:30
 */
@Data
public class InvoiceOrderListRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号", position = 20)
    private Long userId;

    /**
     * 用户
     */
    private User user;

    /**
     * 关联订单类型{0:购买藏品,1:幸运抽奖,2:平台服务费(寄售),3:平台服务费(提现手续费),4:权益购买}
     */
    @ApiModelProperty(name = "orderType", value = "关联订单类型{0:购买藏品,1:幸运抽奖,2:平台服务费(寄售),3:平台服务费(提现手续费),4:权益购买}", position = 30)
    private String orderType;

    /**
     * 关联订单id
     */
    @ApiModelProperty(name = "orderId", value = "关联订单id", position = 40)
    private Long orderId;

    /**
     * 订单说明
     */
    @ApiModelProperty(name = "orderNote", value = "订单说明", position = 50)
    private String orderNote;

    /**
     * 开票金额
     */
    @ApiModelProperty(name = "amount", value = "开票金额", position = 60)
    private BigDecimal amount;

    /**
     * 公司编号
     */
    @ApiModelProperty(name = "companyId", value = "公司编号", position = 70)
    private Long companyId;

    /**
     * 公司名称
     */
    @ApiModelProperty(name = "companyName", value = "公司名称", position = 80)
    private String companyName;

    /**
     * 产生时间
     */
    @ApiModelProperty(name = "createDatetime", value = "产生时间", position = 90)
    private Date createDatetime;

    /**
     * 状态{0:待申请发票,1:已申请发票}
     */
    @ApiModelProperty(name = "status", value = "状态{0:待申请发票,1:已申请发票}", position = 100)
    private String status;

    /**
     * 申请发票id
     */
    @ApiModelProperty(name = "invoiceApplyId", value = "申请发票id", position = 110)
    private Long invoiceApplyId;

    /**** Properties ****/

    /**
     * 关联订单类型{0:购买藏品,1:幸运抽奖,2:平台服务费(寄售),3:平台服务费(提现手续费),4:权益购买}
     */
    @ApiModelProperty(name = "orderTypeName", value = "关联订单类型{0:购买藏品,1:幸运抽奖,2:平台服务费(寄售),3:平台服务费(提现手续费),4:权益购买}")
    private String orderTypeName;

    /**
     * 状态{0:待申请发票,1:已申请发票}
     */
    @ApiModelProperty(name = "statusName", value = "状态{0:待申请发票,1:已申请发票}")
    private String statusName;

    public String getOrderTypeName() {
        if (StringUtils.isNotBlank(orderType)) {
            orderTypeName = EInvoiceOrderOrderType.getInvoiceOrderOrderType(orderType).getValue();
        }

        return orderTypeName;
    }

    public String getStatusName() {
        if (StringUtils.isNotBlank(status)) {
            statusName = EInvoiceOrderStatus.getInvoiceOrderStatus(status).getValue();
        }

        return statusName;
    }

}
