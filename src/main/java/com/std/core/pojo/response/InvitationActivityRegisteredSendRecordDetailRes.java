package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> ycj
 * @since : 2022-05-12 19:42
 */
@Data
public class InvitationActivityRegisteredSendRecordDetailRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 系列序号
     */
    @ApiModelProperty(name = "seriesId", value = "系列序号", position = 20)
    private Long seriesId;

    /**
     * 活动序号
     */
    @ApiModelProperty(name = "activityId", value = "活动序号", position = 30)
    private Long activityId;

    /**
     * 记录序号
     */
    @ApiModelProperty(name = "recordId", value = "记录序号", position = 40)
    private Long recordId;

    /**
     * 用户序号
     */
    @ApiModelProperty(name = "userId", value = "用户序号", position = 50)
    private Long userId;

    /**
     * 用户
     */
    private User user;

    /**
     * 作品序号
     */
    @ApiModelProperty(name = "collectionId", value = "作品序号", position = 60)
    private Long collectionId;

    /**
     * 藏品序号
     */
    @ApiModelProperty(name = "collectionDetailId", value = "藏品序号", position = 70)
    private Long collectionDetailId;

    /**
     * 触发用户
     */
    @ApiModelProperty(name = "refUser", value = "触发用户", position = 80)
    private Long refUser;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 90)
    private Date createDatetime;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createTime", value = "创建时间", position = 100)
    private Long createTime;

}
