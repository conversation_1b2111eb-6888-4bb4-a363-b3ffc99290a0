package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import io.micrometer.core.instrument.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import com.std.core.enums.EChangeTypeStatus;

/**
 * <AUTHOR> ycj
 * @since : 2021-10-25 16:38
 */
@Data
public class ChangeTypeListRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 类型名称
     */
    @ApiModelProperty(name = "name", value = "类型名称", position = 20)
    private String name;

    /**
     * 图片
     */
    @ApiModelProperty(name = "pic", value = "图片", position = 30)
    private String pic;

    /**
     * 选中图
     */
    @ApiModelProperty(name = "onPic", value = "选中图", position = 35)
    private String onPic;

    /**
     * 层次
     */
    @ApiModelProperty(name = "layer", value = "层次", position = 35)
    private Integer layer;

    /**
     * 序号
     */
    @ApiModelProperty(name = "orderNo", value = "序号", position = 40)
    private Integer orderNo;

    /**
     * 状态 0:待上架,1:已上架,2:已下架
     */
    @ApiModelProperty(name = "status", value = "状态 {0:待上架,1:已上架,2:已下架}", position = 50)
    private String status;


    /**** Properties ****/

    /**
     * 状态 0:待上架,1:已上架,2:已下架
     */
    @ApiModelProperty(name = "statusName", value = "状态 0:待上架,1:已上架,2:已下架")
    private String statusName;

    public String getStatusName() {
        if (StringUtils.isNotBlank(status)) {
            statusName = EChangeTypeStatus.getChangeTypeStatus(status).getValue();
        }

        return statusName;
    }

}
