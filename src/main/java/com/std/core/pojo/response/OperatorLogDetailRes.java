package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> ycj
 * @since : 2022-02-20 18:26
 */
@Data
public class OperatorLogDetailRes {

    /**
     * 自增主键
     */
    @ApiModelProperty(name = "id", value = "自增主键", position = 10)
    private Long id;

    /**
     * 操作者uid
     */
    @ApiModelProperty(name = "userId", value = "操作者uid", position = 20)
    private Long userId;

    /**
     * 用户
     */
    private User user;

    /**
     * 操作时间
     */
    @ApiModelProperty(name = "operateTime", value = "操作时间", position = 30)
    private Date operateTime;

    /**
     * 操作ip
     */
    @ApiModelProperty(name = "ip", value = "操作ip", position = 40)
    private String ip;

    /**
     * 浏览器头信息
     */
    @ApiModelProperty(name = "userAgent", value = "浏览器头信息", position = 50)
    private String userAgent;

    /**
     * 操作url
     */
    @ApiModelProperty(name = "url", value = "操作url", position = 60)
    private String url;

    /**
     * 表单get内容
     */
    @ApiModelProperty(name = "get", value = "表单get内容", position = 70)
    private String get;

    /**
     * 表单post内容
     */
    @ApiModelProperty(name = "post", value = "表单post内容", position = 80)
    private String post;

}
