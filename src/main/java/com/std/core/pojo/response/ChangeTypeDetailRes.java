package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import io.micrometer.core.instrument.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import com.std.core.enums.EChangeTypeStatus;

/**
 * <AUTHOR> ycj
 * @since : 2021-10-25 16:38
 */
@Data
public class ChangeTypeDetailRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 类型名称
     */
    @ApiModelProperty(name = "name", value = "类型名称", position = 20)
    private String name;

    /**
     * 图片
     */
    @ApiModelProperty(name = "pic", value = "图片", position = 30)
    private String pic;

    /**
     * 序号
     */
    @ApiModelProperty(name = "orderNo", value = "序号", position = 40)
    private Integer orderNo;

    /**
     * 状态 0:待上架,1:已上架,2:已下架
     */
    @ApiModelProperty(name = "status", value = "状态 {0:待上架,1:已上架,2:已下架}", position = 50)
    private String status;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 60)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updateName", value = "更新人名称", position = 70)
    private String updateName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updaterDatetime", value = "更新时间", position = 80)
    private Date updaterDatetime;

    /**** Properties ****/

    /**
     * 状态 0:待上架,1:已上架,2:已下架
     */
    @ApiModelProperty(name = "statusName", value = "状态 0:待上架,1:已上架,2:已下架")
    private String statusName;

    public String getStatusName() {
        if (StringUtils.isNotBlank(status)) {
            statusName = EChangeTypeStatus.getChangeTypeStatus(status).getValue();
        }

        return statusName;
    }

}
