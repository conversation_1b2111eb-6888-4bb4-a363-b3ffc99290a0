package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import com.std.core.enums.EYaoMilletConfigType;
import com.std.core.enums.EYaoMilletConfigStatus;

/**
 * <AUTHOR> ycj
 * @since : 2022-11-09 20:39
 */
@Data
public class YaoMilletConfigPageRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 类型 {0:乾,1:坤,2:巽,3:坎,4:艮,5:震,6:离,7:兑}
     */
    @ApiModelProperty(name = "type", value = "类型 {0:乾,1:坤,2:巽,3:坎,4:艮,5:震,6:离,7:兑}", position = 20)
    private String type;

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称", position = 30)
    private String name;

    /**
     * 所需爻图
     */
    @ApiModelProperty(name = "yaoPic", value = "所需爻图", position = 40)
    private String yaoPic;

    /**
     * 元粟图
     */
    @ApiModelProperty(name = "milletPic", value = "元粟图", position = 50)
    private String milletPic;

    /**
     * 每日兑换上限
     */
    @ApiModelProperty(name = "dayMax", value = "每日兑换上限", position = 60)
    private BigDecimal dayMax;

    /**
     * 需要的阴爻数
     */
    @ApiModelProperty(name = "yinYao", value = "需要的阴爻数", position = 70)
    private BigDecimal yinYao;

    /**
     * 需要的阳爻数
     */
    @ApiModelProperty(name = "yangYao", value = "需要的阳爻数", position = 80)
    private BigDecimal yangYao;

    /**
     * 状态 {0:废弃,1:启用}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:废弃,1:启用}", position = 90)
    private String status;

    /**
     * 顺序
     */
    @ApiModelProperty(name = "orderNo", value = "顺序", position = 100)
    private Integer orderNo;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 110)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称", position = 120)
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 130)
    private Date updateDatetime;

    /**** Properties ****/

    /**
     * 类型 {0:乾,1:坤,2:巽,3:坎,4:艮,5:震,6:离,7:兑}
     */
    @ApiModelProperty(name = "typeName", value = "类型 {0:乾,1:坤,2:巽,3:坎,4:艮,5:震,6:离,7:兑}")
    private String typeName;

    /**
     * 状态 {0:废弃,1:启用}
     */
    @ApiModelProperty(name = "statusName", value = "状态 {0:废弃,1:启用}")
    private String statusName;

    public String getTypeName() {
        if (StringUtils.isNotBlank(type)) {
            typeName = EYaoMilletConfigType.getYaoMilletConfigType(type).getValue();
        }

        return typeName;
    }

    public String getStatusName() {
        if (StringUtils.isNotBlank(status)) {
            statusName = EYaoMilletConfigStatus.getYaoMilletConfigStatus(status).getValue();
        }

        return statusName;
    }

}
