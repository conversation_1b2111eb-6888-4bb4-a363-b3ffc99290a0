package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> ycj
 * @since : 2022-03-20 14:21
 */
@Data
public class ChannelUserDetailRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 渠道类型
     */
    @ApiModelProperty(name = "channelId", value = "渠道类型", position = 20)
    private Long channelId;

    /**
     * 关联渠道用户
     */
    @ApiModelProperty(name = "channelUserId", value = "关联渠道用户", position = 30)
    private Long channelUserId;

    /**
     * 用户序号
     */
    @ApiModelProperty(name = "userId", value = "用户序号", position = 40)
    private Long userId;

    /**
     * 用户
     */
    private User user;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 50)
    private Date createDatetime;

}
