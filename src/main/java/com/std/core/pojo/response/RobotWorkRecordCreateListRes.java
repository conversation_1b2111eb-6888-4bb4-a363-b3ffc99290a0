package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * 新增机器人挖宝记录
 *
 * <AUTHOR> ycj
 * @since : 2022-08-29 20:16
 */
@Data
public class RobotWorkRecordCreateListRes {

    @ApiModelProperty(name = "sendTime", value = "派遣时间(小时)")
    private String sendTime;

    @ApiModelProperty(name = "robotCreateResList", value = "机器人信息")
    private List<RobotWorkRecordCreateDetailListRes> robotCreateResList;
}
