package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * OSS上传临时Token响应
 *
 * <AUTHOR> system
 * @since : 2024-07-30
 */
@Data
public class OssUploadTokenRes {

    /**
     * 临时访问密钥ID
     */
    @ApiModelProperty(name = "accessKeyId", value = "临时访问密钥ID", position = 10)
    private String accessKeyId;

    /**
     * 临时访问密钥Secret
     */
    @ApiModelProperty(name = "accessKeySecret", value = "临时访问密钥Secret", position = 20)
    private String accessKeySecret;

    /**
     * 安全令牌
     */
    @ApiModelProperty(name = "securityToken", value = "安全令牌", position = 30)
    private String securityToken;

    /**
     * OSS Bucket名称
     */
    @ApiModelProperty(name = "bucket", value = "OSS Bucket名称", position = 40)
    private String bucket;

    /**
     * OSS Endpoint
     */
    @ApiModelProperty(name = "endpoint", value = "OSS Endpoint", position = 50)
    private String endpoint;

    /**
     * 上传路径前缀
     */
    @ApiModelProperty(name = "prefix", value = "上传路径前缀", position = 60)
    private String prefix;

    /**
     * Token过期时间（秒）
     */
    @ApiModelProperty(name = "expiration", value = "Token过期时间（秒）", position = 70)
    private Long expiration;

    /**
     * 文件访问基础URL
     */
    @ApiModelProperty(name = "baseUrl", value = "文件访问基础URL", position = 80)
    private String baseUrl;

    public OssUploadTokenRes() {
    }

    public OssUploadTokenRes(String accessKeyId, String accessKeySecret, String securityToken, 
                           String bucket, String endpoint, String prefix, Long expiration, String baseUrl) {
        this.accessKeyId = accessKeyId;
        this.accessKeySecret = accessKeySecret;
        this.securityToken = securityToken;
        this.bucket = bucket;
        this.endpoint = endpoint;
        this.prefix = prefix;
        this.expiration = expiration;
        this.baseUrl = baseUrl;
    }
}
