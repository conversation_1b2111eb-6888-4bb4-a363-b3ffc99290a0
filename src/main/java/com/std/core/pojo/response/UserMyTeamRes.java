package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2021/2/8 下午5:38
 */
@Data
public class UserMyTeamRes {

    /**
     * 直推总人数
     */
    @ApiModelProperty(name = "teamCount", value = "直推总人数", position = 10)
    private Integer teamCount;

    /**
     * 直推激活总人数
     */
    @ApiModelProperty(name = "teamActiveCount", value = "直推激活总人数", position = 20)
    private Integer teamActiveCount;

    /**
     * 团队今日盈利
     */
    @ApiModelProperty(name = "teamTodayIncome", value = "团队今日盈利", position = 30)
    private BigDecimal teamTodayIncome;

    /**
     * 团队累计盈利
     */
    @ApiModelProperty(name = "teamTotalIncome", value = "团队累计盈利", position = 40)
    private BigDecimal teamTotalIncome;

    /**
     * 直推列表
     */
    @ApiModelProperty(name = "teamList", value = "直推列表", position = 50)
    private List<SubUserRes> teamList;

}

    
    