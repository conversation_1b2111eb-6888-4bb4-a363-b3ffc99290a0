package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> ycj
 * @since : 2022-04-19 19:10
 */
@Data
public class UserSettleAccountInfoRes {

    @ApiModelProperty(name = "toSettleAmount", value = "代结算金额", position = 30)
    private BigDecimal toSettleAmount;

    @ApiModelProperty(name = "settleAmount", value = "结算金额", position = 30)
    private BigDecimal settleAmount;

    @ApiModelProperty(name = "settleNote", value = "结算说明", position = 30)
    private String settleNote;

//    @ApiModelProperty(name = "recordResList", value = "结算明细", position = 30)
//    private List<UserSettleRecordRes> recordResList;
}
