package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> ycj
 * @since : 2023-04-27 16:27
 */
@Data
public class ChannelSystemExportConfirmRes {

    /**
     * 渠道名称
     */
    @ApiModelProperty(name = "channelName", value = "渠道名称", position = 40)
    private String channelName;

    /**
     * 转币地址
     */
    @ApiModelProperty(name = "toAddress", value = "转币地址", position = 70)
    private String toAddress;


    @ApiModelProperty(name = "quantity", value = "导出数量", position = 70)
    private Integer quantity;

    @ApiModelProperty(name = "freeQuantity", value = "免费数量", position = 70)
    private Integer freeQuantity;

    @ApiModelProperty(name = "fee", value = "手续费", position = 70)
    private BigDecimal fee;
}
