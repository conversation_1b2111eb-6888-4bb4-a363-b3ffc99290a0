package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> ycj
 * @since : 2022-06-02 14:21
 */
@Data
public class LotteryActivityJoinDetailDetailRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 作品序号
     */
    @ApiModelProperty(name = "collectionId", value = "作品序号", position = 60)
    private Long collectionId;

    /**
     * 藏品名称
     */
    @ApiModelProperty(name = "collectionName", value = "藏品名称", position = 70)
    private String collectionName;

    /**
     * 封面文件地址
     */
    @ApiModelProperty(name = "coverFileUrl", value = "封面文件地址", position = 80)
    private String coverFileUrl;

    /**
     * 藏品序号
     */
    @ApiModelProperty(name = "collectionDetailId", value = "藏品序号", position = 90)
    private Long collectionDetailId;

    /**
     * 代币编号
     */
    @ApiModelProperty(name = "tokenId", value = "代币编号", position = 100)
    private String tokenId;

    @ApiModelProperty(name = "status", value = "报名记录 0:锁定中，1：已解锁", position = 100)
    private String status;
}
