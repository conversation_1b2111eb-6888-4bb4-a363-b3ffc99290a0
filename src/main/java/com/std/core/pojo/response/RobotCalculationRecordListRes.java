package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> ycj
 * @since : 2022-08-29 21:51
 */
@Data
public class RobotCalculationRecordListRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 奖品id
     */
    @ApiModelProperty(name = "awardId", value = "奖品id", position = 20)
    private Long awardId;

    /**
     * 奖品类型 0=藏品 1=道具 2=阴阳爻  3=红包
     */
    @ApiModelProperty(name = "awardRefType", value = "奖品类型 0=藏品 1=道具 2=阴阳爻 3=红包", position = 20)
    private String awardRefType;

    /**
     * 藏品名称
     */
    @ApiModelProperty(name = "collectionName", value = "藏品名称", position = 20)
    private String collectionName;

    /**
     * 模糊文件地址
     */
    @ApiModelProperty(name = "coverFileUrl", value = "模糊文件地址", position = 30)
    private String coverFileUrl;

    /**
     * 阴爻个数
     */
    @ApiModelProperty(name = "yaoYinQuantity", value = "阴爻个数", position = 40)
    private String yaoYinQuantity;

    /**
     * 阳爻个数
     */
    @ApiModelProperty(name = "yaoYangQuantity", value = "阳爻个数", position = 50)
    private String yaoYangQuantity;

    /**
     * 备注说明
     */
    @ApiModelProperty(name = "note", value = "备注说明", position = 60)
    private String note;

}
