package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 新增竞拍渠道用户序号
 *
 * <AUTHOR> ycj
 * @since : 2022-03-19 18:49
 */
@Data
public class ChannelCollectionExportRes {

    /**
     * 订单编号
     */
    @ApiModelProperty(name = "orderId", value = "订单编号", position = 20)
    private Long orderId;

    @ApiModelProperty(name = "channelUserId", value = "渠道系统用户", position = 40)
    private Long  channelUserId;

    @ApiModelProperty(name = "address", value = "对方链上地址", position = 40)
    private String address;

}
