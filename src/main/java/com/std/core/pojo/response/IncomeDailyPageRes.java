package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> LEO
 * @since : 2021-02-07 14:12
 */
@Data
public class IncomeDailyPageRes {

    /**
     * 主键编号
     */
    @ApiModelProperty(name = "id", value = "主键编号", position = 10)
    private Long id;

    /**
     * 日期
     */
    @ApiModelProperty(name = "incomeDate", value = "日期", position = 20)
    private Date incomeDate;

    /**
     * 分佣总额
     */
    @ApiModelProperty(name = "amount", value = "分佣总额", position = 30)
    private BigDecimal amount;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 40)
    private Date createDatetime;

}
