package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> ycj
 * @since : 2022-03-29 13:47
 */
@Data
public class PeriodAuctionDetailRes {

    /**
     * 
     */
    @ApiModelProperty(name = "id", value = "", position = 10)
    private Long id;

    /**
     * 期数编号
     */
    @ApiModelProperty(name = "periodId", value = "期数编号", position = 20)
    private Long periodId;

    /**
     * 保证金
     */
    @ApiModelProperty(name = "bond", value = "保证金", position = 30)
    private BigDecimal bond;

    /**
     * 起拍价
     */
    @ApiModelProperty(name = "startPrice", value = "起拍价", position = 40)
    private BigDecimal startPrice;

    /**
     * 加价幅度
     */
    @ApiModelProperty(name = "priceAuction", value = "加价幅度", position = 50)
    private BigDecimal priceAuction;

    /**
     * 成交价
     */
    @ApiModelProperty(name = "finalPrice", value = "成交价", position = 60)
    private BigDecimal finalPrice;

    /**
     * 最后出价用户id
     */
    @ApiModelProperty(name = "lastUserId", value = "最后出价用户id", position = 70)
    private Long lastUserId;

    /**
     * 当前价格
     */
    @ApiModelProperty(name = "currentPrice", value = "当前价格", position = 80)
    private BigDecimal currentPrice;

    /**
     * 竞价次数
     */
    @ApiModelProperty(name = "auctionTimes", value = "竞价次数", position = 90)
    private Integer auctionTimes;

    /**
     * 开拍时间
     */
    @ApiModelProperty(name = "startTime", value = "开拍时间", position = 100)
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(name = "endTime", value = "结束时间", position = 110)
    private Date endTime;

    /**
     * 拍卖时限(分)
     */
    @ApiModelProperty(name = "timeLimit", value = "拍卖时限(分)", position = 120)
    private Integer timeLimit;

    /**
     * 延迟秒数
     */
    @ApiModelProperty(name = "delayedSecond", value = "延迟秒数", position = 130)
    private Integer delayedSecond;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createTime", value = "创建时间", position = 140)
    private Date createTime;

    /**
     * 更改时间
     */
    @ApiModelProperty(name = "updateTime", value = "更改时间", position = 150)
    private Date updateTime;

    /**
     * 最终结束时间
     */
    @ApiModelProperty(name = "delayedTime", value = "最终结束时间", position = 160)
    private Date delayedTime;

}
