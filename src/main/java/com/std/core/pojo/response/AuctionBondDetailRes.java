package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> xieyj
 * @since : 2021-07-25 20:11
 */
@Data
public class AuctionBondDetailRes {

    /**
     * 
     */
    @ApiModelProperty(name = "id", value = "", position = 10)
    private Long id;

    /**
     * 乐拍id
     */
    @ApiModelProperty(name = "auctionId", value = "乐拍id", position = 20)
    private Long auctionId;

    /**
     * 3支付宝APP支付，5微信APP支付，9余额支付
     */
    @ApiModelProperty(name = "payment", value = "3支付宝APP支付，5微信APP支付，9余额支付", position = 30)
    private String payment;

    /**
     * 用户id
     */
    @ApiModelProperty(name = "userId", value = "用户id", position = 40)
    private Long userId;

    /**
     * 用户
     */
    private User user;

    /**
     * 是否支付 0未支付 1已支付
     */
    @ApiModelProperty(name = "status", value = "是否支付 0未支付 1已支付", position = 50)
    private String status;

    /**
     * 支付时间
     */
    @ApiModelProperty(name = "payTime", value = "支付时间", position = 60)
    private Date payTime;

    /**
     * 退款时间
     */
    @ApiModelProperty(name = "refundTime", value = "退款时间", position = 70)
    private Date refundTime;

    /**
     * 退款状态,0未退款，1已退款
     */
    @ApiModelProperty(name = "isRefund", value = "退款状态,0未退款，1已退款", position = 80)
    private String isRefund;

    /**
     * 第三方支付流水号
     */
    @ApiModelProperty(name = "serialNumber", value = "第三方支付流水号", position = 90)
    private Long serialNumber;

}
