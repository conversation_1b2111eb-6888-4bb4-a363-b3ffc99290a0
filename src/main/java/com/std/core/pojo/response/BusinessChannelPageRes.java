package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> ycj
 * @since : 2022-02-17 16:06
 */
@Data
public class BusinessChannelPageRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 渠道序号
     */
    @ApiModelProperty(name = "channelId", value = "渠道序号", position = 20)
    private Long channelId;

    /**
     * 业务类型
     */
    @ApiModelProperty(name = "bizType", value = "业务类型", position = 30)
    private String bizType;

    /**
     * 是否启用 0:不启用,1:启用
     */
    @ApiModelProperty(name = "status", value = "是否启用 0:不启用,1:启用", position = 40)
    private String status;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 50)
    private Long updater;

    /**
     * 更新人名
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名", position = 60)
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 70)
    private Date updateDatetime;

}
