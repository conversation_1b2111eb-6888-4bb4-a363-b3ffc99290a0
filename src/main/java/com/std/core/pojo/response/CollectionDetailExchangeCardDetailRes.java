package com.std.core.pojo.response;

import com.std.core.enums.ECollectionDetailExchangeCardStatus;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> xieyj
 * @since : 2022-12-13 17:32
 */
@Data
public class CollectionDetailExchangeCardDetailRes {

    /**
     * id
     */
    @ApiModelProperty(name = "id", value = "id", position = 10)
    private Long id;

    /**
     * 作品id
     */
    @ApiModelProperty(name = "collectionId", value = "作品id", position = 20)
    private Long collectionId;

    /**
     * 藏品型号id
     */
    @ApiModelProperty(name = "collectionDetailId", value = "藏品型号id", position = 30)
    private Long collectionDetailId;

    /**
     * 上架作品id
     */
    @ApiModelProperty(name = "publishCollectionId", value = "上架作品id", position = 40)
    private Long publishCollectionId;

    /**
     * 上架藏品型号id
     */
    @ApiModelProperty(name = "publishCollectionDetailId", value = "上架藏品型号id", position = 50)
    private Long publishCollectionDetailId;

    /**
     * 状态 {0:待使用,1:已使用}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:待使用,1:已使用}", position = 60)
    private String status;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 70)
    private Date createDatetime;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 80)
    private Date updateDatetime;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注", position = 90)
    private String remark;

    /**** Properties ****/

    /**
     * {0:待使用,1:已使用}
     */
    @ApiModelProperty(name = "statusName", value = " {0:待使用,1:已使用}")
    private String statusName;

    public String getStatusName() {
        if (StringUtils.isNotBlank(status)) {
            statusName = ECollectionDetailExchangeCardStatus.getCollectionDetailExchangeCardStatus(status).getValue();
        }

        return statusName;
    }

}
