package com.std.core.pojo.response;

import com.std.core.enums.EIdentifyOrderStatus;
import com.std.core.pojo.domain.User;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR> xieyj
 * @since : 2021-07-07 14:45
 */
@Data
public class IdentifyOrderListRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号", position = 20)
    private String userId;

    /**
     * 用户
     */
    private User user;

    /**
     * 状态 {"0":"待调用三方","1":"三方认证成功","2":"三方认证失败待人工认证","3":"人工认证通过","4":"人工认证失败"}
     */
    @ApiModelProperty(name = "status", value = "状态 {\"0\":\"待调用三方\",\"1\":\"三方认证成功\",\"2\":\"三方认证失败待人工认证\",\"3\":\"人工认证通过\",\"4\":\"人工认证失败\"}", position = 30)
    private String status;

    /**
     * 真实姓名
     */
    @ApiModelProperty(name = "realName", value = "真实姓名", position = 40)
    private String realName;

    /**
     * 身份证号码
     */
    @ApiModelProperty(name = "idNo", value = "身份证号码", position = 50)
    private String idNo;

    /**
     * 身份证正面照片
     */
    @ApiModelProperty(name = "frontImage", value = "身份证正面照片", position = 60)
    private String frontImage;

    /**
     * 身份证反面照片
     */
    @ApiModelProperty(name = "backImage", value = "身份证反面照片", position = 70)
    private String backImage;

    /**
     * 人脸照片
     */
    @ApiModelProperty(name = "faceImage", value = "人脸照片", position = 80)
    private String faceImage;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 90)
    private Date createDatetime;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 100)
    private String updater;

    /**
     * 修改时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "修改时间", position = 110)
    private String updateDatetime;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注", position = 120)
    private String remark;

    /**** Properties ****/

    /**
     * 状态 {"0":"待调用三方","1":"三方认证成功","2":"三方认证失败待人工认证","3":"人工认证通过","4":"人工认证失败"}
     */
    @ApiModelProperty(name = "statusName", value = "状态 {\"0\":\"待调用三方\",\"1\":\"三方认证成功\",\"2\":\"三方认证失败待人工认证\",\"3\":\"人工认证通过\",\"4\":\"人工认证失败\"}", position = 30)
    private String statusName;

    public String getStatusName() {
        if (StringUtils.isNotBlank(status)) {
            statusName = EIdentifyOrderStatus.getIdentifyOrderStatus(status).getValue();
        }

        return statusName;
    }

}
