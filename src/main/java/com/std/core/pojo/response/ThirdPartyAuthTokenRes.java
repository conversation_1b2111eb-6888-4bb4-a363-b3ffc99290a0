package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 第三方生成授权令牌响应
 *
 * <AUTHOR> system
 * @since : 2024-07-29
 */
@Data
public class ThirdPartyAuthTokenRes {

    /**
     * 授权令牌
     */
    @ApiModelProperty(name = "token", value = "授权令牌", position = 10)
    private String token;

    /**
     * 过期时间
     */
    @ApiModelProperty(name = "expire_time", value = "过期时间", position = 20)
    private String expire_time;

    /**
     * 请求的授权范围
     */
    @ApiModelProperty(name = "requested_scopes", value = "请求的授权范围", position = 30)
    private List<String> requested_scopes;

    /**
     * 授权页面URL
     */
    @ApiModelProperty(name = "auth_url", value = "授权页面URL", position = 40)
    private String auth_url;

    public ThirdPartyAuthTokenRes() {
    }

    public ThirdPartyAuthTokenRes(String token, String expire_time, List<String> requested_scopes, String auth_url) {
        this.token = token;
        this.expire_time = expire_time;
        this.requested_scopes = requested_scopes;
        this.auth_url = auth_url;
    }
}
