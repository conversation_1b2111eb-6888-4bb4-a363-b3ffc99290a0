package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> xieyj
 * @since : 2022-01-19 13:32
 */
@Data
public class ContractListRes {

    /**
     * 
     */
    @ApiModelProperty(name = "id", value = "", position = 10)
    private Long id;

    /**
     * 合约地址
     */
    @ApiModelProperty(name = "contractAddress", value = "合约地址", position = 20)
    private String contractAddress;

    /**
     * 协议
     */
    @ApiModelProperty(name = "protocol", value = "协议", position = 30)
    private String protocol;

    /**
     * 链信息
     */
    @ApiModelProperty(name = "chain", value = "链信息", position = 40)
    private String chain;

    /**
     * 状态0=待上架 1=已上架
     */
    @ApiModelProperty(name = "status", value = "状态0=待上架 1=已上架", position = 50)
    private String status;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 60)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称", position = 70)
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 80)
    private Date updateDatetime;

}
