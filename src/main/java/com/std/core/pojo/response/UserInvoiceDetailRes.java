package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import com.std.core.enums.EUserInvoiceType;

/**
 * <AUTHOR> ycj
 * @since : 2022-08-18 15:59
 */
@Data
public class UserInvoiceDetailRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 类型{0:个人,1:企业单位}
     */
    @ApiModelProperty(name = "type", value = "类型{0:个人,1:企业单位}", position = 20)
    private String type;

    /**
     * 抬头名称
     */
    @ApiModelProperty(name = "name", value = "抬头名称", position = 30)
    private String name;

    /**
     * 税号
     */
    @ApiModelProperty(name = "taxNo", value = "税号", position = 40)
    private String taxNo;

    /**
     * 注册地
     */
    @ApiModelProperty(name = "registerAddress", value = "注册地", position = 50)
    private String registerAddress;

    /**
     * 注册电话
     */
    @ApiModelProperty(name = "registerPhone", value = "注册电话", position = 60)
    private String registerPhone;

    /**
     * 开户支行名称
     */
    @ApiModelProperty(name = "openBranch", value = "开户支行名称", position = 70)
    private String openBranch;

    /**
     * 银行账号
     */
    @ApiModelProperty(name = "bankAccount", value = "银行账号", position = 80)
    private String bankAccount;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 90)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称", position = 100)
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 110)
    private Date updateDatetime;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注", position = 120)
    private String remark;

    /**** Properties ****/

    /**
     * 类型{0:个人,1:企业单位}
     */
    @ApiModelProperty(name = "typeName", value = "类型{0:个人,1:企业单位}")
    private String typeName;

    public String getTypeName() {
        if (StringUtils.isNotBlank(type)) {
            typeName = EUserInvoiceType.getUserInvoiceType(type).getValue();
        }

        return typeName;
    }

}
