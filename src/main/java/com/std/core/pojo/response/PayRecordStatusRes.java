package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

import lombok.Data;

/**
 * <AUTHOR> ycj
 * @since : 2021-11-04 16:01
 */
@Data
public class PayRecordStatusRes {

    /**
     * 状态（0=待支付 1=已支付 2=支付取消）
     */
    @ApiModelProperty(name = "status", value = "状态（0=待支付 1=已支付 2=支付取消）", position = 70)
    private String status;

    @ApiModelProperty(name = "bizType", value = "业务类型0:充值,1:创建作品支付 2 一口价订单支付 3 竞拍支付保证金 4:竞拍支付 5:购买作品包支付", position = 90)
    private String bizType;

    @ApiModelProperty(name = "recommendListRes", value = "抽中的盲盒信息")
    private ChangeRecommendListRes recommendListRes;

    @ApiModelProperty(name = "payCollectionRes", value = "购买的藏品信息")
    private CollectionOrderPayCollectionRes payCollectionRes;

    @ApiModelProperty(name = "openDatetime", value = "开图时间")
    private Date openDatetime;

    @ApiModelProperty(name = "openFlag", value = "开图标识 0:未开,1:已开", position = 65)
    private String openFlag = "0";

    @ApiModelProperty(name = "openPic", value = "已开图片", position = 65)
    private String openPic;

    @ApiModelProperty(name = "periodCategory", value = "期数类别")
    private String periodCategory;

    @ApiModelProperty(name = "periodId", value = "期数id")
    private Long periodId;
}
