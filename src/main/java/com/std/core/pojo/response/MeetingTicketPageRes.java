package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import com.std.core.enums.EMeetingTicketType;

/**
 * <AUTHOR> ycj
 * @since : 2023-05-24 16:59
 */
@Data
public class MeetingTicketPageRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 门票类型 {0:藏品,1:密码}
     */
    @ApiModelProperty(name = "type", value = "门票类型 {0:藏品,1:密码}", position = 20)
    private String type;

    /**
     * 会议序号
     */
    @ApiModelProperty(name = "meetingId", value = "会议序号", position = 30)
    private Long meetingId;

    /**
     * 门票序号
     */
    @ApiModelProperty(name = "collectionId", value = "门票序号", position = 40)
    private Long collectionId;

    /**
     * 密码
     */
    @ApiModelProperty(name = "pwd", value = "密码", position = 50)
    private String pwd;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 60)
    private Date createDatetime;

    /**** Properties ****/

    /**
     * 门票类型 {0:藏品,1:密码}
     */
    @ApiModelProperty(name = "typeName", value = "门票类型 {0:藏品,1:密码}")
    private String typeName;

    public String getTypeName() {
        if (StringUtils.isNotBlank(type)) {
            typeName = EMeetingTicketType.getMeetingTicketType(type).getValue();
        }

        return typeName;
    }

}
