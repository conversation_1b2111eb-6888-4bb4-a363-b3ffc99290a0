package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> ycj
 * @since : 2020/10/24 16:36
 */
@Data
public class SubUserRes {

    @ApiModelProperty(name = "id", value = "用户编号", position = 1)
    private Long id;

    @ApiModelProperty(name = "nickname", value = "昵称", position = 2)
    private String nickname;

    @ApiModelProperty(name = "mobile", value = "手机号", position = 10)
    private String mobile;

    @ApiModelProperty(name = "photo", value = "头像", position = 20)
    private String photo;

    @ApiModelProperty(name = "registerDatetime", value = "注册时间", position = 30)
    private String registerDatetime;

    @ApiModelProperty(name = "memberFlag", value = "是否激活", position = 40)
    private String memberFlag;

    @ApiModelProperty(name = "todayIncome", value = "今日盈利", position = 50)
    private BigDecimal todayIncome;

    @ApiModelProperty(name = "totalIncome", value = "累计盈利", position = 60)
    private String totalIncome;

    @ApiModelProperty(name = "isChannel", value = "是否为群主 1是，0否", position = 70)
    private String isChannel;

}
