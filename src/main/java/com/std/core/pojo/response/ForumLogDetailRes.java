package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> xieyj
 * @since : 2021-07-23 18:57
 */
@Data
public class ForumLogDetailRes {

    /**
     * 编号
     */
    @ApiModelProperty(name = "id", value = "编号", position = 10)
    private Long id;

    /**
     * 类型(1关注 2点赞 3评论)
     */
    @ApiModelProperty(name = "type", value = "类型(1关注 2点赞 3评论)", position = 20)
    private String type;

    /**
     * 类型说明(关注，取消关注 点赞，取消点赞，评论)
     */
    @ApiModelProperty(name = "typeNote", value = "类型说明(关注，取消关注 点赞，取消点赞，评论)", position = 30)
    private String typeNote;

    /**
     * 关联编号(关注是用户编号，点赞是帖子的编号，评论是评论的编号)
     */
    @ApiModelProperty(name = "relId", value = "关联编号(关注是用户编号，点赞是帖子的编号，评论是评论的编号)", position = 40)
    private String relId;

    /**
     * 关联用户编号
     */
    @ApiModelProperty(name = "relUserId", value = "关联用户编号", position = 50)
    private String relUserId;

    /**
     * 上级编号(关注是用户编号，点赞是帖子的编号，评论是帖子的编号)
     */
    @ApiModelProperty(name = "preId", value = "上级编号(关注是用户编号，点赞是帖子的编号，评论是帖子的编号)", position = 60)
    private String preId;

    /**
     * 帖子编号
     */
    @ApiModelProperty(name = "postId", value = "帖子编号", position = 70)
    private Long postId;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号", position = 80)
    private String userId;

    /**
     * 用户
     */
    private User user;

    /**
     * 阅读标志 0=未读 1已读
     */
    @ApiModelProperty(name = "status", value = "阅读标志 0=未读 1已读", position = 90)
    private String status;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 100)
    private Date createDatetime;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注", position = 110)
    private String remark;

}
