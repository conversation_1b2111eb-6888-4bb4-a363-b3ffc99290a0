package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

/**
 * <AUTHOR> ycj
 * @since : 2022-05-23 16:46
 */
@Data
public class DegressionAuctionQualificationsListRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 藏品
     */
    @ApiModelProperty(name = "collectionId", value = "藏品", position = 30)
    private Long collectionId;

    /**
     * 藏品名称
     */
    @ApiModelProperty(name = "collectionName", value = "藏品名称", position = 40)
    private String collectionName;

    /**
     * 封面文件地址
     */
    @ApiModelProperty(name = "coverFileUrl", value = "封面文件地址", position = 50)
    private String coverFileUrl;

    /**
     * 数量
     */
    @ApiModelProperty(name = "quantity", value = "数量", position = 60)
    private Integer quantity;

    /**
     * 拥有数量
     */
    @ApiModelProperty(name = "haveQuantity", value = "拥有数量", position = 60)
    private Integer haveQuantity;

    @ApiModelProperty(name = "collectFlag", value = "集齐标识 0:未集齐，1:已拥有", position = 60)
    private String collectFlag;
}
