package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> ycj
 * @since : 2022-10-13 11:21
 */
@Data
public class CollectionRightsDetailTicketCheckRes {

    @ApiModelProperty(name = "ticketFlag",value = "是否需要选择门票标识 0:不需要,1:需要")
    private String ticketFlag;

    /**
     * 头像
     */
    @ApiModelProperty(name = "photo", value = "头像")
    private String photo;

    /**
     * 昵称
     */
    @ApiModelProperty(name = "nickname", value = "昵称")
    private String nickname;

    /**
     * 用户序号
     */
    @ApiModelProperty(name = "userId", value = "用户序号")
    private Long userId;

    @ApiModelProperty(name = "ticketNote", value = "元宇宙门票说明")
    private String ticketNote;
}
