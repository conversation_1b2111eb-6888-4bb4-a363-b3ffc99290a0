package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import com.std.core.enums.EDropRecordStatus;

/**
 * <AUTHOR> ycj
 * @since : 2022-07-05 19:43
 */
@Data
public class DropRecordPageRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 订单序号
     */
    @ApiModelProperty(name = "orderId", value = "订单序号", position = 20)
    private Long orderId;

    /**
     * 空投藏品
     */
    @ApiModelProperty(name = "collectionId", value = "空投藏品", position = 30)
    private Long collectionId;

    /**
     * 空投类型 0:按用户空投,1:按藏品空投
     */
    @ApiModelProperty(name = "type", value = "空投类型 0:按用户空投,1:按藏品空投", position = 40)
    private String type;

    /**
     * 状态 {0:失败,1:成功}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:失败,1:成功}", position = 50)
    private String status;

    /**
     * 手机号
     */
    @ApiModelProperty(name = "mobile", value = "手机号", position = 60)
    private String mobile;

    /**
     * 用户
     */
    @ApiModelProperty(name = "userId", value = "用户", position = 70)
    private Long userId;

    /**
     * 用户
     */
    private User user;

    /**
     * 失败原因
     */
    @ApiModelProperty(name = "remark", value = "失败原因", position = 80)
    private String remark;

    /**
     * 操作人
     */
    @ApiModelProperty(name = "creater", value = "操作人", position = 90)
    private Long creater;

    /**
     * 操作人类型
     */
    @ApiModelProperty(name = "createrKind", value = "操作人类型", position = 100)
    private String createrKind;

    /**
     * 操作人名称
     */
    @ApiModelProperty(name = "creatrName", value = "操作人名称", position = 110)
    private String creatrName;

    /**
     * 操作时间
     */
    @ApiModelProperty(name = "createDatetime", value = "操作时间", position = 120)
    private Date createDatetime;

    /**** Properties ****/

    /**
     * 状态 {0:失败,1:成功}
     */
    @ApiModelProperty(name = "statusName", value = "状态 {0:失败,1:成功}")
    private String statusName;

    public String getStatusName() {
        if (StringUtils.isNotBlank(status)) {
            statusName = EDropRecordStatus.getDropRecordStatus(status).getValue();
        }

        return statusName;
    }

}
