package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * PFP图片上传响应
 *
 * <AUTHOR> system
 * @since : 2024-07-30
 */
@Data
public class PfpImageUploadRes {

    /**
     * 成功上传的图片数量
     */
    @ApiModelProperty(name = "successCount", value = "成功上传的图片数量", position = 10)
    private Integer successCount;

    /**
     * 失败上传的图片数量
     */
    @ApiModelProperty(name = "failCount", value = "失败上传的图片数量", position = 20)
    private Integer failCount;

    /**
     * 上传成功的图片信息
     */
    @ApiModelProperty(name = "successList", value = "上传成功的图片信息", position = 30)
    private List<PfpImageInfo> successList;

    /**
     * 上传失败的图片信息
     */
    @ApiModelProperty(name = "failList", value = "上传失败的图片信息", position = 40)
    private List<PfpImageFailInfo> failList;

    @Data
    public static class PfpImageInfo {
        @ApiModelProperty(name = "orderNumber", value = "排序号", position = 10)
        private Integer orderNumber;

        @ApiModelProperty(name = "originalName", value = "原始文件名", position = 20)
        private String originalName;

        @ApiModelProperty(name = "ossUrl", value = "OSS存储URL", position = 30)
        private String ossUrl;

        @ApiModelProperty(name = "ipfsUrl", value = "IPFS存储URL", position = 40)
        private String ipfsUrl;
    }

    @Data
    public static class PfpImageFailInfo {
        @ApiModelProperty(name = "originalName", value = "原始文件名", position = 10)
        private String originalName;

        @ApiModelProperty(name = "errorMessage", value = "错误信息", position = 20)
        private String errorMessage;
    }
}
