package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> xieyj
 * @since : 2021-05-07 13:46
 */
@Data
public class StatisticPageRes {

    /**
     * 
     */
    @ApiModelProperty(name = "id", value = "", position = 10)
    private Long id;

    /**
     * 新增用户数
     */
    @ApiModelProperty(name = "newUserCount", value = "新增用户数", position = 20)
    private Integer newUserCount;

    /**
     * 活跃用户数
     */
    @ApiModelProperty(name = "activeUserCount", value = "活跃用户数", position = 30)
    private Integer activeUserCount;

    /**
     * 截至当日用户总数
     */
    @ApiModelProperty(name = "userCount", value = "截至当日用户总数", position = 40)
    private Long userCount;

    /**
     * 当日充值(usdt)
     */
    @ApiModelProperty(name = "chargeTotal", value = "当日充值(usdt)", position = 50)
    private BigDecimal chargeTotal;

    /**
     * 当日提取(usdt)
     */
    @ApiModelProperty(name = "turnOutTotal", value = "当日提取(usdt)", position = 60)
    private BigDecimal turnOutTotal;

    /**
     * 净流入(usdt)
     */
    @ApiModelProperty(name = "netTotal", value = "净流入(usdt)", position = 70)
    private BigDecimal netTotal;

    /**
     * 总余额(usdt)
     */
    @ApiModelProperty(name = "balanceTotal", value = "总余额(usdt)", position = 80)
    private BigDecimal balanceTotal;

    /**
     * 当前持仓金额(usdt)
     */
    @ApiModelProperty(name = "entrustAmount", value = "当前持仓金额(usdt)", position = 90)
    private BigDecimal entrustAmount;

    /**
     * 历史持仓金额(usdt)
     */
    @ApiModelProperty(name = "entrustAmountHistory", value = "历史持仓金额(usdt)", position = 100)
    private BigDecimal entrustAmountHistory;

    /**
     * 总操盘资金
     */
    @ApiModelProperty(name = "operateAmount", value = "总操盘资金", position = 110)
    private BigDecimal operateAmount;

    /**
     * 总运营资金
     */
    @ApiModelProperty(name = "businessAmount", value = "总运营资金", position = 120)
    private BigDecimal businessAmount;

    /**
     * 当前仓位个数
     */
    @ApiModelProperty(name = "cycleCount", value = "当前仓位个数", position = 130)
    private Integer cycleCount;

    /**
     * 历史仓位个数
     */
    @ApiModelProperty(name = "cycleHistoryCount", value = "历史仓位个数", position = 140)
    private Integer cycleHistoryCount;

    /**
     * 总仓位个数
     */
    @ApiModelProperty(name = "cycleCountTotal", value = "总仓位个数", position = 150)
    private Integer cycleCountTotal;

    /**
     * 用户总盈利
     */
    @ApiModelProperty(name = "profitAmount", value = "用户总盈利", position = 160)
    private BigDecimal profitAmount;

    /**
     * 平台佣金收入
     */
    @ApiModelProperty(name = "commissionAmount", value = "平台佣金收入", position = 170)
    private BigDecimal commissionAmount;

    /**
     * 分佣总支出
     */
    @ApiModelProperty(name = "commissionOut", value = "分佣总支出", position = 180)
    private BigDecimal commissionOut;

    /**
     * 新用户新增操盘资金
     */
    @ApiModelProperty(name = "newUserOperateAmount", value = "新用户新增操盘资金", position = 190)
    private BigDecimal newUserOperateAmount;

    /**
     * 老用户新增操盘资金
     */
    @ApiModelProperty(name = "oldUserOperateAmount", value = "老用户新增操盘资金", position = 200)
    private BigDecimal oldUserOperateAmount;

    /**
     * 当日新增操盘资金
     */
    @ApiModelProperty(name = "totalAddOperateAmount", value = "当日新增操盘资金", position = 210)
    private BigDecimal totalAddOperateAmount;

    /**
     * 日期
     */
    @ApiModelProperty(name = "createTime", value = "日期", position = 220)
    private Date createTime;

}
