package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> ycj
 * @since : 2022-10-25 16:34
 */
@Data
public class ChipUidListRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 活动序号
     */
    @ApiModelProperty(name = "activityId", value = "活动序号", position = 20)
    private Long activityId;

    /**
     * 芯片编号
     */
    @ApiModelProperty(name = "uid", value = "芯片编号", position = 30)
    private String uid;

    /**
     * 状态 0:未使用,1:已使用
     */
    @ApiModelProperty(name = "status", value = "状态 0:未使用,1:已使用", position = 40)
    private String status;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 50)
    private Date createDatetime;

    /**
     * 使用时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "使用时间", position = 60)
    private Date updateDatetime;

}
