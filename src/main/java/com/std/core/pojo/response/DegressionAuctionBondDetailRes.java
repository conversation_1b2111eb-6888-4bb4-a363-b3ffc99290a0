package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import com.std.core.enums.EDegressionAuctionBondStatus;
import com.std.core.enums.EDegressionAuctionBondPayType;
import com.std.core.enums.EDegressionAuctionBondPayStatus;

/**
 * <AUTHOR> ycj
 * @since : 2022-05-23 19:37
 */
@Data
public class DegressionAuctionBondDetailRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 竞拍序号
     */
    @ApiModelProperty(name = "auctionId", value = "竞拍序号", position = 20)
    private Long auctionId;

    /**
     * 竞拍名称
     */
    @ApiModelProperty(name = "auctionName", value = "竞拍名称", position = 30)
    private String auctionName;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号", position = 40)
    private Long userId;

    /**
     * 用户
     */
    private User user;

    /**
     * 订单序号
     */
    @ApiModelProperty(name = "orderId", value = "订单序号", position = 50)
    private Long orderId;

    /**
     * {0:已缴纳,1:已退还,2:已违约扣除}
     */
    @ApiModelProperty(name = "status", value = "{0:已缴纳,1:已退还,2:已违约扣除}", position = 60)
    private String status;

    /**
     * 数量
     */
    @ApiModelProperty(name = "quantity", value = "数量", position = 70)
    private Integer quantity;

    /**
     * 保证金金额
     */
    @ApiModelProperty(name = "bondPrice", value = "保证金金额", position = 80)
    private BigDecimal bondPrice;

    /**
     * 支付金额
     */
    @ApiModelProperty(name = "payAmount", value = "支付金额", position = 90)
    private BigDecimal payAmount;

    /**
     * 支付方式 {0:余额支付,1:支付宝,2:微信}
     */
    @ApiModelProperty(name = "payType", value = "支付方式 {0:余额支付,1:支付宝,2:微信}", position = 100)
    private String payType;

    /**
     * 支付订单号
     */
    @ApiModelProperty(name = "payOrderCode", value = "支付订单号", position = 110)
    private String payOrderCode;

    /**
     * 支付状态 {0:待支付,1:已支付,2:支付失败}
     */
    @ApiModelProperty(name = "payStatus", value = "支付状态 {0:待支付,1:已支付,2:支付失败}", position = 120)
    private String payStatus;

    /**
     * 支付时间
     */
    @ApiModelProperty(name = "payDatetime", value = "支付时间", position = 130)
    private Date payDatetime;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 140)
    private Date updateDatetime;

    /**
     * 余额支付金额
     */
    @ApiModelProperty(name = "payBalanceAmount", value = "余额支付金额", position = 150)
    private BigDecimal payBalanceAmount;

    /**
     * 现金支付金额
     */
    @ApiModelProperty(name = "payCashAmount", value = "现金支付金额", position = 160)
    private BigDecimal payCashAmount;

    /**
     * 平台佣金收入
     */
    @ApiModelProperty(name = "commissionAmount", value = "平台佣金收入", position = 170)
    private BigDecimal commissionAmount;

    /**** Properties ****/

    /**
     * {0:已缴纳,1:已退还,2:已违约扣除}
     */
    @ApiModelProperty(name = "statusName", value = "{0:已缴纳,1:已退还,2:已违约扣除}")
    private String statusName;

    /**
     * 支付方式 {0:余额支付,1:支付宝,2:微信}
     */
    @ApiModelProperty(name = "payTypeName", value = "支付方式 {0:余额支付,1:支付宝,2:微信}")
    private String payTypeName;

    /**
     * 支付状态 {0:待支付,1:已支付,2:支付失败}
     */
    @ApiModelProperty(name = "payStatusName", value = "支付状态 {0:待支付,1:已支付,2:支付失败}")
    private String payStatusName;

    public String getStatusName() {
        if (StringUtils.isNotBlank(status)) {
            statusName = EDegressionAuctionBondStatus.getDegressionAuctionBondStatus(status).getValue();
        }

        return statusName;
    }

    public String getPayTypeName() {
        if (StringUtils.isNotBlank(payType)) {
            payTypeName = EDegressionAuctionBondPayType.getDegressionAuctionBondPayType(payType).getValue();
        }

        return payTypeName;
    }

    public String getPayStatusName() {
        if (StringUtils.isNotBlank(payStatus)) {
            payStatusName = EDegressionAuctionBondPayStatus.getDegressionAuctionBondPayStatus(payStatus).getValue();
        }

        return payStatusName;
    }

}
