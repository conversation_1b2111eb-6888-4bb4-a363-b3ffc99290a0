package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> ycj
 * @since : 2022-07-25 19:48
 */
@Data
public class CollectionRightRecordDetailRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 权益序号
     */
    @ApiModelProperty(name = "rightId", value = "权益序号", position = 20)
    private Long rightId;

    /**
     * 权益发行方序号
     */
    @ApiModelProperty(name = "rightCompanyId", value = "权益发行方序号", position = 30)
    private Long rightCompanyId;

    /**
     * 权益藏品
     */
    @ApiModelProperty(name = "collectionId", value = "权益藏品", position = 40)
    private Long collectionId;

    /**
     * 权益类型
     */
    @ApiModelProperty(name = "refType", value = "权益类型", position = 50)
    private String refType;

    /**
     * 关联序号
     */
    @ApiModelProperty(name = "refId", value = "关联序号", position = 60)
    private Long refId;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 70)
    private Date createDatetime;

}
