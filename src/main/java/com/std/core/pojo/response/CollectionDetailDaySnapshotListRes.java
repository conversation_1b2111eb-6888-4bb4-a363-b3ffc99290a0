package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> ycj
 * @since : 2022-04-16 15:56
 */
@Data
public class CollectionDetailDaySnapshotListRes {

    /**
     * 
     */
    @ApiModelProperty(name = "id", value = "", position = 10)
    private Long id;

    /**
     * 藏品型号id
     */
    @ApiModelProperty(name = "collectionDetailId", value = "藏品型号id", position = 20)
    private Long collectionDetailId;

    /**
     * 拥有者id
     */
    @ApiModelProperty(name = "ownerId", value = "拥有者id", position = 30)
    private Long ownerId;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 40)
    private Date createDatetime;

}
