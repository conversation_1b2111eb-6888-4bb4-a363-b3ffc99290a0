package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import com.std.core.enums.EPitConditionsStatus;

/**
 * <AUTHOR> ycj
 * @since : 2022-05-06 17:34
 */
@Data
public class PitConditionsListRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 售卖序号
     */
    @ApiModelProperty(name = "sellerId", value = "售卖序号", position = 20)
    private Long sellerId;

    /**
     * 坑位序号
     */
    @ApiModelProperty(name = "pitId", value = "坑位序号", position = 30)
    private Long pitId;

    /**
     * 意向用户
     */
    @ApiModelProperty(name = "userId", value = "意向用户", position = 40)
    private Long userId;

    /**
     * 用户
     */
    private User user;

    /**
     * 状态 {0:进行中,1:已预定,2:已成交,3:已违约}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:进行中,1:已预定,2:已成交,3:已违约}", position = 50)
    private String status;

    /**
     * 意向顺位
     */
    @ApiModelProperty(name = "orderNo", value = "意向顺位", position = 60)
    private Integer orderNo;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 70)
    private Date createDatetime;

    /**** Properties ****/

    /**
     * 状态 {0:进行中,1:已预定,2:已成交,3:已违约}
     */
    @ApiModelProperty(name = "statusName", value = "状态 {0:进行中,1:已预定,2:已成交,3:已违约}")
    private String statusName;

    public String getStatusName() {
        if (StringUtils.isNotBlank(status)) {
            statusName = EPitConditionsStatus.getPitConditionsStatus(status).getValue();
        }

        return statusName;
    }

}
