package com.std.core.pojo.response;

import com.std.common.base.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;


@Data
public class UserAccountRes extends BaseDo {

    @ApiModelProperty(name = "accountNumber", value = "账户编号")
    private String accountNumber;

    @ApiModelProperty(name = "currency", value = "币种")
    private String currency;

    @ApiModelProperty(name = "availableAmount", value = "可用余额")
    private BigDecimal availableAmount;

    @ApiModelProperty(name = "totalJourNum", value = "流水总计")
    private Integer totalJourNum;

    @ApiModelProperty(name = "lockAmount", value = "待入账金额")
    private BigDecimal lockAmount;

    @ApiModelProperty(name = "lockNum", value = "待入账数量")
    private Integer lockNum;

    @ApiModelProperty(name = "inAmount", value = "已入账金额")
    private BigDecimal inAmount;

    @ApiModelProperty(name = "inNum", value = "已入账数量")
    private Integer inNum;

    @ApiModelProperty(name = "withdrawalAmount", value = "提现中金额")
    private BigDecimal withdrawalAmount;

    @ApiModelProperty(name = "withdrawalNum", value = "提现中数量")
    private Integer withdrawalNum;

    @ApiModelProperty(name = "outAmount", value = "已提现金额")
    private BigDecimal outAmount;

    @ApiModelProperty(name = "outNum", value = "已提现数量")
    private Integer outNum;

    @ApiModelProperty(name = "recharge", value = "充值")
    private BigDecimal recharge;

    @ApiModelProperty(name = "rechargeNum", value = "已充值数量")
    private Integer rechargeNum;

    @ApiModelProperty(name = "consume", value = "消费金额")
    private BigDecimal consume;

    @ApiModelProperty(name = "consumeNum", value = "已消费数量")
    private Integer consumeNum;

    @ApiModelProperty(name = "distribution", value = "分销")
    private BigDecimal distribution;

    @ApiModelProperty(name = "distributionNum", value = "分销数量")
    private Integer distributionNum;

    @ApiModelProperty(name = "share", value = "拼单")
    private BigDecimal share;

    @ApiModelProperty(name = "shareNum", value = "拼单数量")
    private Integer shareNum;

    @ApiModelProperty(name = "acceptance", value = "兑换")
    private BigDecimal acceptance;

    @ApiModelProperty(name = "acceptanceNum", value = "兑换数量")
    private Integer acceptanceNum;

}
