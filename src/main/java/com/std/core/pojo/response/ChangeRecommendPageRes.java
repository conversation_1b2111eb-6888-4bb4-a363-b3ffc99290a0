package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import io.micrometer.core.instrument.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import com.std.core.enums.EChangeRecommendType;
import com.std.core.enums.EChangeRecommendStatus;

/**
 * <AUTHOR> ycj
 * @since : 2021-10-26 18:52
 */
@Data
public class ChangeRecommendPageRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称", position = 20)
    private String name;

    /**
     * 图片
     */
    @ApiModelProperty(name = "pic", value = "图片", position = 30)
    private String pic;

    /**
     * 系列序号
     */
    @ApiModelProperty(name = "seriesId", value = "系列序号", position = 40)
    private Long seriesId;

    /**
     * 类型 {0:百变大咖,1:盲盒}
     */
    @ApiModelProperty(name = "type", value = "类型 {0:百变大咖,1:盲盒}", position = 50)
    private String type;

    /**
     * 状态 {0:待上架,1:已上架,2:已下架}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:待上架,1:已上架,2:已下架}", position = 60)
    private String status;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 70)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updateName", value = "更新人名称", position = 80)
    private String updateName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updaterDatetime", value = "更新时间", position = 90)
    private Date updaterDatetime;

    /**** Properties ****/

    /**
     * 类型 {0:百变大咖,1:盲盒}
     */
    @ApiModelProperty(name = "typeName", value = "类型 {0:百变大咖,1:盲盒}")
    private String typeName;

    /**
     * 状态 {0:待上架,1:已上架,2:已下架}
     */
    @ApiModelProperty(name = "statusName", value = "状态 {0:待上架,1:已上架,2:已下架}")
    private String statusName;

    public String getTypeName() {
        if (StringUtils.isNotBlank(type)) {
            typeName = EChangeRecommendType.getChangeRecommendType(type).getValue();
        }

        return typeName;
    }

    public String getStatusName() {
        if (StringUtils.isNotBlank(status)) {
            statusName = EChangeRecommendStatus.getChangeRecommendStatus(status).getValue();
        }

        return statusName;
    }

}
