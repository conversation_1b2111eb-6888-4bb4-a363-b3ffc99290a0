package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> ycj
 * @since : 2022-08-29 21:51
 */
@Data
public class RobotCalculationRecordModifyRes {

    /**
     * 藏品序号
     */
    @ApiModelProperty(name = "id", value = "藏品序号", position = 10)
    private Long id;

    /**
     * 作品序号
     */
    @ApiModelProperty(name = "collectionId", value = "作品序号", position = 20)
    private Long collectionId;

    /**
     * 藏品名称
     */
    @ApiModelProperty(name = "collectionName", value = "藏品名称", position = 20)
    private String collectionName;

    /**
     * 模糊文件地址
     */
    @ApiModelProperty(name = "coverFileUrl", value = "模糊文件地址", position = 30)
    private String coverFileUrl;

    /**
     * tokenId
     */
    @ApiModelProperty(name = "tokenId", value = "tokenId", position = 30)
    private String tokenId;
}
