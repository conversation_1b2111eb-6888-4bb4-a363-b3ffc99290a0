package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import com.std.core.enums.EChipActivityType;
import com.std.core.enums.EChipActivityStatus;

/**
 * <AUTHOR> ycj
 * @since : 2022-08-24 10:39
 */
@Data
public class ChipActivityListRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 活动名称
     */
    @ApiModelProperty(name = "name", value = "活动名称", position = 20)
    private String name;

    /**
     * 类型 {0:无限制,1:单次}
     */
    @ApiModelProperty(name = "type", value = "类型 {0:无限制,1:单次}", position = 30)
    private String type;

    /**
     * 封面图地址
     */
    @ApiModelProperty(name = "coverFileUrl", value = "封面图地址", position = 20)
    private String coverFileUrl;

    @ApiModelProperty(name = "receiveFlag", value = "领取标识 0:可领取,1:超出领取上限", position = 20)
    private String receiveFlag;

}
