package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> ycj
 * @since : 2021-12-28 13:27
 */
@Data
public class CompanyEntityListRes {

    /**
     * 
     */
    @ApiModelProperty(name = "id", value = "", position = 10)
    private Long id;

    /**
     * 机构id
     */
    @ApiModelProperty(name = "companyId", value = "机构id", position = 20)
    private Long companyId;

    /**
     * 已发放数量
     */
    @ApiModelProperty(name = "sendQuantity", value = "已发放数量", position = 30)
    private Integer sendQuantity;

    /**
     * 实物名称
     */
    @ApiModelProperty(name = "name", value = "实物名称", position = 40)
    private String name;

    /**
     * 实物内容
     */
    @ApiModelProperty(name = "content", value = "实物内容", position = 50)
    private String content;

    /**
     * 实物图片
     */
    @ApiModelProperty(name = "imageUrl", value = "实物图片", position = 60)
    private String imageUrl;

}
