package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;


@Data
public class UserDetailRes {

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "id", value = "用户编号")
    private Long id;

    /**
     * 用户名
     */
    @ApiModelProperty(name = "nickname", value = "用户名")
    private String nickname;

    /**
     * 用户头像
     */
    @ApiModelProperty(name = "photo", value = "用户头像")
    private String photo;

    /**
     * 用户等级
     */
    @ApiModelProperty(name = "level", value = "用户等级")
    private String level;

    /**
     * 信用分
     */
    @ApiModelProperty(name = "creditScore", value = "信用分")
    private BigDecimal creditScore;

    /**
     * 消息
     */
    @ApiModelProperty(name = "message", value = "消息")
    private Integer message;

    /**
     * 参拍
     */
    @ApiModelProperty(name = "participateAuction", value = "参拍")
    private Integer participateAuction;

    /**
     * 拼单
     */
    @ApiModelProperty(name = "shareProduct", value = "拼单")
    private Integer shareProduct;

    /**
     * 购物车
     */
    @ApiModelProperty(name = "shareProduct", value = "购物车")
    private Integer carts;

    /**
     * 关注
     */
    @ApiModelProperty(name = "userNotice", value = "关注")
    private Integer userNotice;

    /**
     * 足迹
     */
    @ApiModelProperty(name = "footprint", value = "足迹")
    private Integer footprint;

    /**
     * 可用余额
     */
    @ApiModelProperty(name = "availableAmount", value = "可用余额")
    private BigDecimal availableAmount;

    /**
     * 提现中金额
     */
    @ApiModelProperty(name = "frozenAmount", value = "提现中金额")
    private BigDecimal frozenAmount;

    /**
     * 保证金
     */
    @ApiModelProperty(name = "ensureAmount", value = "保证金")
    private BigDecimal ensureAmount;

    /**
     * 优惠券
     */
    @ApiModelProperty(name = "coupon", value = "优惠券")
    private Integer coupon;

    /**
     * 购物券
     */
    @ApiModelProperty(name = "couponAmount", value = "购物券")
    private BigDecimal couponAmount;

    /**
     * 积分
     */
    @ApiModelProperty(name = "accumulatePoints", value = "积分")
    private BigDecimal accumulatePoints;

    /**
     * 待付款
     */
    @ApiModelProperty(name = "toBePaid", value = "待付款")
    private Integer toBePaid;

    /**
     * 待发货
     */
    @ApiModelProperty(name = "shareProduct", value = "待发货")
    private Integer toBeDelivery;

    /**
     * 待收货
     */
    @ApiModelProperty(name = "toBeReceiptDelivery", value = "待收货")
    private Integer toBeReceiptDelivery;

    /**
     * 已收货
     */
    @ApiModelProperty(name = "receiptDelivery", value = "已收货")
    private Integer receiptDelivery;

    /**
     * 已完成
     */
    @ApiModelProperty(name = "accomplish", value = "已完成")
    private Integer accomplish;

    /**
     * 评价
     */
    @ApiModelProperty(name = "comments", value = "评价")
    private Integer comments;

    /**
     * 售后
     */
    @ApiModelProperty(name = "afterSales", value = "售后")
    private Integer afterSales;

    @ApiModelProperty(name = "inviteCode", value = "邀请码")
    private String inviteCode;

    @ApiModelProperty(name = "introduce", value = "个人介绍")
    private String introduce;


    @ApiModelProperty(name = "blockAddress", value = "区块链地址")
    private String blockAddress;

}
