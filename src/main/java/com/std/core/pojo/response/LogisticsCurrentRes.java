package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> xieyj
 * @since : 2020/9/24 19:02
 */
@Data
public class LogisticsCurrentRes {

    @ApiModelProperty(name = "logisticCode", value = "物流编号")
    private String logisticCode;

    @ApiModelProperty(name = "shipperCode", value = "物流公司编号")
    private String shipperCode;

    @ApiModelProperty(name = "shipperName", value = "物流公司名称")
    private String shipperName;

    @ApiModelProperty(name = "address", value = "收货地址")
    private String address;

    @ApiModelProperty(name = "state", value = "状态（数据字典kdn_logistics_status）")
    private String state;

    @ApiModelProperty(name = "stateEx", value = "增值物流状态（数据字典kdn_logistics_status）")
    private String stateEx;

    @ApiModelProperty(name = "ebusinessID", value = "业务编号")
    private String ebusinessID;

    @ApiModelProperty(name = "success", value = "是否成功")
    private String success;

    @ApiModelProperty(name = "location", value = "位置")
    private String location;

    @ApiModelProperty(name = "traces", value = "轨迹")
    private List<Traces> traces;
}


@Data
class Traces {

    @ApiModelProperty(name = "action", value = "当前状态（数据字典kdn_logistics_status）")
    private String action;

    @ApiModelProperty(name = "location", value = "位置")
    private String location;

    @ApiModelProperty(name = "acceptStation", value = "描述")
    private String acceptStation;

    @ApiModelProperty(name = "acceptTime", value = "时间")
    private String acceptTime;
}
