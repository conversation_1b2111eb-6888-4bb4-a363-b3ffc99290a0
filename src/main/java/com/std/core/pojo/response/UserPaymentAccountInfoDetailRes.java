package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> ycj
 * @since : 2022-04-19 19:10
 */
@Data
public class UserPaymentAccountInfoDetailRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 
     */
    @ApiModelProperty(name = "name", value = "", position = 30)
    private String name;

    /**
     * 法人证件类型
     */
    @ApiModelProperty(name = "legalLicenceType", value = "法人证件类型 数据字典：user_payment_account_licence_type", position = 80)
    private String legalLicenceType;

    /**
     * 法人证件号
     */
    @ApiModelProperty(name = "legalLicenceNo", value = "法人证件号", position = 90)
    private String legalLicenceNo;

    /**
     * 证件照片1
     */
    @ApiModelProperty(name = "legalLicenceFrontUrl", value = "证件照片1", position = 100)
    private String legalLicenceFrontUrl;

    /**
     * 证件照片2
     */
    @ApiModelProperty(name = "legalLicenceBackUrl", value = "证件照片2", position = 110)
    private String legalLicenceBackUrl;

    /**
     * 真实姓名
     */
    @ApiModelProperty(name = "legalRealName", value = "真实姓名", position = 120)
    private String legalRealName;

    /**
     * 手机号
     */
    @ApiModelProperty(name = "legalMobile", value = "手机号", position = 130)
    private String legalMobile;

    /**
     * 结算银行卡号
     */
    @ApiModelProperty(name = "settleCardNo", value = "结算银行卡号", position = 140)
    private String settleCardNo;

    /**
     * 结算银行
     */
    @ApiModelProperty(name = "settleBankCode", value = "结算银行", position = 150)
    private String settleBankCode;

    /**
     * 结算银行名称
     */
    @ApiModelProperty(name = "settleBankName", value = "结算银行名称", position = 150)
    private String settleBankName;

    /**
     * 状态(0=审核中,1=审核通过,2=审核失败,3=已解约)
     */
    @ApiModelProperty(name = "status", value = "状态(-1=未申请,0=审核中,1=审核通过,2=审核失败,3=已解约)", position = 220)
    private String status;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注", position = 250)
    private String remark;

    @ApiModelProperty(name = "settleBankInfo",value = "收款卡号")
    private String settleBankInfo;

    private String channelType;

    /**
     * 真实姓名
     */
    @ApiModelProperty(name = "realName", value = "真实姓名", position = 40)
    private String realName;

    /**
     * 身份证号码
     */
    @ApiModelProperty(name = "idNo", value = "身份证号码", position = 50)
    private String idNo;
}
