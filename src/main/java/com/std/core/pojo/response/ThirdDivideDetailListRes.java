package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> ycj
 * @since : 2022-03-27 20:14
 */
@Data
public class ThirdDivideDetailListRes {

    /**
     * 
     */
    @ApiModelProperty(name = "id", value = "", position = 10)
    private Long id;

    /**
     * 分账类型(0=渠道商 1=平台)
     */
    @ApiModelProperty(name = "type", value = "分账类型(0=渠道商 1=平台)", position = 20)
    private String type;

    /**
     * 分账订单类型
     */
    @ApiModelProperty(name = "orderType", value = "分账订单类型", position = 30)
    private String orderType;

    /**
     * 订单号
     */
    @ApiModelProperty(name = "orderId", value = "订单号", position = 40)
    private Long orderId;

    /**
     * 支付订单号
     */
    @ApiModelProperty(name = "payOrderNo", value = "支付订单号", position = 50)
    private String payOrderNo;

    /**
     * 支付三方订单号
     */
    @ApiModelProperty(name = "payThirdNo", value = "支付三方订单号", position = 60)
    private String payThirdNo;

    /**
     * 分账金额
     */
    @ApiModelProperty(name = "amount", value = "分账金额", position = 70)
    private BigDecimal amount;

    /**
     * 分账时间
     */
    @ApiModelProperty(name = "createTime", value = "分账时间", position = 80)
    private Date createTime;

    /**
     * 状态(0=待处理，1=处理中 2=成功 3=失败)
     */
    @ApiModelProperty(name = "status", value = "状态(0=待处理，1=处理中 2=成功 3=失败)", position = 90)
    private String status;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 100)
    private Date updateDatetime;

}
