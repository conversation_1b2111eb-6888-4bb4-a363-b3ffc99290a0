package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import com.std.core.enums.ECollectionDetailRecordStatus;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> xieyj
 * @since : 2021-07-08 09:45
 */
@Data
public class CollectionDetailRecordDetailRes {

    /**
     * 
     */
    @ApiModelProperty(name = "id", value = "", position = 10)
    private Long id;

    /**
     * 藏品id
     */
    @ApiModelProperty(name = "collectionDetailId", value = "藏品id", position = 20)
    private Long collectionDetailId;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号", position = 30)
    private Long userId;

    /**
     * 用户
     */
    private User user;

    /**
     * 拥有者id
     */
    @ApiModelProperty(name = "ownerId", value = "拥有者id", position = 40)
    private Long ownerId;

    /**
     * 成交价格
     */
    @ApiModelProperty(name = "price", value = "成交价格", position = 50)
    private BigDecimal price;

    /**
     * 现在拥有状态 0:否,1:是
     */
    @ApiModelProperty(name = "status", value = "现在拥有状态 0:否,1:是 ", position = 60)
    private String status;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 70)
    private Date createDatetime;

    /**** Properties ****/

    /**
     * 现在拥有状态 0:否,1:是
     */
    @ApiModelProperty(name = "statusName", value = "现在拥有状态 0:否,1:是 ")
    private String statusName;

    public String getStatusName() {
        if (StringUtils.isNotBlank(status)) {
            statusName = ECollectionDetailRecordStatus.getCollectionDetailRecordStatus(status).getValue();
        }

        return statusName;
    }

}
