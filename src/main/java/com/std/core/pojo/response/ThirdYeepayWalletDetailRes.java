package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import com.std.core.enums.EThirdYeepayWalletCertificateType;
import com.std.core.enums.EThirdYeepayWalletWalletCategory;
import com.std.core.enums.EThirdYeepayWalletStatus;

/**
 * <AUTHOR> wzh
 * @since : 2023-08-15 15:06
 */
@Data
public class ThirdYeepayWalletDetailRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 用户
     */
    @ApiModelProperty(name = "userId", value = "用户", position = 20)
    private Long userId;

    /**
     * 用户
     */
    private User user;

    /**
     * 商户用户ID
     */
    @ApiModelProperty(name = "userMerchantNo", value = "商户用户ID", position = 30)
    private String userMerchantNo;

    /**
     * 姓名
     */
    @ApiModelProperty(name = "name", value = "姓名", position = 40)
    private String name;

    /**
     * 证件类型 {0:身份证}
 
     */
    @ApiModelProperty(name = "certificateType", value = "证件类型 {0:身份证}", position = 50)
    private String certificateType;

    /**
     * 证件号码
     */
    @ApiModelProperty(name = "certificateNo", value = "证件号码", position = 60)
    private String certificateNo;

    /**
     * 手机号
     */
    @ApiModelProperty(name = "mobile", value = "手机号", position = 70)
    private String mobile;

    /**
     * 商户请求流水号
     */
    @ApiModelProperty(name = "requestNo", value = "商户请求流水号", position = 80)
    private String requestNo;

    /**
     * 易宝唯一订单号
     */
    @ApiModelProperty(name = "businessNo", value = "易宝唯一订单号", position = 90)
    private String businessNo;

    /**
     * 商户编号 易宝支付分配的商户唯一标识
     */
    @ApiModelProperty(name = "merchantNo", value = "商户编号 易宝支付分配的商户唯一标识", position = 100)
    private String merchantNo;

    /**
     * 钱包账户ID
     */
    @ApiModelProperty(name = "walletUserNo", value = "钱包账户ID", position = 110)
    private String walletUserNo;

    /**
     * 钱包账户等级{ONE_CATEGORY:一类,TWO_CATEGORY:二类,THREE_CATEGORY:三类}
     */
    @ApiModelProperty(name = "walletCategory", value = "钱包账户等级{ONE_CATEGORY:一类,TWO_CATEGORY:二类,THREE_CATEGORY:三类}", position = 120)
    private String walletCategory;

    /**
     * 状态 {0:申请中,1:申请成功,2:申请失败}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:申请中,1:申请成功,2:申请失败}", position = 130)
    private String status;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 140)
    private Date createDatetime;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 150)
    private Date updateDatetime;

    /**** Properties ****/

    /**
     * 证件类型 {0:身份证}
 
     */
    @ApiModelProperty(name = "certificateTypeName", value = "证件类型 {0:身份证}")
    private String certificateTypeName;

    /**
     * 钱包账户等级{ONE_CATEGORY:一类,TWO_CATEGORY:二类,THREE_CATEGORY:三类}
     */
    @ApiModelProperty(name = "walletCategoryName", value = "钱包账户等级{ONE_CATEGORY:一类,TWO_CATEGORY:二类,THREE_CATEGORY:三类}")
    private String walletCategoryName;

    /**
     * 状态 {0:申请中,1:申请成功,2:申请失败}
     */
    @ApiModelProperty(name = "statusName", value = "状态 {0:申请中,1:申请成功,2:申请失败}")
    private String statusName;

    public String getCertificateTypeName() {
        if (StringUtils.isNotBlank(certificateType)) {
            certificateTypeName = EThirdYeepayWalletCertificateType.getThirdYeepayWalletCertificateType(certificateType).getValue();
        }

        return certificateTypeName;
    }

    public String getWalletCategoryName() {
        if (StringUtils.isNotBlank(walletCategory)) {
            walletCategoryName = EThirdYeepayWalletWalletCategory.getThirdYeepayWalletWalletCategory(walletCategory).getValue();
        }

        return walletCategoryName;
    }

    public String getStatusName() {
        if (StringUtils.isNotBlank(status)) {
            statusName = EThirdYeepayWalletStatus.getThirdYeepayWalletStatus(status).getValue();
        }

        return statusName;
    }

}
