package com.std.core.pojo.response;

import com.std.core.enums.ECollectionBuyOrderPayStatus;
import com.std.core.enums.ECollectionBuyOrderPayType;
import com.std.core.pojo.domain.User;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR> ycj
 * @since : 2021-11-04 16:01
 */
@Data
public class CollectionBuyOrderPageRes {

    /**
     *
     */
    @ApiModelProperty(name = "id", value = "", position = 10)
    private Long id;

    /**
     * 业务类型 0=单品购买
     */
    @ApiModelProperty(name = "bizType", value = "业务类型 0=单品购买", position = 20)
    private String bizType;

    /**
     * 业务关联id
     */
    @ApiModelProperty(name = "bizId", value = "业务关联id", position = 30)
    private Long bizId;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号", position = 40)
    private Long userId;

    /**
     * 用户
     */
    private User user;

    /**
     * 作品id
     */
    @ApiModelProperty(name = "collectionId", value = "作品id", position = 50)
    private Long collectionId;

    /**
     * 单品型号id
     */
    @ApiModelProperty(name = "collectionDetailId", value = "单品型号id", position = 60)
    private Long collectionDetailId;

    /**
     * 状态（0=待支付 1=已支付 2=支付取消）
     */
    @ApiModelProperty(name = "status", value = "状态（0=待支付 1=已支付 2=支付取消）", position = 70)
    private String status;

    /**
     * 价格
     */
    @ApiModelProperty(name = "price", value = "价格", position = 80)
    private BigDecimal price;

    /**
     * 数量
     */
    @ApiModelProperty(name = "quantity", value = "数量", position = 90)
    private Integer quantity;

    /**
     * 支付金额
     */
    @ApiModelProperty(name = "payAmount", value = "支付金额", position = 100)
    private BigDecimal payAmount;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createTime", value = "创建时间", position = 110)
    private Date createTime;

    /**
     * 支付方式 {0:余额支付,1:支付宝,2:微信}
     */
    @ApiModelProperty(name = "payType", value = "支付方式 {0:余额支付,1:支付宝,2:微信}", position = 120)
    private String payType;

    /**
     * 支付订单号
     */
    @ApiModelProperty(name = "payOrderCode", value = "支付订单号", position = 130)
    private String payOrderCode;

    /**
     * 支付状态 {0:待支付,1:已支付,2:支付失败}
     */
    @ApiModelProperty(name = "payStatus", value = "支付状态 {0:待支付,1:已支付,2:支付失败}", position = 140)
    private String payStatus;

    /**
     * 支付时间
     */
    @ApiModelProperty(name = "payDatetime", value = "支付时间", position = 150)
    private Date payDatetime;

    /**
     * 余额支付金额
     */
    @ApiModelProperty(name = "payBalanceAmount", value = "余额支付金额", position = 160)
    private BigDecimal payBalanceAmount;

    /**
     * 现金支付金额
     */
    @ApiModelProperty(name = "payCashAmount", value = "现金支付金额", position = 170)
    private BigDecimal payCashAmount;

    /**
     * 平台佣金收入
     */
    @ApiModelProperty(name = "commissionAmount", value = "平台佣金收入", position = 180)
    private BigDecimal commissionAmount;

    /**** Properties ****/

    /**
     * 支付方式 {0:余额支付,1:支付宝,2:微信}
     */
    @ApiModelProperty(name = "payTypeName", value = "支付方式 {\"0\":\"余额支付\",\"1\":\"支付宝\",\"2\":\"微信\"}")
    private String payTypeName;

    /**
     * 支付状态 {0:待支付,1:已支付,2:支付失败}
     */
    @ApiModelProperty(name = "payStatusName", value = "支付状态 {\"0\":\"待支付\",\"1\":\"已支付\",\"2\":\"支付失败\"}")
    private String payStatusName;

    public String getPayTypeName() {
        if (StringUtils.isNotBlank(payType)) {
            payTypeName = ECollectionBuyOrderPayType.getCollectionBuyOrderPayType(payType).getValue();
        }

        return payTypeName;
    }

    public String getPayStatusName() {
        if (StringUtils.isNotBlank(payStatus)) {
            payStatusName = ECollectionBuyOrderPayStatus.getCollectionBuyOrderPayStatus(payStatus).getValue();
        }

        return payStatusName;
    }

}
