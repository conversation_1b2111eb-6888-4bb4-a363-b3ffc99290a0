package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> wzh
 * @since : 2023-06-01 18:43
 */
@Data
public class MeetingPresentersDetailRes {

    /**
     * 会议序号
     */
    @ApiModelProperty(name = "meetingId", value = "会议序号", position = 10)
    private Long meetingId;

    /**
     * 主播序号
     */
    @ApiModelProperty(name = "userId", value = "主播序号", position = 20)
    private Long userId;

    /**
     * 用户
     */
    private User user;

}
