package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import com.std.core.enums.EKeywordReaction;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> xieyj
 * @since : 2021-07-05 21:02
 */
@Data
public class KeywordListRes {

    /**
     * 编号（自增长）
     */
    @ApiModelProperty(name = "id", value = "编号（自增长）", position = 10)
    private Integer id;

    /**
     * 词语
     */
    @ApiModelProperty(name = "word", value = "词语", position = 20)
    private String word;

    /**
     * 作用等级
     */
    @ApiModelProperty(name = "level", value = "作用等级", position = 30)
    private String level;

    /**
     * 反应 1:直接拦截,2:替换,3:审核
     */
    @ApiModelProperty(name = "reaction", value = "反应 1:直接拦截,2:替换,3:审核", position = 40)
    private String reaction;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 50)
    private Long updater;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 60)
    private Date updateDatetime;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注", position = 70)
    private String remark;

    /**** Properties ****/

    /**
     * 反应 1:直接拦截,2:替换,3:审核
     */
    @ApiModelProperty(name = "reactionName", value = "反应 1:直接拦截,2:替换,3:审核")
    private String reactionName;

    public String getReactionName() {
        if (StringUtils.isNotBlank(reaction)) {
            reactionName = EKeywordReaction.getKeywordReaction(reaction).getValue();
        }

        return reactionName;
    }

}
