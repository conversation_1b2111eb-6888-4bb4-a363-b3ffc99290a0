package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class XmlyJsTicketSignRes {

    @ApiModelProperty(name = "appId", value = "appId")
    private String appId;

    @ApiModelProperty(name = "js_ticket", value = "js_ticket")
    private String js_ticket;

    @ApiModelProperty(name = "nonce", value = "随机数")
    private String nonce;

    @ApiModelProperty(name = "timestamp", value = "时间戳")
    private String timestamp;

    @ApiModelProperty(name = "signature", value = "签名")
    private String signature;
}
