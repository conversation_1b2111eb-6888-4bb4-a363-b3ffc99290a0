package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 用户每月免费抽取次数
 *
 * <AUTHOR> xieyj
 * @since : 2023-02-14 14:45
 */
@Data
public class BlindboxUser extends BaseDo {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号", position = 20)
    private Long userId;

    /**
     * 用户
     */
    @ApiModelProperty(hidden = true)
    private User user;

    /**
     * 用户关键信息（手机号/身份证/姓名）
     */
    private String keywords;

    /**
     * 数量
     */
    @ApiModelProperty(name = "quantity", value = "数量", position = 30)
    private Integer quantity;



}
