package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import com.std.core.pojo.response.IntegralExchangeDetailPageRes;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 兑换记录
 *
 * <AUTHOR> ycj
 * @since : 2022-04-26 05:55
 */
@Data
public class IntegralExchange extends BaseDo {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 用户序号
     */
    @ApiModelProperty(name = "userId", value = "用户序号", position = 20)
    private Long userId;

    /**
     * 用户
     */
    @ApiModelProperty(hidden = true)
    private User user;

    /**
     * 用户关键信息（手机号/身份证/姓名）
     */
    private String keywords;

    /**
     * 获得积分数
     */
    @ApiModelProperty(name = "integralPrice", value = "获得积分数", position = 30)
    private BigDecimal integralPrice;

    /**
     * 单次兑换藏品数
     */
    @ApiModelProperty(name = "quantity", value = "单次兑换藏品数", position = 40)
    private Integer quantity;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 50)
    private Date createDatetime;

    @ApiModelProperty(name = "collectionName",value = "藏品名")
    private String collectionName;

    @ApiModelProperty(name = "collectionId",value = "作品序号")
    private Long collectionId;

    private List<IntegralExchangeDetail> detailList;
}
