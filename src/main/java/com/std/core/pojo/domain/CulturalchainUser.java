package com.std.core.pojo.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.std.common.base.BaseDo;
import com.std.core.pojo.response.SmsUnreadMessagesRes;
import com.std.core.util.InviteCodeUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 用户
 *
 * <AUTHOR> haiqingzheng
 * @since : 2020-03-02 14:09
 */
@Data
public class CulturalchainUser extends BaseDo {

    @ApiModelProperty(name = "appId", value = "编号")
    private String appId;

    @ApiModelProperty(name = "hash", value = "用户hash")
    private String hash;

    @ApiModelProperty(name = "nickname", value = "昵称")
    private String nickname;

    @ApiModelProperty(name = "avatarURL", value = "头像")
    private String avatarURL;

    @ApiModelProperty(name = "mobile", value = "手机号")
    private String mobile;

    @ApiModelProperty(name = "culturalchainUserIdentity", value = "证件信息")
    private CulturalchainUserIdentity culturalchainUserIdentity;
}
