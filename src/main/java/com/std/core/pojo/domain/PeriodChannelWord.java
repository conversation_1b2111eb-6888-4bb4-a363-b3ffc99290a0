package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 期数作品机构口令
 *
 * <AUTHOR> xieyj
 * @since : 2022-06-07 09:45
 */
@Data
public class PeriodChannelWord extends BaseDo {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 作品id
     */
    @ApiModelProperty(name = "periodId", value = "作品id", position = 20)
    private Long periodId;

    /**
     * 渠道id
     */
    @ApiModelProperty(name = "channelId", value = "渠道id", position = 30)
    private Long channelId;

    /**
     * 口令
     */
    @ApiModelProperty(name = "word", value = "口令", position = 40)
    private String word;

    /**
     * 状态(0=可使用 1=已作废)
     */
    @ApiModelProperty(name = "status", value = "状态(0=可使用 1=已作废)", position = 50)
    private String status;

    /**
     * 状态集合
     */
    private List<String> statusList;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 60)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称", position = 70)
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 80)
    private Date updateDatetime;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注", position = 90)
    private String remark;

    //=====db properties=====
    /**
     * 期数作品名称
     */
    @ApiModelProperty(name = "periodName", value = "期数作品名称", position = 20)
    private String periodName;

    /**
     * 渠道名称
     */
    @ApiModelProperty(name = "channelName", value = "渠道名称", position = 30)
    private String channelName;

}
