package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import com.std.core.enums.ECollectionDetailStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 藏品
 *
 * <AUTHOR> xieyj
 * @since : 2021-07-08 09:42
 */
@Data
public class CollectionDetailTransferModify extends BaseDo {


    /**
     * 拥有者id
     */
    @ApiModelProperty(name = "ownerId", value = "拥有者id", position = 50)
    private Long ownerId;


    /**
     * 状态集合
     */
    private List<Long> idList;


    /**
     * 数量
     */
    @ApiModelProperty(name = "count", value = "数量", position = 50)
    private int count;

    /**
     * 状态
     */
    @ApiModelProperty(name = "status", value = "状态", position = 50)
    private String status;
}
