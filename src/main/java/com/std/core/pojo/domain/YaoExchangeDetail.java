package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 爻转化订单明细
 *
 * <AUTHOR> ycj
 * @since : 2022-11-10 17:58
 */
@Data
public class YaoExchangeDetail extends BaseDo {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 用户
     */
    @ApiModelProperty(name = "userId", value = "用户", position = 20)
    private Long userId;

    /**
     * 用户
     */
    @ApiModelProperty(hidden = true)
    private User user;

    /**
     * 用户关键信息（手机号/身份证/姓名）
     */
    private String keywords;

    /**
     * 兑换序号
     */
    @ApiModelProperty(name = "exchangeId", value = "兑换序号", position = 30)
    private Long exchangeId;

    /**
     * 作品序号
     */
    @ApiModelProperty(name = "collectionId", value = "作品序号", position = 40)
    private Long collectionId;

    /**
     * 作品序号
     */
    @ApiModelProperty(name = "collectionName", value = "作品名称", position = 40)
    private String collectionName;
    /**
     * 藏品序号
     */
    @ApiModelProperty(name = "collectionDetailId", value = "藏品序号", position = 50)
    private Long collectionDetailId;

    /**
     * 代币编号
     */
    @ApiModelProperty(name = "tokenId", value = "代币编号", position = 60)
    private String tokenId;

    /**
     * 阴爻数
     */
    @ApiModelProperty(name = "yinYao", value = "阴爻数", position = 70)
    private BigDecimal yinYao;

    /**
     * 阳爻数
     */
    @ApiModelProperty(name = "yangYao", value = "阳爻数", position = 80)
    private BigDecimal yangYao;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 90)
    private Date createDatetime;



}
