package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import com.std.core.enums.EMeetingRecordStatus;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 直播会议参与记录
 *
 * <AUTHOR> wzh
 * @since : 2023-05-04 10:14
 */
@Data
public class MeetingRecord extends BaseDo {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 会议ID
     */
    @ApiModelProperty(name = "meetingId", value = "会议ID", position = 20)
    private Long meetingId;

    /**
     * 参与者类型 0:用户,1:NPC
     */
    @ApiModelProperty(name = "participantType", value = "参与者类型 0:用户,1:NPC", position = 30)
    private String participantType;

    /**
     * 参与者类型 0:用户,1:NPC
     */
    @ApiModelProperty(name = "participantTypeList", value = "参与者类型 0:用户,1:NPC", position = 30)
    private List<String> participantTypeList;

    /**
     * 参与者ID
     */
    @ApiModelProperty(name = "participantId", value = "参与者ID", position = 30)
    private Long participantId;

    /**
     * 会议密码
     */
    @ApiModelProperty(name = "pwd", value = "会议密码", position = 40)
    private String pwd;

    /**
     * 状态 {0:会议中,1:已退出}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:会议中,1:已退出}", position = 40)
    private String status;

    /**
     * 状态集合
     */
    private List<String> statusList;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 50)
    private Date createDatetime;

    @ApiModelProperty(name = "exitDatetime", value = "退出时间", position = 50)
    private Date exitDatetime;

    /**** Properties ****/

    /**
     * 状态 {0:会议中,1:已退出}
     */
    @ApiModelProperty(name = "statusName", value = "状态 {0:会议中,1:已退出,2:已结束}")
    private String statusName;

    public String getStatusName() {
      if (StringUtils.isNotBlank(status)) {
        statusName = EMeetingRecordStatus.getMeetingRecordStatus(status).getValue();
      }

      return statusName;
    }

    @ApiModelProperty(hidden = true)
    private User user;

    @ApiModelProperty(name = "meetingName", value = "会议名", position = 20)
    private String meetingName;

}
