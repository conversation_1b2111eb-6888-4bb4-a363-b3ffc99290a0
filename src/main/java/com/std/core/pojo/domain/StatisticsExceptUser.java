package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import com.std.core.enums.EStatisticsExceptUserType;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 统计排除用户
 *
 * <AUTHOR> ycj
 * @since : 2022-04-08 15:22
 */
@Data
public class StatisticsExceptUser extends BaseDo {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 类型 {0:统计余额}
     */
    @ApiModelProperty(name = "type", value = "类型 {0:统计余额}", position = 20)
    private String type;

    /**
     * 排除用户
     */
    @ApiModelProperty(name = "userId", value = "排除用户", position = 30)
    private Long userId;

    /**
     * 用户
     */
    @ApiModelProperty(hidden = true)
    private User user;

    /**
     * 用户关键信息（手机号/身份证/姓名）
     */
    private String keywords;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 40)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称", position = 50)
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 60)
    private Date updateDatetime;

    /**** Properties ****/

    /**
     * 类型 {0:统计余额}
     */
    @ApiModelProperty(name = "typeName", value = "类型 {0:统计余额}")
    private String typeName;

    public String getTypeName() {
      if (StringUtils.isNotBlank(type)) {
        typeName = EStatisticsExceptUserType.getStatisticsExceptUserType(type).getValue();
      }

      return typeName;
    }



}
