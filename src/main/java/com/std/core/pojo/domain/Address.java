package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 用户地址
 *
 * <AUTHOR> xieyj
 * @since : 2020-07-17 10:40
 */
@Data
public class Address extends BaseDo {

    /**
     * 自增主键
     */
    @ApiModelProperty(name = "id", value = "自增主键", position = 10)
    private Long id;

    /**
     * 用户uid
     */
    @ApiModelProperty(name = "userId", value = "用户uid", position = 20)
    private Long userId;
    /**
     * 用户名称
     */
    @ApiModelProperty(name = "userName", value = "用户名称", position = 25)
    private String userName;
    /**
     * 收货人姓名
     */
    @ApiModelProperty(name = "name", value = "收货人姓名", position = 30)
    private String name;

    /**
     * 用户类型0 用户 1供应商
     */
    @ApiModelProperty(name = "userType", value = "用户类型", position = 31)
    private String userType;

    /**
     * 是否默认地址1是，0否,一个用户只能有一个默认地址
     */
    @ApiModelProperty(name = "isDefault", value = "是否默认地址1是，0否,一个用户只能有一个默认地址", position = 40)
    private String isDefault;

    /**
     * 省份名称
     */
    @ApiModelProperty(name = "province", value = "省份名称", position = 50)
    private String province;

    /**
     * 省份ID对应area表中的id
     */
    @ApiModelProperty(name = "provinceId", value = "省份ID对应area表中的id", position = 60)
    private Long provinceId;

    /**
     * 城市名称
     */
    @ApiModelProperty(name = "city", value = "城市名称", position = 70)
    private String city;

    /**
     * 城市ID对应area表中的id
     */
    @ApiModelProperty(name = "cityId", value = "城市ID对应area表中的id", position = 80)
    private Long cityId;

    /**
     * 区/县
     */
    @ApiModelProperty(name = "county", value = "区/县", position = 90)
    private String county;

    /**
     * 区/县ID对应area表中的id
     */
    @ApiModelProperty(name = "countyId", value = "区/县ID对应area表中的id", position = 100)
    private Long countyId;

    /**
     * 详细地址
     */
    @ApiModelProperty(name = "address", value = "详细地址", position = 110)
    private String address;

    /**
     * 电话收货人电话
     */
    @ApiModelProperty(name = "phone", value = "电话收货人电话", position = 120)
    private String phone;

    /**
     * 1启用, 0禁用
     */
    @ApiModelProperty(name = "status", value = "1启用, 0禁用", position = 130)
    private String status;

    /**
     * 用户电话
     */
    @ApiModelProperty(name = "mobile", value = "收货人手机", position = 140)
    private String mobile;
    /**
     * 添加时间
     */
    @ApiModelProperty(name = "postTime", value = "添加时间", position = 150)
    private Date postTime;
    /**
     * 用户信息
     */
    @ApiModelProperty(name = "user", value = "用户信息", hidden = true)
    private User user;

    @ApiModelProperty(name = "keywords", value = "模糊查询关键字")
    private String keywords;
}
