package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import com.std.core.enums.EChargeStatus;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * 充值订单
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-26 10:01
 */
@Data
public class Charge extends BaseDo {

    /**
     * 编号
     */
    @ApiModelProperty(name = "id", value = "编号")
    private Long id;

    /**
     * 账户编号
     */
    @ApiModelProperty(name = "accountNumber", value = "账户编号")
    private String accountNumber;

    /**
     * 账户类型
     */
    @ApiModelProperty(name = "accountType", value = "账户类型")
    private String accountType;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号")
    private Long userId;

    /**
     * 用户类型
     */
    @ApiModelProperty(name = "userKind", value = "用户类型")
    private String userKind;

    /**
     * 充值金额
     */
    @ApiModelProperty(name = "amount", value = "充值金额")
    private BigDecimal amount;

    /**
     * 币种
     */
    @ApiModelProperty(name = "currency", value = "币种")
    private String currency;

    /**
     * 关联业务类型
     */
    @ApiModelProperty(name = "bizType", value = "关联业务类型")
    private String bizType;

    /**
     * 关联业务备注
     */
    @ApiModelProperty(name = "bizNote", value = "关联业务备注")
    private String bizNote;

    /**
     * 关联订单号
     */
    @ApiModelProperty(name = "bizNo", value = "关联订单号")
    private String bizNo;

    /**
     * 是否开票
     */
    @ApiModelProperty(name = "billFlag", value = "是否开票（0否 1是）")
    private String billFlag;

    /**
     * 状态（1待支付 2支付失败 3支付成功）
     */
    @ApiModelProperty(name = "status", value = "状态（1待支付 2支付失败 3支付成功 4支付超时）")
    private String status;

    /**
     * 申请人
     */
    @ApiModelProperty(name = "applyUser", value = "申请人")
    private Long applyUser;

    /**
     * 申请说明
     */
    @ApiModelProperty(name = "applyNote", value = "申请说明")
    private String applyNote;

    /**
     * 申请时间
     */
    @ApiModelProperty(name = "applyDatetime", value = "申请时间")
    private Date applyDatetime;

    /**
     * 支付渠道类型（内部账inner 人工线下off_line 支付宝支付alipay 微信公众号支付wechat）
     */
    @ApiModelProperty(name = "channelType", value = "支付渠道类型（内部账inner 人工线下off_line 支付宝支付alipay 微信公众号支付wechat）")
    private String channelType;

    /**
     * 支付渠道银行
     */
    @ApiModelProperty(name = "channelBank", value = "支付渠道银行")
    private String channelBank;

    /**
     * 支付渠道账号信息
     */
    @ApiModelProperty(name = "channelAccountInfo", value = "支付渠道账号信息")
    private String channelAccountInfo;

    /**
     * 支付渠道账号
     */
    @ApiModelProperty(name = "channelAccountNumber", value = "支付渠道账号")
    private String channelAccountNumber;

    /**
     * 支付渠道单号
     */
    @ApiModelProperty(name = "channelOrder", value = "支付渠道单号")
    private String channelOrder;

    /**
     * 订单分组组号（信息代表）
     */
    @ApiModelProperty(name = "payGroup", value = "订单分组组号（信息代表）")
    private String payGroup;

    /**
     * 支付回录人
     */
    @ApiModelProperty(name = "payUser", value = "支付回录人")
    private String payUser;

    /**
     * 支付回录说明
     */
    @ApiModelProperty(name = "payNote", value = "支付回录说明")
    private String payNote;

    /**
     * 支付回录时间
     */
    @ApiModelProperty(name = "payDatetime", value = "支付回录时间")
    private Date payDatetime;

    //DB Properties

    /**
     * 用户信息
     */
    @ApiModelProperty(name = "userInfo", value = "用户信息")
    private User userInfo;

    /**
     * 编号
     */
    @ApiModelProperty(name = "idForQuery", value = "编号")
    private Long idForQuery;

    /**
     * 申请时间起
     */
    @ApiModelProperty(name = "applyDatetimeStart", value = "申请时间起")
    private Date applyDatetimeStart;


    /**
     * 申请时间止
     */
    @ApiModelProperty(name = "applyDatetimeEnd", value = "申请时间止")
    private Date applyDatetimeEnd;

    /**
     * 申请人
     */
    @ApiModelProperty(name = "applyUserName", value = "申请人")
    private String applyUserName;

    /**
     * 用户类型（C端用户 CLINIC诊所用户）
     */
    @ApiModelProperty(name = "userKindList", value = "用户类型（C端用户 CLINIC诊所用户）")
    private List<String> userKindList;

    /**
     * 充值渠道
     */
    @ApiModelProperty(name = "channelTypeList", value = "支付渠道类型（内部账inner 人工线下off_line 支付宝支付alipay 微信公众号支付wechat）")
    private List<String> channelTypeList;

    /**
     * 申请人列表
     */
    private List<User> userList;

    /**
     * 支付回录时间起
     */
    @ApiModelProperty(name = "payDatetimeStart", value = "支付回录时间起")
    private Date payDatetimeStart;
    @ApiModelProperty(name = "keywords", value = "充值用户关键字")
    private String keywords;
    /**
     * 支付回录时间止
     */
    @ApiModelProperty(name = "applyDatetimeEnd", value = "支付回录时间止")
    private Date payDatetimeEnd;

    @ApiModelProperty(name = "statusName", value = "状态")
    private String statusName;

    public String getChargeStatus() {
        if (StringUtils.isNotBlank(status)) {
            statusName = EChargeStatus.getChargeStatus(status).getValue();
        }
        return statusName;
    }

}
