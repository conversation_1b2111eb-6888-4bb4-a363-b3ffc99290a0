package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import com.std.core.enums.ERobotWorkRecordStatus;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 机器人挖宝记录
 *
 * <AUTHOR> ycj
 * @since : 2022-08-29 20:16
 */
@Data
public class RobotWorkRecord extends BaseDo {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 活动序号
     */
    @ApiModelProperty(name = "activityId", value = "活动序号", position = 10)
    private Long activityId;

    /**
     * 用户序号
     */
    @ApiModelProperty(name = "userId", value = "用户序号", position = 20)
    private Long userId;

    /**
     * 用户
     */
    @ApiModelProperty(hidden = true)
    private User user;

    /**
     * 用户关键信息（手机号/身份证/姓名）
     */
    private String keywords;

    /**
     * 状态 {0:进行中,1:完成}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:进行中,1:完成}", position = 30)
    private String status;

    /**
     * 状态集合
     */
    private List<String> statusList;

    /**
     * 机器人数量
     */
    @ApiModelProperty(name = "number", value = "机器人数量", position = 40)
    private Integer number;

    /**
     * 总概率
     */
    @ApiModelProperty(name = "totalProbability", value = "总概率", position = 50)
    private BigDecimal totalProbability;



    /**
     * 派遣时间
     */
    @ApiModelProperty(name = "sendTime", value = "派遣时间", position = 60)
    private Integer sendTime;

    /**
     * 开始时间
     */
    @ApiModelProperty(name = "createDatetime", value = "开始时间", position = 70)
    private Date createDatetime;

    /**
     * 完成时间
     */
    @ApiModelProperty(name = "finishDatetime", value = "完成时间", position = 80)
    private Date finishDatetime;

    /**
     * 实际完成时间
     */
    @ApiModelProperty(name = "actualFinishDatetime", value = "实际完成时间", position = 90)
    private Date actualFinishDatetime;

    /**** Properties ****/

    /**
     * 状态 {0:进行中,1:完成}
     */
    @ApiModelProperty(name = "statusName", value = "状态 {0:进行中,1:完成}")
    private String statusName;

    public String getStatusName() {
      if (StringUtils.isNotBlank(status)) {
        statusName = ERobotWorkRecordStatus.getRobotWorkRecordStatus(status).getValue();
      }

      return statusName;
    }



}
