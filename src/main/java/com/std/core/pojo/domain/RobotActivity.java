package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import com.std.core.enums.ERobotActivityStatus;
import com.std.core.enums.ERobotActivityStartStatus;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 机器人挖宝活动
 *
 * <AUTHOR> wzh
 * @since : 2023-05-24 16:45
 */
@Data
public class RobotActivity extends BaseDo {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 活动名称
     */
    @ApiModelProperty(name = "name", value = "活动名称", position = 20)
    private String name;

    /**
     * 状态 {0:待上架,1:已上架,2:已下架}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:待上架,1:已上架,2:已下架}", position = 30)
    private String status;

    /**
     * 状态集合
     */
    private List<String> statusList;

    /**
     * 开始状态 {0:待开始,1:进行中,2:已结束}
     */
    @ApiModelProperty(name = "startStatus", value = "开始状态 {0:待开始,1:进行中,2:已结束}", position = 40)
    private String startStatus;

    /**
     * 开始时间
     */
    @ApiModelProperty(name = "startDatetime", value = "开始时间", position = 50)
    private Date startDatetime;

    /**
     * 结束时间
     */
    @ApiModelProperty(name = "endDatetime", value = "结束时间", position = 60)
    private Date endDatetime;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "creator", value = "创建人", position = 70)
    private Long creator;

    /**
     * 创建人名
     */
    @ApiModelProperty(name = "createName", value = "创建人名", position = 80)
    private String createName;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 90)
    private Date createDatetime;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 100)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称", position = 110)
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 120)
    private Date updateDatetime;

    /**
     * 备注说明
     */
    @ApiModelProperty(name = "remark", value = "备注说明", position = 130)
    private String remark;

    /**** Properties ****/

    /**
     * 状态 {0:待上架,1:已上架,2:已下架}
     */
    @ApiModelProperty(name = "statusName", value = "状态 {0:待上架,1:已上架,2:已下架}")
    private String statusName;

    /**
     * 开始状态 {0:待开始,1:进行中,2:已结束}
     */
    @ApiModelProperty(name = "startStatusName", value = "开始状态 {0:待开始,1:进行中,2:已结束}")
    private String startStatusName;

    public String getStatusName() {
      if (StringUtils.isNotBlank(status)) {
        statusName = ERobotActivityStatus.getRobotActivityStatus(status).getValue();
      }

      return statusName;
    }

    public String getStartStatusName() {
      if (StringUtils.isNotBlank(startStatus)) {
        startStatusName = ERobotActivityStartStatus.getRobotActivityStartStatus(startStatus).getValue();
      }

      return startStatusName;
    }

    @ApiModelProperty(name = "endDatetimeEnd", value = "结束时间止", position = 60)
    private Date endDatetimeEnd;


}
