package com.std.core.pojo.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 比赛房间
 *
 * <AUTHOR> ycj
 * @since : 2022-11-29 15:17
 */
@Data
public class MeetingStartSend {

    @ApiModelProperty(name = "meetingId", value = "会议id")
    private String meetingId ;

    @ApiModelProperty(name = "roomId", value = "房间id")
    private String roomId ;

    @ApiModelProperty(name = "presenterId", value = "主播id")
    private String presenterId ;

    /**
     * 邀请码
     */
    @ApiModelProperty(name = "inviteNo", value = "邀请码")
    private String inviteNo;


}
