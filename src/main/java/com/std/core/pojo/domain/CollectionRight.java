package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * 作品权益表
 *
 * <AUTHOR> ycj
 * @since : 2022-02-09 13:48
 */
@Data
public class CollectionRight extends BaseDo {

    /**
     *
     */
    @ApiModelProperty(name = "id", value = "", position = 10)
    private Long id;

    /**
     * 作品id
     */
    @ApiModelProperty(name = "collectionId", value = "作品id", position = 20)
    private Long collectionId;

    /**
     * 权益类型(0=免提现手续费 1=免费抽盲盒)
     */
    @ApiModelProperty(name = "type", value = "权益类型(0=免提现手续费 1=免费抽盲盒)", position = 30)
    private String type;

    /**
     * 权益关联id(类型=盲盒时的系列id)
     */
    @ApiModelProperty(name = "refId", value = "权益关联id(类型=盲盒时的系列id)", position = 40)
    private Long refId;

    /**
     * 权益周期类型(-1=永久 0=按年 1=按月 2=按日)
     */
    @ApiModelProperty(name = "cycleType", value = "权益周期类型(0=永久 1=按年 2=按月 3=按日)", position = 50)
    private String cycleType;

    /**
     * 权益周期次数(-1=无限次)
     */
    @ApiModelProperty(name = "cycleTime", value = "权益周期单个次数(-1=无限次)", position = 60)
    private Integer cycleTime;

    /**
     * 状态(0=可用 1=作废)
     */
    @ApiModelProperty(name = "status", value = "状态(0=可用 1=作废)", position = 70)
    private String status;

    /**
     * 状态集合
     */
    private List<String> statusList;

    /**
     * 权益关联名称(类型=盲盒时的系列名称)
     */
    @ApiModelProperty(name = "refName", value = "权益关联名称(类型=盲盒时的系列名称)", position = 40)
    private String refName;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号")
    private Long userId;
}
