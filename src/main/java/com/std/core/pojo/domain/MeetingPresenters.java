package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 会议直播主播
 *
 * <AUTHOR> wzh
 * @since : 2023-06-01 18:43
 */
@Data
public class MeetingPresenters extends BaseDo {

    /**
     * 会议序号
     */
    @ApiModelProperty(name = "meetingId", value = "会议序号", position = 10)
    private Long meetingId;

    /**
     * 主播序号
     */
    @ApiModelProperty(name = "userId", value = "主播序号", position = 20)
    private Long userId;

    /**
     * 用户
     */
    @ApiModelProperty(hidden = true)
    private User user;

    /**
     * 用户关键信息（手机号/身份证/姓名）
     */
    private String keywords;

    /**
     * 手机号
     */
    @ApiModelProperty(name = "mobile", value = "手机号", position = 20)
    private String mobile;


    public MeetingPresenters() {
    }

    public MeetingPresenters(Long meetingId, Long userId) {
        this.meetingId = meetingId;
        this.userId = userId;
    }

}
