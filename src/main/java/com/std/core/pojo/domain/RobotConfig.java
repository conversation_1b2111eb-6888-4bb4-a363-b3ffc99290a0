package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import com.std.core.enums.ERobotConfigStatus;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 机器人等级配置
 *
 * <AUTHOR> ycj
 * @since : 2022-08-29 10:08
 */
@Data
public class RobotConfig extends BaseDo {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 藏品序号
     */
    @ApiModelProperty(name = "collectionId", value = "藏品序号", position = 20)
    private Long collectionId;

    /**
     * 藏品名称
     */
    @ApiModelProperty(name = "collectionName", value = "藏品名称", position = 20)
    private String collectionName;

    /**
     * 等级
     */
    @ApiModelProperty(name = "level", value = "等级", position = 30)
    private String level;

    /**
     * 模糊文件地址
     */
    @ApiModelProperty(name = "coverFileUrl", value = "模糊文件地址", position = 30)
    private String coverFileUrl;

    /**
     * 状态 {0:待启用,1:启用中}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:待启用,1:启用中}", position = 40)
    private String status;

    /**
     * 状态集合
     */
    private List<String> statusList;

    /**
     * 每次概率
     */
    @ApiModelProperty(name = "probability", value = "每次概率", position = 50)
    private BigDecimal probability;

    /**
     * 派遣时间
     */
    @ApiModelProperty(name = "sendTime", value = "派遣时间", position = 60)
    private Integer sendTime;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "creater", value = "创建人", position = 80)
    private Long creater;

    /**
     * 创建人名
     */
    @ApiModelProperty(name = "createrName", value = "创建人名", position = 90)
    private String createrName;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 100)
    private Date createDatetime;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 110)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称", position = 120)
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 130)
    private Date updateDatetime;

    /**** Properties ****/

    /**
     * 状态 {0:待启用,1:启用中}
     */
    @ApiModelProperty(name = "statusName", value = "状态 {0:待启用,1:启用中}")
    private String statusName;

    public String getStatusName() {
        if (StringUtils.isNotBlank(status)) {
            statusName = ERobotConfigStatus.getRobotConfigStatus(status).getValue();
        }

        return statusName;
    }


}
