package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import com.std.core.enums.EContractTokenType;
import com.std.core.pojo.response.CollectionDetailDetailRes;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * 代币(erc721)
 *
 * <AUTHOR> xieyj
 * @since : 2021-07-09 09:31
 */
@Data
public class ContractToken extends BaseDo {

    /**
     * ID
     */
    @ApiModelProperty(name = "id", value = "", position = 10)
    private Long id;

    /**
     * 用户类型
     */
    @ApiModelProperty(name = "userType", value = "用户类型 0:用户,1:机构", position = 20)
    private String userType;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号", position = 20)
    private Long userId;

    /**
     * 用户
     */
    @ApiModelProperty(hidden = true)
    private User user;

    /**
     * 机构
     */
    @ApiModelProperty(name = "company", value = "机构")
    private Company company;

    /**
     * 用户关键信息（手机号/身份证/姓名）
     */
    private String keywords;

    /**
     * 类型 0:自创,1:外来
     */
    @ApiModelProperty(name = "type", value = "类型 0:自创,1:外来", position = 30)
    private String type;

    /**
     * 合约id
     */
    @ApiModelProperty(name = "contractId", value = "合约id", position = 10)
    private Long contractId;

    /**
     * 合约地址
     */
    @ApiModelProperty(name = "contractAddress", value = "合约地址", position = 40)
    private String contractAddress;

    /**
     * 代币编号
     */
    @ApiModelProperty(name = "tokenId", value = "代币编号", position = 50)
    private String tokenId;

    /**
     * 归属地址
     */
    @ApiModelProperty(name = "address", value = "归属地址", position = 60)
    private String address;

    /**
     * 状态 0;待分配  1:已分配
     */
    @ApiModelProperty(name = "status", value = "状态 0;待分配  1:已分配", position = 70)
    private String status;

    /**
     * 状态集合
     */
    private List<String> statusList;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 80)
    private Date createDatetime;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 90)
    private String updater;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 100)
    private Date updateDatetime;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注", position = 110)
    private String remark;

    /**
     * 作品id
     */
    @ApiModelProperty(name = "collectionId", value = "作品id", position = 30)
    private Long collectionId;

    /**
     * 藏品id
     */
    @ApiModelProperty(name = "collectionDetailId", value = "藏品id", position = 40)
    private Long collectionDetailId;

    @ApiModelProperty(name = "refId", value = "关联id", position = 82)
    private Long refId;

    @ApiModelProperty(name = "refType", value = "关联类型", position = 83)
    private String refType;

    /**** Properties ****/

    CollectionDetailDetailRes collectionDetailDetailRes;
    /**
     * 类型 0:自创,1:外来
     */
    @ApiModelProperty(name = "typeName", value = "类型 0:自创,1:外来")
    private String typeName;

    public String getTypeName() {
        if (StringUtils.isNotBlank(type)) {
            typeName = EContractTokenType.getContractTokenType(type).getValue();
        }

        return typeName;
    }

    public String getHideTokenId() {
        String result = tokenId;
        if (org.apache.commons.lang3.StringUtils.isBlank(result)) {
            return result;
        }

        if (result.length() > 8) {
            result = result.substring(0, 5) + "...";
        }

        return result;
    }

    public static String getHideTokenId(String tokenId) {
        String result = tokenId;
        if (org.apache.commons.lang3.StringUtils.isBlank(result)) {
            return result;
        }

        if (result.length() > 8) {
            result = result.substring(0, 5) + "...";
        }

        return result;
    }
}
