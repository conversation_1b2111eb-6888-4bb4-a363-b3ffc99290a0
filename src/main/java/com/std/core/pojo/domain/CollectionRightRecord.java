package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 权益使用明细
 *
 * <AUTHOR> ycj
 * @since : 2022-07-25 19:48
 */
@Data
public class CollectionRightRecord extends BaseDo {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 权益序号
     */
    @ApiModelProperty(name = "rightId", value = "权益序号", position = 20)
    private Long rightId;

    /**
     * 权益发行方序号
     */
    @ApiModelProperty(name = "rightCompanyId", value = "权益发行方序号", position = 30)
    private Long rightCompanyId;

    /**
     * 权益藏品
     */
    @ApiModelProperty(name = "collectionId", value = "权益藏品", position = 40)
    private Long collectionId;

    /**
     * 权益藏品
     */
    @ApiModelProperty(name = "collectionName", value = "权益藏品", position = 40)
    private String collectionName;

    /**
     * 空投类型 0:期数,1:手动
     */
    @ApiModelProperty(name = "dropType", value = "空投类型 0:期数,1:手动", position = 50)
    private String dropType;

    /**
     * 权益类型
     */
    @ApiModelProperty(name = "refType", value = "权益类型", position = 50)
    private String refType;

    /**
     * 关联序号
     */
    @ApiModelProperty(name = "refId", value = "关联序号", position = 60)
    private Long refId;

    /**
     * 关联藏品
     */
    @ApiModelProperty(name = "refCollectionId", value = "关联藏品", position = 60)
    private Long refCollectionId;

    /**
     * 关联藏品
     */
    @ApiModelProperty(name = "refCollectionName", value = "关联藏品", position = 60)
    private String refCollectionName;

    /**
     * 关联期数
     */
    @ApiModelProperty(name = "refPeriod", value = "关联期数", position = 60)
    private String refPeriod;


    /**
     * 优先抢购分钟数
     */
    @ApiModelProperty(name = "advanceMins", value = "优先抢购分钟数", position = 40)
    private Integer advanceMins;

    /**
     * 折扣比例,1不打折
     */
    @ApiModelProperty(name = "discountRate", value = "折扣比例,1不打折", position = 40)
    private BigDecimal discountRate;

    /**
     * 单次空投份数
     */
    @ApiModelProperty(name = "dropNumber", value = "单次空投份数", position = 40)
    private Integer dropNumber;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 70)
    private Date createDatetime;



}
