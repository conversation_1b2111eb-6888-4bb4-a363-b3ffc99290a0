package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import com.std.core.enums.EThirdTradeTransferOrderPlatType;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 三方系统交易划转订单
 *
 * <AUTHOR> xieyj
 * @since : 2022-11-18 17:30
 */
@Data
public class ThirdTradeTransferOrder extends BaseDo {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 三方平台类型，{0:xmeta}
     */
    @ApiModelProperty(name = "platType", value = "三方平台类型，{0:xmeta}", position = 20)
    private String platType;

    /**
     * 三方订单号
     */
    @ApiModelProperty(name = "tranNo", value = "三方订单号", position = 30)
    private String tranNo;

    /**
     * 发起方用户id
     */
    @ApiModelProperty(name = "sourceUserId", value = "发起方用户id", position = 40)
    private Long sourceUserId;

    /**
     * 发起用户钱包地址
     */
    @ApiModelProperty(name = "sourceWalletHash", value = "发起用户钱包地址", position = 50)
    private String sourceWalletHash;

    /**
     * 发起用户手机号(不验证)
     */
    @ApiModelProperty(name = "sourceMobile", value = "发起用户手机号(不验证)", position = 60)
    private String sourceMobile;

    /**
     * 接收方用户id
     */
    @ApiModelProperty(name = "targetUserId", value = "接收方用户id", position = 70)
    private Long targetUserId;

    /**
     * 接收用户钱包地址
     */
    @ApiModelProperty(name = "targetWalletHash", value = "接收用户钱包地址", position = 80)
    private String targetWalletHash;

    /**
     * 接收用户手机号(不验证)
     */
    @ApiModelProperty(name = "targetMobile", value = "接收用户手机号(不验证)", position = 90)
    private String targetMobile;

    /**
     * 状态(0=待回调确认 1=交易成功 2=交易失败)
     */
    @ApiModelProperty(name = "status", value = "状态(0=待确认 1=交易成功 2=交易失败)", position = 100)
    private String status;

    /**
     * 状态集合
     */
    private List<String> statusList;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 110)
    private Date createDatetime;

    /**
     * 回调时间
     */
    @ApiModelProperty(name = "callbackDatetime", value = "回调时间", position = 120)
    private Date callbackDatetime;

    /**** Properties ****/

    /**
     * 三方平台类型，{0:xmeta}
     */
    @ApiModelProperty(name = "platTypeName", value = "三方平台类型，{0:xmeta}")
    private String platTypeName;

    public String getPlatTypeName() {
        if (StringUtils.isNotBlank(platType)) {
            platTypeName = EThirdTradeTransferOrderPlatType.getThirdTradeTransferOrderPlatType(platType).getValue();
        }

        return platTypeName;
    }


}
