package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 用户绑定快捷支付
 *
 * <AUTHOR> ycj
 * @since : 2022-03-09 17:36
 */
@Data
public class UserBindCard extends BaseDo {

    /**
     * 编号
     */
    @ApiModelProperty(name = "id", value = "编号", position = 10)
    private Long id;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号", position = 20)
    private Long userId;

    /**
     * 用户
     */
    @ApiModelProperty(hidden = true)
    private User user;

    /**
     * 用户关键信息（手机号/身份证/姓名）
     */
    private String keywords;

    /**
     * 渠道银行编号
     */
    @ApiModelProperty(name = "channelBankId", value = "渠道银行编号", position = 30)
    private Long channelBankId;

    /**
     * 渠道类型 {0:宝付,1:易宝}
     */
    @ApiModelProperty(name = "channelType", value = "渠道类型 {0:宝付}", position = 30)
    private String channelType;

    /**
     * 银行编号
     */
    @ApiModelProperty(name = "bankCode", value = "银行编号", position = 40)
    private String bankCode;

    /**
     * 银行名称
     */
    @ApiModelProperty(name = "bankName", value = "银行名称", position = 50)
    private String bankName;

    /**
     * 银行卡号
     */
    @ApiModelProperty(name = "cardNo", value = "银行卡号", position = 60)
    private String cardNo;

    /**
     * 银行卡类型
     */
    @ApiModelProperty(name = "cardType", value = "银行卡类型", position = 70)
    private String cardType;

    /**
     * 银行卡户名
     */
    @ApiModelProperty(name = "cardRealName", value = "银行卡户名", position = 80)
    private String cardRealName;

    /**
     * 银行卡身份证
     */
    @ApiModelProperty(name = "cardIdNo", value = "银行卡身份证", position = 90)
    private String cardIdNo;

    /**
     * 绑定的手机号
     */
    @ApiModelProperty(name = "mobile", value = "绑定的手机号", position = 95)
    private String mobile;

    /**
     * 预签约唯一码
     */
    @ApiModelProperty(name = "preSignCode", value = "预签约唯一码", position = 100)
    private String preSignCode;

    /**
     * 短信验证码
     */
    @ApiModelProperty(name = "smsCheckCode", value = "短信验证码", position = 110)
    private String smsCheckCode;

    @ApiModelProperty(name = "securityCode",value = "安全码",position = 115)
    private String securityCode;

    @ApiModelProperty(name = "expiryDate",value = "有效期",position = 118)
    private String expiryDate;

    /**
     * 状态（0预绑定 1已绑定 2已解绑）
     */
    @ApiModelProperty(name = "status", value = "状态（0预绑定 1已绑定 2已解绑）", position = 120)
    private String status;

    /**
     * 状态集合
     */
    private List<String> statusList;

    /**
     * 签约协议号
     */
    @ApiModelProperty(name = "protocolNo", value = "签约协议号", position = 120)
    private String protocolNo;

    /**
     * 预绑定时间
     */
    @ApiModelProperty(name = "preBindDatetime", value = "预绑定时间", position = 130)
    private Date preBindDatetime;

    /**
     * 绑卡时间
     */
    @ApiModelProperty(name = "bindDatetime", value = "绑卡时间", position = 140)
    private Date bindDatetime;

    /**
     * 解绑时间
     */
    @ApiModelProperty(name = "unbindDatetime", value = "解绑时间", position = 150)
    private Date unbindDatetime;

    private String logo;


    public String toStringYeePay() {
        return "{" +'\\'+'\"'+
                "bankCardNo"+'\\'+'\"'+":"+'\\'+'\"' + cardNo +'\\'+ '\"' +
                ","+'\\'+ '\"'+ "cardName"+'\\'+'\"'+":"+'\\'+'\"' + cardRealName +'\\'+ '\"' +
                ","+'\\'+ '\"'+ "idCardNo"+'\\'+'\"'+":"+'\\'+'\"' + cardIdNo +'\\'+ '\"' +
                ","+'\\'+ '\"'+ "mobilePhoneNo"+'\\'+'\"'+":"+'\\'+'\"' + mobile +'\\'+ '\"' +
                "}";
    }
}
