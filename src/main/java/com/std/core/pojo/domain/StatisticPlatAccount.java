package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 平台每日资金情况统计
 *
 * <AUTHOR> xieyj
 * @since : 2020-06-09 20:04
 */
@Data
public class StatisticPlatAccount extends BaseDo {

    /**
     * 主键ID
     */
    @ApiModelProperty(name = "id", value = "主键ID", position = 10)
    private Long id;

    /**
     * 今日用户总余额
     */
    @ApiModelProperty(name = "totalBalance", value = "今日用户总余额", position = 20)
    private BigDecimal totalBalance;

    /**
     * 今日充值总金额
     */
    @ApiModelProperty(name = "todayCharge", value = "今日充值总金额", position = 30)
    private BigDecimal todayCharge;

    /**
     * 截止今日累计充值总金额
     */
    @ApiModelProperty(name = "totalCharge", value = "截止今日累计充值总金额", position = 40)
    private BigDecimal totalCharge;

    /**
     * 今日取现总金额
     */
    @ApiModelProperty(name = "todayWithdraw", value = "今日取现总金额", position = 50)
    private BigDecimal todayWithdraw;

    /**
     * 截止今日累计取现总金额
     */
    @ApiModelProperty(name = "totalWithdraw", value = "截止今日累计取现总金额", position = 60)
    private BigDecimal totalWithdraw;

    /**
     * 统计日期
     */
    @ApiModelProperty(name = "date", value = "统计日期", position = 70)
    private Date date;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 80)
    private Date createDatetime;

    /**
     * 收益时间起
     */
    @ApiModelProperty(name = "dateStart", value = "收益时间起 yyyy-mm-dd", position = 100)
    private Date dateStart;

    /**
     * 收益时间止
     */
    @ApiModelProperty(name = "dateEnd", value = "收益时间止 yyyy-mm-dd", position = 100)
    private Date dateEnd;




}
