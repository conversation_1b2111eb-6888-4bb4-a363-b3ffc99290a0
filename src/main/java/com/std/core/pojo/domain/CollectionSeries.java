package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import com.std.core.enums.ECollectionSeriesType;
import com.std.core.enums.ECollectionSeriesStatus;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 作品系列
 *
 * <AUTHOR> wzh
 * @since : 2023-08-08 17:09
 */
@Data
public class CollectionSeries extends BaseDo {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 类型 {0:无限制,1:时间限制}
     */
    @ApiModelProperty(name = "type", value = "类型 {0:无限制,1:时间限制}", position = 20)
    private String type;

    /**
     * 发行方序号
     */
    @ApiModelProperty(name = "companyId", value = "发行方序号", position = 30)
    private Long companyId;

    @ApiModelProperty(name = "company", value = "发行方信息", position = 30)
    private Company company;

    /**
     * 关联期数序号
     */
    @ApiModelProperty(name = "periodId", value = "关联期数序号", position = 30)
    private Long periodId;

    /**
     * 关联期数名称
     */
    @ApiModelProperty(name = "periodName", value = "关联期数名称", position = 30)
    private String periodName;

    /**
     * 类别
     */
    @ApiModelProperty(name = "category", value = "类别", position = 30)
    private String category;

    /**
     * 系列名
     */
    @ApiModelProperty(name = "name", value = "系列名", position = 40)
    private String name;

    /**
     * 系列图
     */
    @ApiModelProperty(name = "pic", value = "系列图", position = 50)
    private String pic;

    /**
     * 文件类型 0:图片,1:音频,2:视频
     */
    @ApiModelProperty(name = "fileType", value = "文件类型 0:图片,1:音频,2:视频,3:3d文件", position = 60)
    private String fileType;

    /**
     * 版权费
     */
    @ApiModelProperty(name = "copyrightFee", value = "版权费", position = 60)
    private BigDecimal copyrightFee;

    /**
     * 项目出售最大价格
     */
    @ApiModelProperty(name = "maxPrice", value = "项目出售最大价格", position = 60)
    private BigDecimal maxPrice;

    @ApiModelProperty(name = "singleMaxQuantity", value = "单人拥有最大数量")
    private Integer singleMaxQuantity;
    /**
     * 开始时间
     */
    @ApiModelProperty(name = "startDatetime", value = "开始时间", position = 70)
    private Date startDatetime;

    /**
     * 开始时间置空
     */
    @ApiModelProperty(name = "startDatetimeFlag", value = "开始时间置空", position = 70)
    private String startDatetimeFlag;


    /**
     * 状态 {0:待上架,1:已上架,2:已下架}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:待上架,1:已上架,2:已下架}", position = 80)
    private String status;

    /**
     * 状态集合
     */
    private List<String> statusList;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "creater", value = "创建人", position = 90)
    private Long creater;

    /**
     * 创建人名
     */
    @ApiModelProperty(name = "createrName", value = "创建人名", position = 100)
    private String createrName;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 110)
    private Date createDatetime;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 100)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称", position = 110)
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 120)
    private Date updateDatetime;

    @ApiModelProperty(name = "collectionIdList", value = "作品集合", position = 120)
    private List<CollectionSeriesConfig> collectionIdList;
    /**** Properties ****/

    /**
     * 类型 {0:无限制,1:时间限制}
     */
    @ApiModelProperty(name = "typeName", value = "类型 {0:无限制,1:时间限制}")
    private String typeName;

    /**
     * 状态 {0:待上架,1:已上架,2:已下架}
     */
    @ApiModelProperty(name = "statusName", value = "状态 {0:待上架,1:已上架,2:已下架}")
    private String statusName;

    public String getTypeName() {
      if (StringUtils.isNotBlank(type)) {
        typeName = ECollectionSeriesType.getCollectionSeriesType(type).getValue();
      }

      return typeName;
    }

    public String getStatusName() {
      if (StringUtils.isNotBlank(status)) {
        statusName = ECollectionSeriesStatus.getCollectionSeriesStatus(status).getValue();
      }

      return statusName;
    }



}
