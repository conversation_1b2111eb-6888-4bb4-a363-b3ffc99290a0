package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 作品id下架
 *
 * <AUTHOR> xieyj
 * @since : 2021-07-11 17:05
 */
@Data
public class CollectionOffReq {


    /**
     * 作品id
     */
    @ApiModelProperty(name = "id", value = "作品id", position = 150)
    @NotNull(message = "作品id不能为空")
    private Long id;

}
