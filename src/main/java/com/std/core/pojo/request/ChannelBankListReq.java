package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 列表查询渠道银行
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 12:29
 */
@Data
public class ChannelBankListReq extends BaseListReq {

//    /**
//     * 银行编号
//     */
//    @ApiModelProperty(name = "bankCode", value = "银行编号")
//    private String bankCode;
//
//    /**
//     * 银行名称
//     */
//    @ApiModelProperty(name = "bankName", value = "银行名称")
//    private String bankName;

    @ApiModelProperty(name = "bizType", value = "业务类型0:充值,2 一口价订单支付 3 竞拍支付保证金 4:竞拍支付 6:导出藏品支付 7:百变大咖支付 8:购买藏品 9:转赠藏品支付手续费", position = 90)
    private String bizType;

    @ApiModelProperty(name = "bizCategory", value = "业务分类", position = 90)
    private String bizCategory;
}
