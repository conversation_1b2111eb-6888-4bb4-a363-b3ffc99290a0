package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 分页查询元宇宙门票使用记录
 *
 * <AUTHOR> ycj
 * @since : 2022-10-13 15:17
 */
@Data
public class MetaTicketRecordPageReq extends BasePageReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 门票配置类型
     */
    @ApiModelProperty(name = "ticketId", value = "门票配置类型", position = 20)
    private Long ticketId;

    /**
     * 门票类型
     */
    @ApiModelProperty(name = "ticketType", value = "门票类型", position = 30)
    private String ticketType;

    /**
     * 藏品
     */
    @ApiModelProperty(name = "collectionDetailId", value = "藏品", position = 40)
    private Long collectionDetailId;

    /**
     * 状态 {0:失效,1:生效中}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:失效,1:生效中}", position = 50)
    private String status;

    /**
     * 状态列表
     */
    @ApiModelProperty(name = "statusList", value = "状态列表", position = 51)
    private List<String> statusList;

    /**
     * 失效时间
     */
    @ApiModelProperty(name = "failureDatetime", value = "失效时间", position = 60)
    private Date failureDatetime;

    /**
     * 使用人
     */
    @ApiModelProperty(name = "creater", value = "使用人", position = 70)
    private Long creater;

    /**
     * 使用时间
     */
    @ApiModelProperty(name = "createDatetime", value = "使用时间", position = 80)
    private Date createDatetime;

}
