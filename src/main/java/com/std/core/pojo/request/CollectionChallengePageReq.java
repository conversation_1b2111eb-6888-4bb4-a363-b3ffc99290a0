package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 分页查询用户售卖作品
 *
 * <AUTHOR> xieyj
 * @since : 2021-07-11 17:05
 */
@Data
public class CollectionChallengePageReq extends BasePageReq {

    @ApiModelProperty(name = "companyId",value = "机构序号")
    @NotNull(message = "机构序号不能为空")
    private Long companyId;

    @ApiModelProperty(name = "name")
    private String name;
}
