package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import java.util.Date;
import lombok.Data;

/**
 * 修改赋能商城供货商
 *
 * <AUTHOR> ycj
 * @since : 2022-04-26 17:17
 */
@Data
public class GoodsSupplierModifyReq {

    /**
     * 序号
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 供货商名称
     */
    @ApiModelProperty(name = "name", value = "供货商名称", position = 20)
    private String name;

}
