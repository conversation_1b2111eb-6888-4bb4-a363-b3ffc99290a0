package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.util.List;

/**
 * 新增竞拍渠道用户序号
 *
 * <AUTHOR> ycj
 * @since : 2022-03-19 18:49
 */
@Data
public class ChannelCollectionExportReq {

    /**
     * 渠道编号
     */
    @ApiModelProperty(name = "channelCode", value = "渠道编号", position = 20)
    @NotBlank(message = "渠道编号不能为空")
    private String channelCode;

    @ApiModelProperty(name = "transferOrderId", value = "流转订单", position = 40)
    @NotNull(message = "流转订单不能为空")
    private Long transferOrderId;

    /**
     * 支付订单号
     */
    @ApiModelProperty(name = "payOrderCode", value = "支付订单号", position = 90)
    @NotBlank(message = "支付订单号不能为空")
    private String payOrderCode;

    @ApiModelProperty(name = "collectionDetailId",value = "转入藏品集合")
    @NotEmpty(message = "转入藏品集合不能为空")
    private List<ChannelCollectionExportCollectionListReq> collectionDetailId;

    @NotNull(message = "用户序号不能为空")
    private String userId;

    /**
     * 转币地址
     */
    @ApiModelProperty(name = "toAddress", value = "转币地址", position = 70)
    @NotBlank(message = "转币地址不能为空")
    private String toAddress;

    @ApiModelProperty(name = "sign", value = "签名")
    @NotBlank(message = "签名不能为空")
    private String sign;


    public String toStringSing() {
        return "{" +
                "channelCode='" + channelCode + '\'' +
                ", transferOrderId=" + transferOrderId +
                ", payOrderCode='" + payOrderCode + '\'' +
                ", collectionDetailId=" + collectionDetailId +
                ", userId='" + userId + '\'' +
                ", toAddress='" + toAddress + '\'' +
                '}';
    }
}
