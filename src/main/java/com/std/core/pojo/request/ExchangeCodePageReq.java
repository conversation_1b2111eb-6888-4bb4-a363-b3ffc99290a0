package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 分页查询兑换码
 *
 * <AUTHOR> ycj
 * @since : 2022-01-02 20:46
 */
@Data
public class ExchangeCodePageReq extends BasePageReq {

    /**
     * 编号
     */
    @ApiModelProperty(name = "id", value = "编号", position = 10)
    private Long id;

    /**
     * 兑换码
     */
    @ApiModelProperty(name = "exchangeNo", value = "兑换码", position = 20)
    private String exchangeNo;

    /**
     * 作品id
     */
    @ApiModelProperty(name = "collectionId", value = "作品id", position = 30)
    private Long collectionId;

    /**
     * 数量
     */
    @ApiModelProperty(name = "quantity", value = "数量", position = 40)
    private Integer quantity;

    /**
     * 状态(0=待使用，1=已使用，2=已作废)
     */
    @ApiModelProperty(name = "status", value = "状态(0=待使用，1=已使用，2=已作废)", position = 50)
    private String status;

    /**
     * 状态列表
     */
    @ApiModelProperty(name = "statusList", value = "状态列表", position = 51)
    private List<String> statusList;

    /**
     * 兑换用户id
     */
    @ApiModelProperty(name = "exchangeUserId", value = "兑换用户id", position = 120)
    private Long exchangeUserId;

    /**
     * 兑换时间起
     */
    @ApiModelProperty(name = "exchangeTimeStart", value = "兑换时间起yyyy-MM-dd hh:mm:ss", position = 130)
    private Date exchangeTimeStart;

    /**
     * 兑换时间止
     */
    @ApiModelProperty(name = "exchangeTimeEnd", value = "兑换时间止yyyy-MM-dd hh:mm:ss", position = 130)
    private Date exchangeTimeEnd;

    /**
     * 导出批次号
     */
    @ApiModelProperty(name = "exportBatchId", value = "导出批次号", position = 20)
    private Integer exportBatchId;

    /**
     * 导出标志
     */
    @ApiModelProperty(name = "exportFlag", value = "导出标志(0=待导出 1=已导出)", position = 140)
    private String exportFlag;
    /**
     * 用户关键信息（手机号/身份证/姓名）
     */
    @ApiModelProperty(name = "keywords", value = "用户关键信息（手机号/身份证/姓名）", position = 41)
    private String keywords;

    /**
     * 类型 dict=0:发行方,1:平台
     */
    @ApiModelProperty(name = "type", value = "类型 dict=0:发行方,1:平台", position = 50)
    private String type;
}
