package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import com.std.core.util.EnumValue;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 分页查询银行信息
 *
 * <AUTHOR> ycj
 * @since : 2022-03-10 11:01
 */
@Data
public class BankInfoPageReq extends BasePageReq {

    /**
     * 银行关键字
     */
    @ApiModelProperty(name = "keywords", value = "银行关键字", position = 20)
    private String keywords;

    /**
     * 银行编号
     */
    @ApiModelProperty(name = "bankCode", value = "银行编号")
    private String bankCode;

    /**
     * 银行名称
     */
    @ApiModelProperty(name = "bankName", value = "银行名称")
    private String bankName;

    /**
     * 状态（0下架 1上架）
     */
    @ApiModelProperty(name = "status", value = "状态（0下架 1上架）", position = 60)
    private String status;

    /**
     * 类型
     */
    @ApiModelProperty(name = "type ", value = "类型 1:储蓄卡，2信用卡", hidden = true)
    private String type;

}
