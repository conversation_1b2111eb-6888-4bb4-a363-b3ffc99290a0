package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 列表查询兑换组合
 *
 * <AUTHOR> ycj
 * @since : 2022-05-06 17:35
 */
@Data
public class PitConditionsDetailListReq extends BaseListReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 售卖序号
     */
    @ApiModelProperty(name = "sellerId", value = "售卖序号", position = 20)
    private Long sellerId;

    /**
     * 坑位序号
     */
    @ApiModelProperty(name = "pitId", value = "坑位序号", position = 30)
    private Long pitId;

    /**
     * 意向用户
     */
    @ApiModelProperty(name = "userId", value = "意向用户", position = 40)
    private Long userId;

    /**
     * 条件序号
     */
    @ApiModelProperty(name = "conditionId", value = "条件序号", position = 20)
    private Long conditionId;

    /**
     * 藏品序号
     */
    @ApiModelProperty(name = "collectionId", value = "藏品序号", position = 30)
    private Long collectionId;


}
