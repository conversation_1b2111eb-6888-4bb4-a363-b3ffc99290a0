package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 修改帮助中心文章类型
 *
 * <AUTHOR> ycj
 * @since : 2020-11-09 13:28
 */
@Data
public class HelpCategoryModifyReq {

    /**
     * 序号
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 父类id
     */
    @ApiModelProperty(name = "parentsId", value = "父类id", position = 20)
    private Long parentsId;

    /**
     * 分类名称
     */
    @ApiModelProperty(name = "categoryName", value = "分类名称", position = 30)
    private String categoryName;

    /**
     * 分类说明
     */
    @ApiModelProperty(name = "categoryNote", value = "分类说明", position = 40)
    private String categoryNote;


    /**
     * 是否常见问题 "0":"不是","1":"是"
     */
    @ApiModelProperty(name = "commonProblem", value = "是否常见问题 0:不是,1:是", position = 110)
    private String commonProblem;


}
