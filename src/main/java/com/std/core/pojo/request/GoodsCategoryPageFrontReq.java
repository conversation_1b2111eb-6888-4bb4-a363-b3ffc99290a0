package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 分页查询商品分类
 *
 * <AUTHOR> ycj
 * @since : 2022-04-25 22:21
 */
@Data
public class GoodsCategoryPageFrontReq extends BasePageReq {
    @ApiModelProperty(name = "parentId",value = "父id")
    private Long parentId;
}
