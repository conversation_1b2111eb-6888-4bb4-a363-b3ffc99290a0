package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 列表查询支付记录
 *
 * <AUTHOR> xieyj
 * @since : 2021-07-25 14:23
 */
@Data
public class PayRecordListFrontReq extends BaseListReq {

    /**
     * 业务类型0:充值,1:一口价订单支付
     *
     */
    @ApiModelProperty(name = "bizType", value = "业务类型0:充值,1:创建作品支付 2 一口价订单支付 3 竞拍支付保证金 4:竞拍支付 5:购买作品包支付", position = 90)
    private String bizType;

    /**
     * 关联业务编号
     */
    @ApiModelProperty(name = "bizCode", value = "关联业务编号", position = 100)
    private String bizCode;

}
