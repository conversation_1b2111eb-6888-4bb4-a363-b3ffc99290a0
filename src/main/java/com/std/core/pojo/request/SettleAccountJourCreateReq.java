package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 新增结算账户流水
 *
 * <AUTHOR> ycj
 * @since : 2022-04-24 11:10
 */
@Data
public class SettleAccountJourCreateReq {

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号", required = true, position = 20)
    @NotNull(message = "用户编号不能为空")
    private Long userId;

    /**
     * 账户编号
     */
    @ApiModelProperty(name = "accountNumber", value = "账户编号", required = true, position = 30)
    @NotBlank(message = "账户编号不能为空")
    private String accountNumber;

    /**
     * 业务大类
     */
    @ApiModelProperty(name = "bizCategory", value = "业务大类", required = true, position = 40)
    @NotBlank(message = "业务大类不能为空")
    private String bizCategory;

    /**
     * 业务大类
     */
    @ApiModelProperty(name = "bizCategoryNote", value = "业务大类", required = true, position = 50)
    @NotBlank(message = "业务大类不能为空")
    private String bizCategoryNote;

    /**
     * 业务小类
     */
    @ApiModelProperty(name = "bizType", value = "业务小类", required = true, position = 60)
    @NotBlank(message = "业务小类不能为空")
    private String bizType;

    /**
     * 业务小类说明
     */
    @ApiModelProperty(name = "bizNote", value = "业务小类说明", required = true, position = 70)
    @NotBlank(message = "业务小类说明不能为空")
    private String bizNote;

    /**
     * 系统内部参考订单号
     */
    @ApiModelProperty(name = "refNo", value = "系统内部参考订单号", required = true, position = 80)
    @NotBlank(message = "系统内部参考订单号不能为空")
    private String refNo;

    /**
     * 关联的用户ID
     */
    @ApiModelProperty(name = "refUserId", value = "关联的用户ID", position = 90)
    private Long refUserId;

    /**
     * 变动金额
     */
    @ApiModelProperty(name = "transAmount", value = "变动金额", required = true, position = 100)
    @NotNull(message = "变动金额不能为空")
    private BigDecimal transAmount;

    /**
     * 变动前金额
     */
    @ApiModelProperty(name = "preAmount", value = "变动前金额", position = 110)
    private BigDecimal preAmount;

    /**
     * 变动后金额
     */
    @ApiModelProperty(name = "postAmount", value = "变动后金额", position = 120)
    private BigDecimal postAmount;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", required = true, position = 140)
    @NotBlank(message = "创建时间不能为空")
    private String createDatetime;

}
