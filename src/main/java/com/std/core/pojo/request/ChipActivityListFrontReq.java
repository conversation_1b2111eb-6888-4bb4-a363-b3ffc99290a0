package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 列表查询芯片领藏品活动
 *
 * <AUTHOR> ycj
 * @since : 2022-08-24 10:39
 */
@Data
public class ChipActivityListFrontReq extends BaseListReq {

    @ApiModelProperty(name = "companyId", value = "发行方序号", position = 10)
    @NotNull(message = "发行方序号不能为空")
    private Long companyId;

}
