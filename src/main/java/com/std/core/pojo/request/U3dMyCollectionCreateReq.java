package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 新增u3d我的作品位置
 *
 * <AUTHOR> ycj
 * @since : 2022-03-31 16:33
 */
@Data
public class U3dMyCollectionCreateReq {

    /**
     * 藏品id
     */
    @ApiModelProperty(name = "collectionDetailId", value = "藏品id", position = 20)
    @NotNull(message = "藏品id不能为空")
    private Long collectionDetailId;

    /**
     * 序号
     */
    @ApiModelProperty(name = "orderNo", value = "序号", position = 30)
    @NotNull(message = "序号不能为空")
    private Integer orderNo;

    /**
     * 关联类型
     */
    @ApiModelProperty(name = "refType", value = "关联类型", position = 20)
    private String refType = "0";

    /**
     * 关联序号
     */
    @ApiModelProperty(name = "refId", value = "关联序号", position = 25)
    private Integer refId;

}
