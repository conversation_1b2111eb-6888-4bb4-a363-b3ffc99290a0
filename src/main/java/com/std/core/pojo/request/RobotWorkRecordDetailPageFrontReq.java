package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 分页查询机器人挖宝明细
 *
 * <AUTHOR> ycj
 * @since : 2022-08-29 21:21
 */
@Data
public class RobotWorkRecordDetailPageFrontReq extends BasePageReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 记录序号
     */
    @ApiModelProperty(name = "recordId", value = "记录序号", position = 20)
    private Long recordId;

    /**
     * 机器人序号
     */
    @ApiModelProperty(name = "robotId", value = "机器人序号", position = 30)
    private Long robotId;

    /**
     * 等级
     */
    @ApiModelProperty(name = "level", value = "等级", position = 40)
    private String level;

    /**
     * 机器人作品序号
     */
    @ApiModelProperty(name = "collectionId", value = "机器人作品序号", position = 50)
    private Long collectionId;

    /**
     * 机器人藏品序号
     */
    @ApiModelProperty(name = "collectionDetailId", value = "机器人藏品序号", position = 60)
    private Long collectionDetailId;

    /**
     * 概率
     */
    @ApiModelProperty(name = "probability", value = "概率", position = 70)
    private BigDecimal probability;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 80)
    private Date createDatetime;

}
