package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import java.util.Date;
import lombok.Data;

/**
 * 修改渠道商每日统计
 *
 * <AUTHOR> xieyj
 * @since : 2021-06-24 09:21
 */
@Data
public class StatDayChannelModifyReq {

    /**
     * id
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "id", position = 10)
    private Long id;

    /**
     * 渠道商userid
     */
    @ApiModelProperty(name = "userId", value = "渠道商userid", position = 20)
    private Long userId;

    /**
     * 盈利金额
     */
    @ApiModelProperty(name = "profit", value = "盈利金额", position = 30)
    private BigDecimal profit;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createTime", value = "创建时间", position = 40)
    private String createTime;

    /**
     * 状态 0=未结算 1=已结算
     */
    @ApiModelProperty(name = "status", value = "状态 0=未结算 1=已结算", position = 50)
    private String status;

    /**
     * 结算比例
     */
    @ApiModelProperty(name = "deductRate", value = "结算比例", position = 60)
    private BigDecimal deductRate;

    /**
     * 结算金额
     */
    @ApiModelProperty(name = "deductAmount", value = "结算金额", position = 70)
    private BigDecimal deductAmount;

}
