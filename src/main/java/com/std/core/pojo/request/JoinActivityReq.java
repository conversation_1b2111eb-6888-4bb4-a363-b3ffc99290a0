package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> ycj
 * @since : 2020/10/27 15:02
 */
@Data
public class JoinActivityReq {
    /**
     * 活动序号
     */
    @ApiModelProperty(name = "activityId", value = "活动序号", position = 10)
    private Long activityId;
    /**
     * 参与金额
     */
    @ApiModelProperty(name = "joinAmount", value = "参与金额", position = 20)
    private BigDecimal joinAmount;
    /**
     * 支付密码
     */
    @ApiModelProperty(name = "pwd", value = "支付密码", position = 30)
    private String pwd;
}
