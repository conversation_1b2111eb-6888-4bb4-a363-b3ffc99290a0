package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * 分页查询结算
 *
 * <AUTHOR> xiongk
 * @since : 2020-03-18 13:51
 */
@Data
public class SettlePageReq extends BasePageReq {

    /**
     *
     */
    @ApiModelProperty(name = "id", value = "")
    private Long id;

    /**
     * 类型（1托管账户往公帐账户结算 2厂家业务账户往诊所账户结算 3诊所授信结算）
     */
    @ApiModelProperty(name = "type", value = "类型（1托管账户往公帐账户结算 2厂家业务账户往诊所账户结算 3诊所授信结算）")
    private String type;

    /**
     * 来源账户
     */
    @ApiModelProperty(name = "fromAccountNumber", value = "来源账户")
    private String fromAccountNumber;

    /**
     * 结算账户
     */
    @ApiModelProperty(name = "toAccountNumber", value = "结算账户")
    private String toAccountNumber;

    /**
     * 币种
     */
    @ApiModelProperty(name = "currency", value = "币种")
    private String currency;

    /**
     * 申请人编号
     */
    @ApiModelProperty(name = "applyUserId", value = "申请人编号")
    private Long applyUserId;

    /**
     * 回录人
     */
    @ApiModelProperty(name = "repayUserId", value = "回录人")
    private Long repayUserId;

    /**
     * 状态（1待回录 2结算成功 3结算失败）
     */
    @ApiModelProperty(name = "status", value = "状态（1待回录 2结算成功 3结算失败）")
    private String status;

    /**
     * 状态列表
     */
    @ApiModelProperty(name = "statusList", value = "状态列表")
    private List<String> statusList;

}
