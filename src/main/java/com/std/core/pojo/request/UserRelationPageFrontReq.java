package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 分页查询用户关注信息
 *
 * <AUTHOR> xieyj
 * @since : 2021-07-07 09:55
 */
@Data
public class UserRelationPageFrontReq extends BasePageReq {

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号", position = 20)
    private Long userId;


    /**
     * 关系人编号
     */
    @ApiModelProperty(name = "toUser", value = "关系人编号", position = 30)
    private Long toUser;

}
