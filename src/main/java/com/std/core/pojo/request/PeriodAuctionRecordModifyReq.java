package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import java.util.Date;
import lombok.Data;

/**
 * 修改期数竞拍出价记录
 *
 * <AUTHOR> ycj
 * @since : 2022-03-29 21:43
 */
@Data
public class PeriodAuctionRecordModifyReq {

    /**
     * 
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "", position = 10)
    private Long id;

    /**
     * 期数id
     */
    @ApiModelProperty(name = "periodId", value = "期数id", position = 20)
    private Long periodId;

    /**
     * 用户id
     */
    @ApiModelProperty(name = "userId", value = "用户id", position = 30)
    private Long userId;

    /**
     * 出价金额
     */
    @ApiModelProperty(name = "price", value = "出价金额", position = 40)
    private BigDecimal price;

    /**
     * 出价时间
     */
    @ApiModelProperty(name = "postTime", value = "出价时间", position = 50)
    private String postTime;

}
