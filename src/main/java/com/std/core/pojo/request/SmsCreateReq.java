package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 新增消息记录
 *
 * <AUTHOR> golder
 * @since : 2020-04-14 19:57
 */
@Data
public class SmsCreateReq {

    /**
     * 针对人群
     */
    @ApiModelProperty(name = "target", value = "针对人群, dict=sms.target", position = 20)
    private String target;
    /**
     * 用户序号
     */
    @ApiModelProperty(name = "userId", value = "用户序号", position = 25)
    private Long userId;
    /**
     * 消息类型
     */
    @ApiModelProperty(name = "type", value = "消息类型, 1:系统公告，2：我的消息", required = true, position = 30)
    private String type;

    /**
     * 标题
     */
    @ApiModelProperty(name = "title", value = "标题", required = true, position = 50)
    private String title;

    /**
     * 内容
     */
    @ApiModelProperty(name = "content", value = "内容", required = true, position = 60)
    private String content;
    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注", position = 70)
    private String remark;

    /**
     * 渠道来源
     */
    @ApiModelProperty(name = "channelId", value = "渠道来源")
    private Long channelId;

    /**
     * 是否推送
     */
    @ApiModelProperty(name = "isSend", value = "是否推送 0:否,1:是", position = 138)
//    @NotBlank(message = "极光推送不能为空")
//    @EnumValue(strValues = {"0", "1"}, message = "推送参数错误")
    private String isSend;
}
