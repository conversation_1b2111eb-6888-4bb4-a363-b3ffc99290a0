package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 列表查询用户收款账户
 *
 * <AUTHOR> ycj
 * @since : 2022-04-19 19:10
 */
@Data
public class UserPaymentAccountInfoListReq extends BaseListReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 用户序号
     */
    @ApiModelProperty(name = "userId", value = "用户序号", position = 20)
    private Long userId;

    /**
     * 
     */
    @ApiModelProperty(name = "name", value = "", position = 30)
    private String name;

    /**
     * 简称
     */
    @ApiModelProperty(name = "shortName", value = "简称", position = 40)
    private String shortName;

    /**
     * 
     */
    @ApiModelProperty(name = "introduce", value = "", position = 50)
    private String introduce;

    /**
     * 分账标志
     */
    @ApiModelProperty(name = "divideFlag", value = "分账标志", position = 60)
    private String divideFlag;

    /**
     * 平台分成
     */
    @ApiModelProperty(name = "platDivideRate", value = "平台分成", position = 70)
    private BigDecimal platDivideRate;

    /**
     * 法人证件类型
     */
    @ApiModelProperty(name = "legalLicenceType", value = "法人证件类型", position = 80)
    private String legalLicenceType;

    /**
     * 法人证件号
     */
    @ApiModelProperty(name = "legalLicenceNo", value = "法人证件号", position = 90)
    private String legalLicenceNo;

    /**
     * 证件照片1
     */
    @ApiModelProperty(name = "legalLicenceFrontUrl", value = "证件照片1", position = 100)
    private String legalLicenceFrontUrl;

    /**
     * 证件照片2
     */
    @ApiModelProperty(name = "legalLicenceBackUrl", value = "证件照片2", position = 110)
    private String legalLicenceBackUrl;

    /**
     * 真实姓名
     */
    @ApiModelProperty(name = "legalRealName", value = "真实姓名", position = 120)
    private String legalRealName;

    /**
     * 手机号
     */
    @ApiModelProperty(name = "legalMobile", value = "手机号", position = 130)
    private String legalMobile;

    /**
     * 结算银行卡号
     */
    @ApiModelProperty(name = "settleCardNo", value = "结算银行卡号", position = 140)
    private String settleCardNo;

    /**
     * 结算银行
     */
    @ApiModelProperty(name = "settleBankCode", value = "结算银行", position = 150)
    private String settleBankCode;

    /**
     * 省
     */
    @ApiModelProperty(name = "province", value = "省", position = 160)
    private String province;

    /**
     * 市
     */
    @ApiModelProperty(name = "city", value = "市", position = 170)
    private String city;

    /**
     * 区
     */
    @ApiModelProperty(name = "district", value = "区", position = 180)
    private String district;

    /**
     * 详细地址
     */
    @ApiModelProperty(name = "address", value = "详细地址", position = 190)
    private String address;

    /**
     * 易宝申请订单号
     */
    @ApiModelProperty(name = "requestNo", value = "易宝申请订单号", position = 200)
    private String requestNo;

    /**
     * 易宝商户号
     */
    @ApiModelProperty(name = "merchantNo", value = "易宝商户号", position = 210)
    private String merchantNo;

    /**
     * 状态(0=审核中,1=审核通过,2=审核失败,3=已解约)
     */
    @ApiModelProperty(name = "status", value = "状态(0=审核中,1=审核通过,2=审核失败,3=已解约)", position = 220)
    private String status;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 230)
    private Date createDatetime;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 240)
    private Date updateDatetime;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注", position = 250)
    private String remark;

}
