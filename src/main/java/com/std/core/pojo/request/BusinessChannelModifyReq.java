package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import java.util.Date;
import lombok.Data;

/**
 * 修改业务渠道关系表
 *
 * <AUTHOR> ycj
 * @since : 2022-02-17 16:06
 */
@Data
public class BusinessChannelModifyReq {

    /**
     * 序号
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 渠道序号
     */
    @ApiModelProperty(name = "channelId", value = "渠道序号", position = 20)
    private Long channelId;

    /**
     * 业务类型
     */
    @ApiModelProperty(name = "bizType", value = "业务类型", position = 30)
    private String bizType;


}
