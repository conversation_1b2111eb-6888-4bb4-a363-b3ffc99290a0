package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 新增数字藏品型号购买订单
 *
 * <AUTHOR> ycj
 * @since : 2021-11-04 16:01
 */
@Data
public class JiGuangPushCreateReq {

    @ApiModelProperty(name = "noticeInfo", value = "推送内容", position = 10, required = true)
    @NotBlank(message = "推送内容不能为空")
    private String noticeInfo;

    @ApiModelProperty(name = "aliaList",value = "推送目标（别名）",position = 20,required = true)
    private List<String> aliaList;

    @ApiModelProperty(name = "activityKey",value = "指定活动key",position = 30)
    private String activityKey;

    @ApiModelProperty(name = "activityId",value = "指定活动Id",position = 30)
    private String activityId;
}
