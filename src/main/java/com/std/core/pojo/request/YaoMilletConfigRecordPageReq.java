package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 分页查询元粟每日配置记录
 *
 * <AUTHOR> ycj
 * @since : 2022-11-14 15:08
 */
@Data
public class YaoMilletConfigRecordPageReq extends BasePageReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 币种
     */
    @ApiModelProperty(name = "currency", value = "币种", position = 20)
    private String currency;

    /**
     * 总数
     */
    @ApiModelProperty(name = "totalQuantity", value = "总数", position = 30)
    private BigDecimal totalQuantity;

    /**
     * 剩余数量
     */
    @ApiModelProperty(name = "remainQuantity", value = "剩余数量", position = 40)
    private BigDecimal remainQuantity;

    /**
     * 日期编号
     */
    @ApiModelProperty(name = "dateNumber", value = "日期编号", position = 50)
    private Integer dateNumber;

    /**
     * 日期
     */
    @ApiModelProperty(name = "dateTime", value = "日期", position = 60)
    private Date dateTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 70)
    private Date createDatetime;

}
