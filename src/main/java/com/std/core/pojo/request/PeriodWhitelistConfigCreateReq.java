package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 新增白名单配置
 *
 * <AUTHOR> wzh
 * @since : 2023-08-18 13:24
 */
@Data
public class PeriodWhitelistConfigCreateReq {

    /**
     * 类型 {0:平台,1:项目} 
     */
    @ApiModelProperty(name = "type", value = "类型 {0:平台,1:项目} ", required = true, position = 20)
    @NotBlank(message = "类型 {0:平台,1:项目} 不能为空")
    private String type;

    /**
     * 项目序号
     */
    @ApiModelProperty(name = "periodId", value = "项目序号", position = 30)
    private Long periodId;

    /**
     * 兑换比例 (X 平台兑换一张项目)
     */
    @ApiModelProperty(name = "rate", value = "兑换比例 (X 平台兑换一张项目)", position = 40)
    @Min(value = 1,message = "兑换比例不能小于 1")
    private Integer rate;

    /**
     * 作品序号
     */
    @ApiModelProperty(name = "collectionId", value = "作品序号", position = 50)
    @NotNull(message = "作品序号不能为空")
    private Long collectionId;



}
