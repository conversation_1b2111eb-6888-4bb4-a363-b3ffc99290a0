package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 列表查询机器人等级配置
 *
 * <AUTHOR> ycj
 * @since : 2022-08-29 10:08
 */
@Data
public class RobotConfigListFrontReq extends BaseListReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 藏品序号
     */
    @ApiModelProperty(name = "collectionId", value = "藏品序号", position = 20)
    private Long collectionId;

    /**
     * 等级
     */
    @ApiModelProperty(name = "level", value = "等级", position = 30)
    private Integer level;

    /**
     * 状态 {0:待启用,1:启用中}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:待启用,1:启用中}", position = 40)
    private String status;

    /**
     * 每小时概率
     */
    @ApiModelProperty(name = "probability", value = "每小时概率", position = 50)
    private BigDecimal probability;

    /**
     * 派遣时间
     */
    @ApiModelProperty(name = "sendTime", value = "派遣时间", position = 60)
    private Integer sendTime;

    /**
     * 单次派遣最大数量
     */
    @ApiModelProperty(name = "maxNumber", value = "单次派遣最大数量", position = 70)
    private Integer maxNumber;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "creater", value = "创建人", position = 80)
    private Long creater;

    /**
     * 创建人名
     */
    @ApiModelProperty(name = "createrName", value = "创建人名", position = 90)
    private String createrName;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 100)
    private Date createDatetime;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 110)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称", position = 120)
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 130)
    private Date updateDatetime;

}
