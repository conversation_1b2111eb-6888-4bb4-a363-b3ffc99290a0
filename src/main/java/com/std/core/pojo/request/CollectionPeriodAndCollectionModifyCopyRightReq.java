package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 新增版权区作品期数
 *
 * <AUTHOR> ycj
 * @since : 2021-11-03 20:27
 */
@Data
public class CollectionPeriodAndCollectionModifyCopyRightReq {

    @NotNull(message = "序号不能为空")
    private Long id;
    /**
     * 名称(宝藏计划展示)
     */
    @ApiModelProperty(name = "name", value = "名称，(0=版权区自取 1=衍生区可不传，取藏品名称)", position = 20)
    private String name;

    /**
     * 板块类别
     */
    @ApiModelProperty(name = "plateCategory", value = "板块类别", position = 21)
    private String plateCategory;

    /**
     * 文件类型
     */
    @ApiModelProperty(name = "fileType", value = "文件类型 0:图片,1:音频,2:视频,3:3d文件", position = 50)
    @NotBlank(message = "文件类型不能为空")
    private String fileType;

    /**
     * 标签列表
     */
    @ApiModelProperty(name = "tagList", value = "标签列表", position = 70)
    private List<String> tagList;

    /**
     * 封面图
     */
    @ApiModelProperty(name = "coverFileUrl", value = "封面图", position = 80)
    private String coverFileUrl;

    /**
     * 作品介绍
     */
    @ApiModelProperty(name = "content", value = "作品介绍(用图片)", position = 90)
    private String content;

    /**
     * 开始发售时间
     */
    @ApiModelProperty(name = "startSellDate", value = "开始发售时间", required = true, position = 110)
    @NotBlank(message = "开始发售时间不能为空")
    private String startSellDate;

    /**
     * 截止发售时间
     */
    @ApiModelProperty(name = "endSellDate", value = "截止发售时间，格式：yyyy-MM-dd HH:mm", position = 110)
    @NotBlank(message = "截止发售时间不能为空")
    private String endSellDate;

    /**
     * 价格
     */
    @ApiModelProperty(name = "price", value = "价格", required = true, position = 120)
    @NotNull(message = "价格不能为空")
    @DecimalMin(value = "0.01", message = "价格不能小于0.01")
    private BigDecimal price;

    /**
     * 单人最大购买份数
     */
    @ApiModelProperty(name = "buyMax", value = "单人最大购买个数", position = 50)
    @NotNull(message = "单人最大购买个数不能为空")
    @Min(value = 0, message = "单人最大购买个数不能小于0")
    private Integer buyMax;

    /**
     * 优先购买权需要作品列表
     */
    @ApiModelProperty(name = "lockTime", value = "优先购买权需要作品列表", position = 70)
    private List<CollectionPeriodPriorityBuyCompanyCreateReq> collectionPeriodPriorityBuyList;


    /**
     * 折扣作品列表
     */
    @ApiModelProperty(name = "discountCollectionList", value = "折扣作品列表", position = 100)
    private List<PeriodDiscountDetailCompanyCreateReq> discountCollectionList;

    /**
     * 口令标志(0=不是口令 1=是口令)
     */
    @ApiModelProperty(name = "wordFlag", value = "口令标志(0=不是口令 1=是口令)")
    private String wordFlag;

    @ApiModelProperty(name = "channelId", value = "分发渠道", position = 36)
    @NotNull(message = "分发渠道不能为空")
    private Long channelId;

    /**
     * 口令列表
     */
    @ApiModelProperty(name = "periodChannelWordList", value = "口令列表", position = 36)
    private List<PeriodChannelWordCreateCompanyReq> periodChannelWordList;

    /**************************************************作品信息**************************************************************************/
    @ApiModelProperty(name = "collectionInfoList", value = "作品信息")
    @Valid
    private List<CollectionCreatePlatformReq> sellCollectionList;

    @ApiModelProperty(name = "showCollectionInfoList", value = "隐藏款作品信息")
    @Valid
    private List<CollectionCreatePlatformReq> showCollectionList;

    @ApiModelProperty(name = "collectionInfoList", value = "修改作品信息")
    @Valid
    private List<CollectionModifyPlatformReq> sellModifyCollectionList;

    @ApiModelProperty(name = "showCollectionInfoList", value = "修改隐藏款作品信息")
    @Valid
    private List<CollectionModifyPlatformReq> showModifyCollectionInfoList;
}
