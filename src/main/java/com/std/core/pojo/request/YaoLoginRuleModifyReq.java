package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;
import lombok.Data;

/**
 * 修改连续登陆送爻规则
 *
 * <AUTHOR> ycj
 * @since : 2022-12-28 10:50
 */
@Data
public class YaoLoginRuleModifyReq {

    /**
     * 序号
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 天数
     */
    @ApiModelProperty(name = "day", value = "天数", required = true, position = 20)
    @NotNull(message = "天数不能为空")
    @Min(value = 1,message = "天数不能小于1")
    private Integer day;

    /**
     * 固定阴爻数量
     */
    @ApiModelProperty(name = "yinYao", value = "固定阴爻数量", required = true, position = 30)
    @NotNull(message = "固定阴爻数量不能为空")
    @DecimalMin(value = "0",message = "固定阴爻数量不能小于0")
    private BigDecimal yinYao;

    /**
     * 固定阳爻数量
     */
    @ApiModelProperty(name = "yangYao", value = "固定阳爻数量", required = true, position = 40)
    @NotNull(message = "固定阳爻数量不能为空")
    @DecimalMin(value = "0",message = "固定阳爻数量不能小于0")
    private BigDecimal yangYao;

    /**
     * 额外阴爻数量
     */
    @ApiModelProperty(name = "extraYinYao", value = "额外阴爻数量", required = true, position = 50)
    @NotNull(message = "额外阴爻数量不能为空")
    @DecimalMin(value = "0",message = "额外阴爻数量不能小于0")
    private BigDecimal extraYinYao;

    /**
     * 额外阳爻数量
     */
    @ApiModelProperty(name = "extraYangYao", value = "额外阳爻数量", required = true, position = 60)
    @NotNull(message = "额外阳爻数量不能为空")
    @DecimalMin(value = "0",message = "额外阳爻数量不能小于0")
    private BigDecimal extraYangYao;

}
