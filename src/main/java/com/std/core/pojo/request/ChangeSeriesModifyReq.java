package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 修改百变大咖系列
 *
 * <AUTHOR> ycj
 * @since : 2021-10-26 14:05
 */
@Data
public class ChangeSeriesModifyReq {

    /**
     * 序号
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称", position = 20)
    private String name;

    /**
     * 图片
     */
    @ApiModelProperty(name = "pic", value = "图片", position = 30)
    private String pic;

    @ApiModelProperty(name = "backPic", value = "盲盒背景图", position = 30)
    private String backPic;

    /**
     * 介绍
     */
    @ApiModelProperty(name = "introduce", value = "介绍", position = 32)
    private String introduce;

    /**
     * 盲盒价格
     */
    @ApiModelProperty(name = "price", value = "价格", position = 32)
    @DecimalMin(value = "0", message = "价格不能小于0")
    private BigDecimal price;

    /**
     * 原创人
     */
    @ApiModelProperty(name = "authorId", value = "原创人", position = 35)
    @NotNull(message = "原创人不能为空")
    private Long authorId;

    /**
     * 开始发售时间
     */
    @ApiModelProperty(name = "startSellDate", value = "开始发售时间", required = true, position = 110)
    @NotBlank(message = "开始发售时间不能为空")
    private String startSellDate;

    /**
     * 结束发售时间
     */
    @ApiModelProperty(name = "endSellDate", value = "结束发售时间", required = true, position = 110)
    @NotBlank(message = "结束发售时间不能为空")
    private String endSellDate;
    
    /**
     * 顺序
     */
    @ApiModelProperty(name = "orderNo", value = "顺序", position = 50)
    private Integer orderNo;

    /**
     * 合约Id
     */
    @ApiModelProperty(name = "contractId", value = "合约Id", position = 121)
    private Long contractId;

    /**
     * 折扣作品列表
     */
    @ApiModelProperty(name = "discountCollectionList", value = "折扣作品列表", position = 100)
    private List<PeriodDiscountDetailCreateReq> discountCollectionList;
}
