package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import java.util.Date;
import lombok.Data;

/**
 * 修改一级市场折扣明细
 *
 * <AUTHOR> ycj
 * @since : 2022-04-28 13:48
 */
@Data
public class PeriodDiscountDetailModifyReq {

    /**
     * 
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "", position = 10)
    private Long id;

    /**
     * 类型 0=期数 1=幸运抽奖
     */
    @ApiModelProperty(name = "refType", value = "类型 0=期数 1=幸运抽奖", position = 20)
    private String refType;

    /**
     * 关联id
     */
    @ApiModelProperty(name = "refId", value = "关联id", position = 30)
    private Long refId;

    /**
     * 作品id
     */
    @ApiModelProperty(name = "collectionId", value = "作品id", position = 40)
    private Long collectionId;

    /**
     * 折扣比例,1不打折
     */
    @ApiModelProperty(name = "discountRate", value = "折扣比例,1不打折", position = 50)
    private BigDecimal discountRate;

    /**
     * 折扣次数,默认0不限制，由期数限制
     */
    @ApiModelProperty(name = "discountTime", value = "折扣次数,默认0不限制，由期数限制", position = 60)
    private Integer discountTime;

}
