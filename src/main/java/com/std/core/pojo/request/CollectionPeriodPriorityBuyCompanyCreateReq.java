package com.std.core.pojo.request;

import com.std.core.util.EnumValue;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 新增作品期数优先购买权
 *
 * <AUTHOR> xieyj
 * @since : 2022-01-24 16:47
 */
@Data
public class CollectionPeriodPriorityBuyCompanyCreateReq {

    /**
     * 作品编号
     */
    @ApiModelProperty(name = "rightId", value = "权益序号", required = true, position = 30)
    private Long rightId;

//    /**
//     * 数量
//     */
//    @ApiModelProperty(name = "quantity", value = "数量", position = 40)
//    private Integer quantity = 1;

    /**
     * 创建类型 0:正式,1:临时
     */
    @ApiModelProperty(name = "createType", value = "创建类型 0:正式,1:临时", position = 50)
    @NotBlank(message = "创建类型不能为空")
    @EnumValue(strValues = {"0", "1"}, message = "创建类型错误")
    private String createType;

    /**
     * 藏品序号
     */
    @ApiModelProperty(name = "collectionId", value = "藏品序号", position = 40)
    private Long collectionId;

    /**
     * 优先抢购分钟数
     */
    @ApiModelProperty(name = "advanceMins", value = "优先抢购分钟数", position = 40)
    private Integer advanceMins;

}
