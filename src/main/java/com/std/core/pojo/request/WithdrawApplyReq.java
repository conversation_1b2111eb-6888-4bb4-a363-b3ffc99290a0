package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 提币申请
 *
 * <AUTHOR> LEO
 * @since : 2020-10-26 16:38
 */
@Data
public class WithdrawApplyReq {

    /**
     * 账户编号
     */
    @ApiModelProperty(name = "accountNumber", value = "账户编号", required = true, position = 10)
    @NotBlank(message = "账户编号不能为空")
    private String accountNumber;

    /**
     * 提币数量
     */
    @ApiModelProperty(name = "amount", value = "提币数量", required = true, position = 20)
    @NotNull(message = "提币数量不能为空")
    @Pattern(regexp = "^[-+]?(([0-9]+)([.]([0-9]+))?|([.]([0-9]+))?)$", message = "提币数量格式不正确")
    private String amount;

    /**
     * 提币地址
     */
    @ApiModelProperty(name = "toAddress", value = "提币地址", position = 21)
    @NotBlank(message = "提币地址不能为空")
    private String toAddress;

    /**
     * 申请说明
     */
    @ApiModelProperty(name = "applyNote", value = "申请说明", position = 30)
    private String applyNote;

    /**
     * 支付密码
     */
    @ApiModelProperty(name = "tradePwd", value = "支付密码", required = true, position = 40)
    @NotBlank(message = "支付密码不能为空")
    private String tradePwd;

    /**
     * 手机验证码
     */
    @NotBlank(message = "手机验证码不能为空")
    @ApiModelProperty(name = "smsCode", value = "手机验证码  业务类型=WITHDRAW", required = true, position = 50)
    private String smsCode;


}
