package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 分页查询爻获取任务配置
 *
 * <AUTHOR> ycj
 * @since : 2022-11-13 09:20
 */
@Data
public class YaoTaskConfigPageReq extends BasePageReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 类型 {0:登陆,1:购买藏品}
     */
    @ApiModelProperty(name = "type", value = "类型 {0:登陆,1:购买藏品}", position = 20)
    private String type;

    /**
     * 状态 {0:关闭,1:开启}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:关闭,1:开启}", position = 30)
    private String status;

    /**
     * 状态列表
     */
    @ApiModelProperty(name = "statusList", value = "状态列表", position = 31)
    private List<String> statusList;

    /**
     * 获得阴爻数量
     */
    @ApiModelProperty(name = "yinYao", value = "获得阴爻数量", position = 40)
    private BigDecimal yinYao;

    /**
     * 获得阳爻数量
     */
    @ApiModelProperty(name = "yangYao", value = "获得阳爻数量", position = 50)
    private BigDecimal yangYao;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 60)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称", position = 70)
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 80)
    private Date updateDatetime;

}
