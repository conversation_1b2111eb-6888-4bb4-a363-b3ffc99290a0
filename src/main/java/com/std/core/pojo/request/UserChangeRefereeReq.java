package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;


/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 16:49
 */
@Data
public class UserChangeRefereeReq {

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "id", value = "用户编号", position = 10)
    @NotNull(message = "用户编号不能为空")
    private Long userId;

    /**
     * 推荐人
     */
    @ApiModelProperty(name = "userReferee", value = "推荐人", position = 20)
    @NotNull(message = "推荐人不能为空")
    private Long userReferee;

}
