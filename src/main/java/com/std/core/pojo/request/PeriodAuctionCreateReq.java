package com.std.core.pojo.request;

import com.std.core.util.EnumValue;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 新增期数竞拍
 *
 * <AUTHOR> ycj
 * @since : 2022-03-29 13:47
 */
@Data
public class PeriodAuctionCreateReq {

    /**
     * 期数编号
     */
    @ApiModelProperty(name = "periodId", value = "期数编号", required = true, position = 20)
    @NotNull(message = "期数编号不能为空")
    private Long periodId;

    /**
     * 支付方式 0:余额支付,7:易宝银行卡,8:易宝钱包
     */
    @ApiModelProperty(name = "payType", value = "支付方式 0:余额支付,7:易宝银行卡,8:易宝钱包", position = 20)
    @NotBlank(message = "支付方式不能为空")
    @EnumValue(strValues = {"7", "8"}, message = "请选择其他支付方式")
    private String payType;

    @ApiModelProperty(name = "pwd", value = "支付密码", position = 39)
    private String pwd;

    @ApiModelProperty(name = "wxAppId", value = "安卓需要", position = 50)
    private String wxAppId;


    @ApiModelProperty(name = "bindCardId", value = "绑定的卡宾id")
    private Long bindCardId;


}
