package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 列表查询积分商品附加信息
 *
 * <AUTHOR> ycj
 * @since : 2022-04-25 23:13
 */
@Data
public class GoodsAdditionalInformationListReq extends BaseListReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 商品序号
     */
    @ApiModelProperty(name = "goodsId", value = "商品序号", position = 20)
    private Long goodsId;

    /**
     * 购买记录序号
     */
    @ApiModelProperty(name = "recordId", value = "购买记录序号", position = 30)
    private Long recordId;

    /**
     * 附加内容
     */
    @ApiModelProperty(name = "content", value = "附加内容", position = 40)
    private String content;

    /**
     * 状态 {0:待使用,1:已使用,2:已废除}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:待使用,1:已使用,2:已废除}", position = 50)
    private String status;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 60)
    private Date createDatetime;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 70)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称", position = 80)
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 90)
    private Date updateDatetime;

}
