package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 修改藏品组件构成
 *
 * <AUTHOR> xieyj
 * @since : 2021-10-27 14:47
 */
@Data
public class ChangeCollectionModifyReq {

    /**
     * 序号
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 藏品序号
     */
    @ApiModelProperty(name = "collectionId", value = "藏品序号", position = 20)
    private Long collectionId;

    /**
     * 类型序号
     */
    @ApiModelProperty(name = "typeId", value = "类型序号", position = 30)
    private Long typeId;

    /**
     * 展示图
     */
    @ApiModelProperty(name = "adsPic", value = "展示图", position = 40)
    private String adsPic;

    /**
     * 组件图
     */
    @ApiModelProperty(name = "pic", value = "组件图", position = 50)
    private String pic;

    /**
     * 价格
     */
    @ApiModelProperty(name = "price", value = "价格", position = 60)
    private BigDecimal price;

    /**
     * 折扣价
     */
    @ApiModelProperty(name = "discountPrice", value = "折扣价", position = 70)
    private BigDecimal discountPrice;

    /**
     * 层级
     */
    @ApiModelProperty(name = "layer", value = "层级", position = 80)
    private Integer layer;

    /**
     * 序号
     */
    @ApiModelProperty(name = "orderNo", value = "序号", position = 90)
    private Integer orderNo;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 100)
    private String createDatetime;

    /**
     * 关联作品权益列表
     */
    @ApiModelProperty(name = "collectionRightList", value = "关联作品权益列表", position = 142)
    private List<CollectionRightCreateReq> collectionRightList;

}
