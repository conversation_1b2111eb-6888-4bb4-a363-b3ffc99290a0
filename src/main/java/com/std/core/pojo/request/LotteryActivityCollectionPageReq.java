package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 分页查询抽奖资格
 *
 * <AUTHOR> ycj
 * @since : 2022-06-01 17:18
 */
@Data
public class LotteryActivityCollectionPageReq extends BasePageReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 活动序号
     */
    @ApiModelProperty(name = "activityId", value = "活动序号", position = 20)
    private Long activityId;

    /**
     * 活动名称
     */
    @ApiModelProperty(name = "activityName", value = "活动名称", position = 30)
    private String activityName;

    /**
     * 藏品序号
     */
    @ApiModelProperty(name = "collectionId", value = "藏品序号", position = 40)
    private Long collectionId;

    /**
     * 藏品名称
     */
    @ApiModelProperty(name = "collectionName", value = "藏品名称", position = 50)
    private String collectionName;

    /**
     * 封面文件地址
     */
    @ApiModelProperty(name = "coverFileUrl", value = "封面文件地址", position = 60)
    private String coverFileUrl;

    /**
     * 数量
     */
    @ApiModelProperty(name = "quantity", value = "数量", position = 70)
    private Integer quantity;

}
