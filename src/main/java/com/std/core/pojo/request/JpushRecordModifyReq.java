package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import java.util.Date;
import lombok.Data;

/**
 * 修改极光推送记录
 *
 * <AUTHOR> ycj
 * @since : 2022-01-11 15:03
 */
@Data
public class JpushRecordModifyReq {

    /**
     * 序号
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 推送平台
     */
    @ApiModelProperty(name = "platform", value = "推送平台", position = 20)
    private String platform;

    /**
     * 推送消息
     */
    @ApiModelProperty(name = "noticeInfo", value = "推送消息", position = 30)
    private String noticeInfo;

    /**
     * 推送目标
     */
    @ApiModelProperty(name = "alias", value = "推送目标", position = 40)
    private String alias;

    /**
     * 指定活动key
     */
    @ApiModelProperty(name = "activityKey", value = "指定活动入参", position = 50)
    private String activityKey;

    /**
     * 指定活动Id
     */
    @ApiModelProperty(name = "activityAction", value = "指定活动类型", position = 60)
    private String activityAction;

    /**
     * 状态 {0:失败,1:成功}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:失败,1:成功}", position = 70)
    private String status;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "creater", value = "创建人", position = 80)
    private Long creater;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(name = "createrName", value = "创建人姓名", position = 90)
    private String createrName;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 100)
    private String createDatetime;

    /**
     * 
     */
    @ApiModelProperty(name = "content", value = "", position = 110)
    private String content;

}
