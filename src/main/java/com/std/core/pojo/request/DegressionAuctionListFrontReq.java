package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 列表查询降价竞拍
 *
 * <AUTHOR> ycj
 * @since : 2022-05-23 16:43
 */
@Data
public class DegressionAuctionListFrontReq extends BaseListReq {

    /**
     * 
     */
    @ApiModelProperty(name = "id", value = "", position = 10)
    private Long id;

    /**
     * 竞拍名称
     */
    @ApiModelProperty(name = "name", value = "竞拍名称", position = 20)
    private String name;

    /**
     * 封面图
     */
    @ApiModelProperty(name = "pic", value = "封面图", position = 30)
    private String pic;

    /**
     * 状态 {0:待上架,1:已上架,2:已下架}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:待上架,1:已上架,2:已下架}", position = 40)
    private String status;

    /**
     * 状态 {0:待开始,1:售卖中,2:已结束}
     */
    @ApiModelProperty(name = "soldStatus", value = "状态 {0:待开始,1:售卖中,2:已结束}", position = 50)
    private String soldStatus;

    /**
     * 保证金
     */
    @ApiModelProperty(name = "bond", value = "保证金", position = 60)
    private BigDecimal bond;

    /**
     * 起拍价
     */
    @ApiModelProperty(name = "startPrice", value = "起拍价", position = 70)
    private BigDecimal startPrice;

    /**
     * 保底价
     */
    @ApiModelProperty(name = "lowestPrice", value = "保底价", position = 80)
    private BigDecimal lowestPrice;

    /**
     * 减价幅度
     */
    @ApiModelProperty(name = "degressionPrice", value = "减价幅度", position = 90)
    private BigDecimal degressionPrice;

    /**
     * 减价时间（分）
     */
    @ApiModelProperty(name = "degressionTime", value = "减价时间（分）", position = 100)
    private Integer degressionTime;

    /**
     * 当前价格
     */
    @ApiModelProperty(name = "currentPrice", value = "当前价格", position = 110)
    private BigDecimal currentPrice;

    /**
     * 最终价
     */
    @ApiModelProperty(name = "finalPrice", value = "最终价", position = 120)
    private BigDecimal finalPrice;

    /**
     * 积分抵扣金额（元）
     */
    @ApiModelProperty(name = "deductionPrice", value = "积分抵扣金额（元）", position = 130)
    private BigDecimal deductionPrice;

    /**
     * 竞价次数
     */
    @ApiModelProperty(name = "auctionTimes", value = "竞价次数", position = 140)
    private Integer auctionTimes;

    /**
     * 拍卖时限(分)
     */
    @ApiModelProperty(name = "timeLimit", value = "拍卖时限(分)", position = 150)
    private Integer timeLimit;

    /**
     * 总数量
     */
    @ApiModelProperty(name = "totalQuantity", value = "总数量", position = 160)
    private Integer totalQuantity;

    /**
     * 库存
     */
    @ApiModelProperty(name = "remainQuantity", value = "库存", position = 170)
    private Integer remainQuantity;

    /**
     * 展示图
     */
    @ApiModelProperty(name = "showPic", value = "展示图", position = 180)
    private String showPic;

    /**
     * 详情图
     */
    @ApiModelProperty(name = "contentPic", value = "详情图", position = 190)
    private String contentPic;

    /**
     * 已变动次数
     */
    @ApiModelProperty(name = "changeTime", value = "已变动次数", position = 200)
    private Integer changeTime;

    /**
     * 下次变动时间
     */
    @ApiModelProperty(name = "nextChangeDatetime", value = "下次变动时间", position = 210)
    private Date nextChangeDatetime;

    /**
     * 开拍时间
     */
    @ApiModelProperty(name = "startTime", value = "开拍时间", position = 220)
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(name = "endTime", value = "结束时间", position = 230)
    private Date endTime;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "creater", value = "创建人", position = 240)
    private Long creater;

    /**
     * 创建人名
     */
    @ApiModelProperty(name = "createrName", value = "创建人名", position = 250)
    private String createrName;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 260)
    private Date createDatetime;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 270)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称", position = 280)
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 290)
    private Date updateDatetime;

    /**
     * 最终结束时间
     */
    @ApiModelProperty(name = "delayedTime", value = "最终结束时间", position = 300)
    private Date delayedTime;

}
