package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 新增百变大咖组件
 *
 * <AUTHOR> ycj
 * @since : 2021-10-25 17:11
 */
@Data
public class ChangeModuleCreateReq {

    /**
     * 类型序号
     */
    @ApiModelProperty(name = "typeId", value = "类型序号", position = 20)
    @NotNull(message = "类型序号不能为空")
    private Long typeId;

    /**
     * 封面图
     */
    @ApiModelProperty(name = "adsPic", value = "封面图", position = 25)
    @NotBlank(message = "封面图不能为空")
    private String adsPic;

    /**
     * 组件图
     */
    @ApiModelProperty(name = "pic", value = "组件图", position = 30)
    @NotBlank(message = "组件图不能为空")
    private String pic;

    /**
     * 价格
     */
    @ApiModelProperty(name = "price", value = "价格", position = 40)
    @NotNull(message = "价格不能为空")
    @DecimalMin(value = "0",message = "价格不能小于0")
    private BigDecimal price;

    /**
     * 折扣价
     */
    @ApiModelProperty(name = "discountPrice", value = "折扣价", position = 45)
    @NotNull(message = "折扣价不能为空")
    @DecimalMin(value = "0",message = "折扣价不能小于0")
    private BigDecimal discountPrice;

    /**
     * 序号
     */
    @ApiModelProperty(name = "orderNo", value = "顺序", position = 70)
    @NotNull(message = "顺序不能为空")
    @Min(value = 0,message = "顺序不能小于0")
    private Integer orderNo;


}
