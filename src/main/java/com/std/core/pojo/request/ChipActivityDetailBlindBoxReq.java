package com.std.core.pojo.request;

import com.std.core.util.EnumValue;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 修改芯片领藏品活动
 *
 * <AUTHOR> ycj
 * @since : 2022-08-24 10:39
 */
@Data
public class ChipActivityDetailBlindBoxReq {

    /**
     * 序号
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 芯片编号
     */
    @ApiModelProperty(name = "chipCode", value = "芯片编号", position = 20)
    private String chipCode;

}
