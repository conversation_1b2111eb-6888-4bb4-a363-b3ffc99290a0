package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import java.util.Date;
import lombok.Data;

/**
 * 修改用户进入元宇宙记录
 *
 * <AUTHOR> ycj
 * @since : 2022-11-13 20:48
 */
@Data
public class UserEntryRecordModifyReq {

    /**
     * 序号
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 用户序号
     */
    @ApiModelProperty(name = "userId", value = "用户序号", position = 20)
    private Long userId;

    /**
     * 门票类型
     */
    @ApiModelProperty(name = "ticketType", value = "门票类型", position = 30)
    private String ticketType;

    /**
     * 门票序号
     */
    @ApiModelProperty(name = "ticketId", value = "门票序号", position = 40)
    private Long ticketId;

    /**
     * 进入时间戳
     */
    @ApiModelProperty(name = "createTime", value = "进入时间戳", position = 50)
    private Long createTime;

    /**
     * 进入时间
     */
    @ApiModelProperty(name = "createDatetime", value = "进入时间", position = 60)
    private String createDatetime;

}
