package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import java.util.Date;
import lombok.Data;

/**
 * 修改期数作品机构口令
 *
 * <AUTHOR> xieyj
 * @since : 2022-06-07 09:45
 */
@Data
public class PeriodChannelWordModifyReq {

    /**
     * 序号
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 作品id
     */
    @ApiModelProperty(name = "periodId", value = "作品id", position = 20)
    private String periodId;

    /**
     * 渠道id
     */
    @ApiModelProperty(name = "channelId", value = "渠道id", position = 30)
    private String channelId;

    /**
     * 口令
     */
    @ApiModelProperty(name = "word", value = "口令", position = 40)
    private String word;

    /**
     * 状态(0=可使用 1=已作废)
     */
    @ApiModelProperty(name = "status", value = "状态(0=可使用 1=已作废)", position = 50)
    private String status;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 80)
    private String updateDatetime;

}
