package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 新增作品期数优先购买权
 *
 * <AUTHOR> xieyj
 * @since : 2022-01-24 16:47
 */
@Data
public class CollectionPeriodPriorityBuyWhitelistCreateReq {

    /**
     * 白名单购买开始时间
     */
    @ApiModelProperty(name = "startDatetime", value = "白名单购买开始时间", position = 70)
    @NotBlank(message = "白名单购买开始时间不能为空")
    private String startDatetime;

    /**
     * 白名单购买结束时间
     */
    @ApiModelProperty(name = "endDatetime", value = "白名单购买结束时间", position = 70)
    @NotBlank(message = "白名单购买结束时间不能为空")
    private String endDatetime;

    /**
     * 白名单价格
     */
    @ApiModelProperty(name = "price", value = "白名单价格", required = true, position = 120)
    @DecimalMin(value = "0", message = "白名单价格不能小于0")
    @NotNull(message = "白名单价格不能为空")
    private BigDecimal price;

    /**
     * 白名单价格
     */
    @ApiModelProperty(name = "buyMax", value = "白名单单人最大购买个数", required = true, position = 120)
    @Min(value = 1, message = "白名单单人最大购买个数不能小于1")
    @NotNull(message = "白名单单人最大购买个数不能为空")
    private Integer buyMax;

    /**
     * 白名单藏品
     */
    @ApiModelProperty(name = "collectionId", value = "白名单藏品", position = 70)
    @NotNull(message = "白名单藏品不能为空")
    private Long collectionId;

    /**
     * 兑换比例 (X 平台兑换一张项目)
     */
    @ApiModelProperty(name = "rate", value = "兑换比例 (X 平台兑换一张项目)", position = 40)
    @Min(value = 1,message = "兑换比例不能小于 1")
    @NotNull(message = "兑换比例不能为空")
    private Integer rate;

    /**
     * 白名单预留数量
     */
    @ApiModelProperty(name = "totalQuantuty", value = "白名单预留数量", position = 70)
    @Min(value = 0,message = "白名单预留数量不能小于 0")
    @NotNull(message = "白名单预留数量不能为空")
    private Integer totalQuantuty;

}
