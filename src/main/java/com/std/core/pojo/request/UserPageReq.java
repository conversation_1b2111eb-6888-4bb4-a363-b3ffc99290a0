package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 分页查询用户
 *
 * <AUTHOR> haiqingzheng
 * @since : 2020-03-02 14:09
 */
@Data
public class UserPageReq extends BasePageReq {

    /**
     * 编号
     */
    @ApiModelProperty(name = "id", value = "编号")
    private Long id;

    @ApiModelProperty(name = "type", value = "创作者类型（0=机构 1=个人）", required = true)
    private String type;

    /**
     * 类型（运维中心OPS 管理端=SYS C=C端 CA=创作者）
     */
    @ApiModelProperty(name = "kind", value = "类型（运维中心OPS 管理端SYS C端 CA=创作者")
    private String kind;

    /**
     * 登录名称
     */
    @ApiModelProperty(name = "loginName", value = "登录名称")
    private String loginName;

    /**
     * 真实名称
     */
    @ApiModelProperty(name = "realName", value = "真实名称")
    private String realName;

    /**
     * 手机号
     */
    @ApiModelProperty(name = "mobile", value = "手机号")
    private String mobile;

    /**
     * 邮箱
     */
    @ApiModelProperty(name = "email", value = "邮箱")
    private String email;

    /**
     * 状态
     */
    @ApiModelProperty(name = "status", value = "状态")
    private String status;

    @ApiModelProperty(name = "companyId", value = "发行方id")
    private Long companyId;

    @ApiModelProperty(name = "channelId", value = "渠道id")
    private Long channelId;

    @ApiModelProperty(name = "loginStatus", value = "登陆状态 0:重新输入,1:输入验证码,2:锁定中")
    private String loginStatus;

    private String mainFlag;

    private String companyName;

    private String liveFlag;

    private String mainFlagYes;
    private String mainFlagNo;

    private String mobileForQuery;

}
