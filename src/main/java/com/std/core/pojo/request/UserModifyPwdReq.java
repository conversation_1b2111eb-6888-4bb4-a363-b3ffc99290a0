package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;


/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 16:49
 */
@Data
public class UserModifyPwdReq {

    @ApiModelProperty(name = "oldLoginPwd", value = "原密码", required = true)
    @NotBlank(message = "原密码不能为空")
    private String oldLoginPwd;

    @ApiModelProperty(name = "newLoginPwd", value = "新密码", required = true)
    @NotBlank(message = "新密码不能为空")
    private String newLoginPwd;
}
