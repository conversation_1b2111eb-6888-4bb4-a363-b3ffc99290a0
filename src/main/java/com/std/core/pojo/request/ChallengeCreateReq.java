package com.std.core.pojo.request;

import com.std.core.util.EnumValue;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 新增挑战
 *
 * <AUTHOR> ycj
 * @since : 2021-12-28 10:49
 */
@Data
public class ChallengeCreateReq {

    /**
     * 机构id
     */
    @ApiModelProperty(name = "companyId", value = "机构id", required = true, position = 20)
//    @NotNull(message = "机构id不能为空")
    private Long companyId;

    /**
     * 分发渠道
     */
    @ApiModelProperty(name = "channelId", value = "分发渠道", position = 20)
    @NotNull(message = "分发渠道不能为空")
    private Long channelId;

    /**
     * 挑战名称
     */
    @ApiModelProperty(name = "name", value = "挑战名称", required = true, position = 30)
    @NotBlank(message = "挑战名称不能为空")
    @Length(min = 1,max = 60,message = "挑战名称过长")
    private String name;

    /**
     * 封面图
     */
    @ApiModelProperty(name = "coverPicUrl", value = "封面图", position = 40)
    @NotBlank(message = "封面图不能为空")
    private String coverPicUrl;

    /**
     * 开始时间
     */
    @ApiModelProperty(name = "startTime", value = "开始时间", required = true, position = 50)
    @NotBlank(message = "开始时间不能为空")
    private String startTime;

    /**
     * 有效时长
     */
    @ApiModelProperty(name = "effectiveHours", value = "有效时长", required = true, position = 60)
    @NotNull(message = "有效时长不能为空")
    @Min(value = 1, message = "有效时长不能小于1")
    private Integer effectiveHours;

    /**
     * 兑换类型 {0:NFT,1:实物}
     */
    @ApiModelProperty(name = "type", value = "兑换类型 {0:NFT,1:实物}", position = 80)
    @NotBlank(message = "兑换类型不能为空")
    @EnumValue(strValues = {"0", "1"}, message = "兑换类型错误")
    private String type;

    /**
     * 奖品id
     */
    @ApiModelProperty(name = "awardRefId", value = "奖品id", position = 90)
    @NotNull(message = "奖品id不能为空")
    private Long awardRefId;

    /**
     * 奖品总数量
     */
    @ApiModelProperty(name = "awardQuantity", value = "奖品总数量", position = 100)
    @NotNull(message = "奖品总数量不能为空")
    @Min(value = 1, message = "奖品总数量不能小于1")
    private Integer awardQuantity;

    /**
     * 申请说明
     */
    @ApiModelProperty(name = "applyNote", value = "申请说明", position = 160)
    @Length(min = 0,max = 255,message = "申请说明过长")
    private String applyNote;
}
