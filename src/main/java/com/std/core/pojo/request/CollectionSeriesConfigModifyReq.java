package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import java.util.Date;
import lombok.Data;

/**
 * 修改流转作品配置
 *
 * <AUTHOR> wzh
 * @since : 2023-08-08 19:23
 */
@Data
public class CollectionSeriesConfigModifyReq {

    /**
     * 序号
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 系列类型
     */
    @ApiModelProperty(name = "seriesType", value = "系列类型", position = 20)
    private String seriesType;

    /**
     * 作品
     */
    @ApiModelProperty(name = "collectionId", value = "作品", position = 30)
    private Long collectionId;

    /**
     * 系列序号
     */
    @ApiModelProperty(name = "seriesId", value = "系列序号", position = 40)
    private Long seriesId;

    /**
     * 版权费
     */
    @ApiModelProperty(name = "copyrightFee", value = "版权费", position = 50)
    private BigDecimal copyrightFee;

    /**
     * 开始时间
     */
    @ApiModelProperty(name = "startDatetime", value = "开始时间", position = 60)
    private String startDatetime;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "creater", value = "创建人", position = 70)
    private Long creater;

    /**
     * 创建人名
     */
    @ApiModelProperty(name = "createrName", value = "创建人名", position = 80)
    private String createrName;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 90)
    private String createDatetime;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 120)
    private String updateDatetime;

}
