package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 分页查询史诗折扣统计
 *
 * <AUTHOR> xieyj
 * @since : 2022-04-16 22:40
 */
@Data
public class HisStaticPageReq extends BasePageReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号", position = 20)
    private Long userId;

    /**
     * 用户关键信息（手机号/身份证/姓名）
     */
    @ApiModelProperty(name = "keywords", value = "用户关键信息（手机号/身份证/姓名）", position = 21)
    private String keywords;

    /**
     * 一级中签折扣金额
     */
    @ApiModelProperty(name = "firstAmount", value = "一级中签折扣金额", position = 30)
    private BigDecimal firstAmount;

    /**
     * 幸运抽奖折扣金额
     */
    @ApiModelProperty(name = "luckAmount", value = "幸运抽奖折扣金额", position = 40)
    private BigDecimal luckAmount;

    /**
     * 提现折扣金额
     */
    @ApiModelProperty(name = "withdrawAmount", value = "提现折扣金额", position = 50)
    private BigDecimal withdrawAmount;

    /**
     * 总折扣金额
     */
    @ApiModelProperty(name = "totalAmount", value = "总折扣金额", position = 60)
    private BigDecimal totalAmount;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 70)
    private Date createDatetime;

}
