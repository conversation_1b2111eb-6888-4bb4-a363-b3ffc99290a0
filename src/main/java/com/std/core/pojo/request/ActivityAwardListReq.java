package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 列表查询新春活动奖励
 *
 * <AUTHOR> ycj
 * @since : 2022-01-21 21:04
 */
@Data
public class ActivityAwardListReq extends BaseListReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 活动序号
     */
    @ApiModelProperty(name = "acvtivityId", value = "活动序号", position = 20)
    private Long acvtivityId;

    /**
     * 奖励类型
     */
    @ApiModelProperty(name = "type", value = "奖励类型 {0:nft,1:实物}", position = 30)
    private String type;

    /**
     * 奖励序号
     */
    @ApiModelProperty(name = "awardId", value = "奖励序号", position = 40)
    private Integer awardId;

    /**
     * 奖励份数
     */
    @ApiModelProperty(name = "awardNumber", value = "奖励份数", position = 50)
    private Integer awardNumber;

    /**
     * 奖励等级(1=第一名 2=第二名)
     */
    @ApiModelProperty(name = "level", value = "奖励等级(1=第一名 2=第二名)", position = 60)
    private Integer level;

    /**
     * 估值
     */
    @ApiModelProperty(name = "price", value = "估值", position = 70)
    private BigDecimal price;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "creater", value = "创建人", position = 80)
    private Long creater;

    /**
     * 创建人名
     */
    @ApiModelProperty(name = "createName", value = "创建人名", position = 90)
    private String createName;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 100)
    private Date createDatetime;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 110)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称", position = 120)
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 130)
    private Date updateDatetime;

}
