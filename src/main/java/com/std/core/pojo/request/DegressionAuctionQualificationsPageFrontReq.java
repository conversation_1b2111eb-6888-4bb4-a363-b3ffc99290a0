package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 分页查询降价竞拍资格
 *
 * <AUTHOR> ycj
 * @since : 2022-05-23 16:46
 */
@Data
public class DegressionAuctionQualificationsPageFrontReq extends BasePageReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 竞拍序号
     */
    @ApiModelProperty(name = "auctionId", value = "竞拍序号", position = 20)
    private Long auctionId;

    /**
     * 藏品
     */
    @ApiModelProperty(name = "collectionId", value = "藏品", position = 30)
    private Long collectionId;

    /**
     * 藏品名称
     */
    @ApiModelProperty(name = "collectionName", value = "藏品名称", position = 40)
    private String collectionName;

    /**
     * 封面文件地址
     */
    @ApiModelProperty(name = "coverFileUrl", value = "封面文件地址", position = 50)
    private String coverFileUrl;

    /**
     * 数量
     */
    @ApiModelProperty(name = "quantity", value = "数量", position = 60)
    private Integer quantity;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 70)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称", position = 80)
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 90)
    private Date updateDatetime;

}
