package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 列表查询商品竞拍出价记录
 *
 * <AUTHOR> xieyj
 * @since : 2021-07-11 17:12
 */
@Data
public class AuctionRecordListReq extends BaseListReq {

    /**
     * 
     */
    @ApiModelProperty(name = "id", value = "", position = 10)
    private Long id;

    /**
     * 商品id
     */
    @ApiModelProperty(name = "productId", value = "商品id", position = 20)
    private Long productId;

    /**
     * 用户id
     */
    @ApiModelProperty(name = "userId", value = "用户id", position = 30)
    private Long userId;

    /**
     * 出价金额
     */
    @ApiModelProperty(name = "price", value = "出价金额", position = 40)
    private BigDecimal price;

    /**
     * 出价时间
     */
    @ApiModelProperty(name = "postTime", value = "出价时间", position = 50)
    private Date postTime;

}
