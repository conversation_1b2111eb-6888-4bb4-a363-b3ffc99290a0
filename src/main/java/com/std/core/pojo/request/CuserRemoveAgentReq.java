package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2020/6/6 4:29 下午
 */
@Data
public class CuserRemoveAgentReq {

    /**
     * 用户ID
     */
    @ApiModelProperty(name = "userId", value = "用户ID", required = true, position = 1)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注", position = 2)
    private String remark;


}

    
    