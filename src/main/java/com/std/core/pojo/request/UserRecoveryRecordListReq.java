package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 列表查询平台清退用户记录
 *
 * <AUTHOR> ycj
 * @since : 2022-05-17 15:40
 */
@Data
public class UserRecoveryRecordListReq extends BaseListReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 清退用户
     */
    @ApiModelProperty(name = "userId", value = "清退用户", position = 20)
    private Long userId;

    /**
     * 操作人
     */
    @ApiModelProperty(name = "creater", value = "操作人", position = 30)
    private Long creater;

    /**
     * 操作人名称
     */
    @ApiModelProperty(name = "createrName", value = "操作人名称", position = 40)
    private String createrName;

    /**
     * 操作时间
     */
    @ApiModelProperty(name = "createDatetime", value = "操作时间", position = 50)
    private Date createDatetime;

}
