package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 分页查询兑换条件
 *
 * <AUTHOR> ycj
 * @since : 2022-05-06 17:34
 */
@Data
public class PitConditionsPageFrontReq extends BasePageReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 售卖序号
     */
    @ApiModelProperty(name = "sellerId", value = "售卖序号", position = 20)
    private Long sellerId;

    /**
     * 坑位序号
     */
    @ApiModelProperty(name = "pitId", value = "坑位序号", position = 30)
    private Long pitId;

    /**
     * 意向用户
     */
    @ApiModelProperty(name = "userId", value = "意向用户", position = 40)
    private Long userId;

    /**
     * 用户关键信息（手机号/身份证/姓名）
     */
    @ApiModelProperty(name = "keywords", value = "用户关键信息（手机号/身份证/姓名）", position = 41)
    private String keywords;

    /**
     * 状态 {0:进行中,1:已预定,2:已成交,3:已违约}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:进行中,1:已预定,2:已成交,3:已违约}", position = 50)
    private String status;

    /**
     * 状态列表
     */
    @ApiModelProperty(name = "statusList", value = "状态列表", position = 51)
    private List<String> statusList;

    /**
     * 意向顺位
     */
    @ApiModelProperty(name = "orderNo", value = "意向顺位", position = 60)
    private Integer orderNo;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 70)
    private Date createDatetime;

}
