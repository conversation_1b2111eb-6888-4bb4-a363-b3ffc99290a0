package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 分页查询活动用户兑换记录
 *
 * <AUTHOR> ycj
 * @since : 2022-01-22 15:40
 */
@Data
public class ActivityUserExchangePageFrontReq extends BasePageReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 活动记录
     */
    @ApiModelProperty(name = "activityId", value = "活动记录", position = 20)
    private Long activityId;

    /**
     * 用户序号
     */
    @ApiModelProperty(name = "userId", value = "用户序号", position = 30)
    private Long userId;

    /**
     * 用户关键信息（手机号/身份证/姓名）
     */
    @ApiModelProperty(name = "keywords", value = "用户关键信息（手机号/身份证/姓名）", position = 31)
    private String keywords;

    /**
     * 藏品序号
     */
    @ApiModelProperty(name = "collectionDetailId", value = "藏品序号", position = 40)
    private Long collectionDetailId;

    /**
     * 状态 {"0":"待分配","1":"已分配"}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:待分配,1:已分配}", position = 50)
    private String status;

    /**
     * 状态列表
     */
    @ApiModelProperty(name = "statusList", value = "状态列表", position = 51)
    private List<String> statusList;

    /**
     * 兑换时间
     */
    @ApiModelProperty(name = "createDatetime", value = "兑换时间", position = 60)
    private Date createDatetime;

}
