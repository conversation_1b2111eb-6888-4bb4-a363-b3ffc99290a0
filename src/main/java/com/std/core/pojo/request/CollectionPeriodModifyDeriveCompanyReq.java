package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 修改衍生区作品期数
 *
 * <AUTHOR> ycj
 * @since : 2021-11-03 20:27
 */
@Data
public class CollectionPeriodModifyDeriveCompanyReq {

    /**
     * id
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "id", position = 10)
    private Long id;

    /**
     * 藏品编号
     */
    @ApiModelProperty(name = "collectionId", value = "藏品编号", required = true, position = 21)
    @NotNull(message = "藏品编号不能为空")
    private Long collectionId;

    /**
     * 板块类别
     */
    @ApiModelProperty(name = "plateCategory", value = "板块类别", position = 21)
    private String plateCategory;

    /**
     * 开始发售时间
     */
    @ApiModelProperty(name = "startSellDate", value = "开始发售时间", required = true, position = 30)
    @NotBlank(message = "开始发售时间不能为空")
    private String startSellDate;

    /**
     * 总数量
     */
    @ApiModelProperty(name = "totalQuantity", value = "总数量", required = true, position = 40)
    @NotNull(message = "总数量不能为空")
    @Min(value = 0, message = "总数量不能小于0")
    private Integer totalQuantity;

//    /**
//     * 价格
//     */
//    @ApiModelProperty(name = "price", value = "价格", required = true, position = 60)
//    @NotNull(message = "价格不能为空")
//    @DecimalMin(value = "0", message = "价格不能小于0")
//    private BigDecimal price;

    /**
     * 单人最大购买份数
     */
    @ApiModelProperty(name = "buyMax", value = "单人最大购买份数(版权区选填，衍生区必填)", position = 50)
    @NotNull(message = "单人最大购买份数不能为空")
    @Min(value = 0, message = "单人最大购买份数不能小于0")
    private Integer buyMax;

    /**
     * 优先抢购分钟数
     */
    @ApiModelProperty(name = "advanceMins", value = "提前抢购分钟数，默认0", position = 70)
    @Min(value = 0, message = "优先抢购分钟数不能小于0")
    private Integer advanceMins;

    /**
     * 优先购买权需要作品列表
     */
    @ApiModelProperty(name = "lockTime", value = "优先购买权需要作品列表", position = 70)
    private List<CollectionPeriodPriorityBuyCreateReq> collectionPeriodPriorityBuyList;

    /**
     * 折扣作品列表
     */
    @ApiModelProperty(name = "discountCollectionList", value = "折扣作品列表", position = 100)
    private List<PeriodDiscountDetailCreateReq> discountCollectionList;

    @ApiModelProperty(name = "channelId", value = "分发渠道", position = 36)
    @NotNull(message = "分发渠道不能为空")
    private Long channelId;

    /**
     * 口令列表
     */
    @ApiModelProperty(name = "periodChannelWordList", value = "口令列表", position = 36)
//    @NotEmpty(message = "口令列表")
    private List<PeriodChannelWordCreateCompanyReq> periodChannelWordList;
}
