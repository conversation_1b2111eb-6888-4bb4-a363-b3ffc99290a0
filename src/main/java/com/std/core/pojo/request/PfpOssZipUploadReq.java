package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * PFP OSS ZIP文件处理请求
 *
 * <AUTHOR> system
 * @since : 2024-07-30
 */
@Data
public class PfpOssZipUploadReq {

    /**
     * 期数ID
     */
    @NotNull(message = "期数ID不能为空")
    @ApiModelProperty(name = "periodId", value = "期数ID", required = true, position = 10)
    private Long periodId;

    /**
     * OSS上的ZIP文件URL
     */
    @NotBlank(message = "ZIP文件URL不能为空")
    @ApiModelProperty(name = "zipFileUrl", value = "OSS上的ZIP文件URL", required = true, position = 20)
    private String zipFileUrl;

    /**
     * 图片命名规则
     */
    @ApiModelProperty(name = "namingRule", value = "图片命名规则：AUTO-自动编号，FILENAME-使用文件名", position = 30)
    private String namingRule = "AUTO";

    /**
     * 起始编号（当命名规则为AUTO时使用）
     */
    @ApiModelProperty(name = "startNumber", value = "起始编号", position = 40)
    private Integer startNumber = 1;

    /**
     * 图片前缀（用于OSS存储路径）
     */
    @ApiModelProperty(name = "imagePrefix", value = "图片前缀", position = 50)
    private String imagePrefix = "pfp";

    /**
     * 是否保持ZIP内的目录结构
     */
    @ApiModelProperty(name = "keepDirectoryStructure", value = "是否保持ZIP内的目录结构", position = 60)
    private Boolean keepDirectoryStructure = false;

    /**
     * 支持的图片格式（逗号分隔）
     */
    @ApiModelProperty(name = "supportedFormats", value = "支持的图片格式", position = 70)
    private String supportedFormats = "jpg,jpeg,png,gif,bmp,webp";

    /**
     * 是否处理完成后删除原ZIP文件
     */
    @ApiModelProperty(name = "deleteAfterProcess", value = "是否处理完成后删除原ZIP文件", position = 80)
    private Boolean deleteAfterProcess = true;
}
