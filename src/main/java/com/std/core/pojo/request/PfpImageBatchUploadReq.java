package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * PFP图片批量上传请求
 *
 * <AUTHOR> system
 * @since : 2024-07-30
 */
@Data
public class PfpImageBatchUploadReq {

    /**
     * 期数ID
     */
    @NotNull(message = "期数ID不能为空")
    @ApiModelProperty(name = "periodId", value = "期数ID", required = true, position = 10)
    private Long periodId;

    /**
     * 图片文件列表
     */
    @NotNull(message = "图片文件不能为空")
    @ApiModelProperty(name = "imageFiles", value = "图片文件列表", required = true, position = 20)
    private List<MultipartFile> imageFiles;

    /**
     * 图片命名规则
     */
    @ApiModelProperty(name = "namingRule", value = "图片命名规则：AUTO-自动编号，FILENAME-使用文件名", position = 30)
    private String namingRule = "AUTO";

    /**
     * 起始编号（当命名规则为AUTO时使用）
     */
    @ApiModelProperty(name = "startNumber", value = "起始编号", position = 40)
    private Integer startNumber = 1;

    /**
     * 图片前缀（用于OSS存储路径）
     */
    @ApiModelProperty(name = "imagePrefix", value = "图片前缀", position = 50)
    private String imagePrefix = "pfp";
}
