package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 列表查询机器人挖宝活动奖品
 *
 * <AUTHOR> ycj
 * @since : 2022-08-29 16:33
 */
@Data
public class RobotActivityAwardListFrontReq extends BaseListReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 奖品类型 {0:藏品,1:道具,2:事物}
     */
    @ApiModelProperty(name = "refType", value = "奖品类型 {0:藏品,1:道具,2:事物}", position = 20)
    private String refType;

    /**
     * 关联序号
     */
    @ApiModelProperty(name = "refId", value = "关联序号", position = 30)
    private Long refId;

    /**
     * 状态 {0:待上架,1:上架中,2:已下架}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:待上架,1:上架中,2:已下架}", position = 40)
    private String status;

    /**
     * 封面图
     */
    @ApiModelProperty(name = "pic", value = "封面图", position = 50)
    private String pic;

    /**
     * 总数量
     */
    @ApiModelProperty(name = "totalQuantity", value = "总数量", position = 60)
    private Integer totalQuantity;

    /**
     * 剩余数量
     */
    @ApiModelProperty(name = "remainQuantity", value = "剩余数量", position = 70)
    private Integer remainQuantity;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "creater", value = "创建人", position = 80)
    private Long creater;

    /**
     * 创建人名
     */
    @ApiModelProperty(name = "createrName", value = "创建人名", position = 90)
    private String createrName;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 100)
    private Date createDatetime;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 110)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称", position = 120)
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 130)
    private Date updateDatetime;

}
