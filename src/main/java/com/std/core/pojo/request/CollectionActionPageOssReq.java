package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分页查询作品收藏
 *
 * <AUTHOR> xieyj
 * @since : 2021-07-08 09:36
 */
@Data
public class CollectionActionPageOssReq extends BasePageReq {

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号", position = 30)
    private Long userId;

    /**
     * 作品id/作品包id
     */
    @ApiModelProperty(name = "refId", value = "作品id/作品包id", position = 31)
    private Long refId;
}
