package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 列表查询发行方信息修改记录
 *
 * <AUTHOR> ycj
 * @since : 2022-07-01 13:56
 */
@Data
public class CompanyModifyRecordListReq extends BaseListReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 发行方序号
     */
    @ApiModelProperty(name = "companyId", value = "发行方序号", position = 20)
    private Long companyId;

    /**
     * 状态 {0:待审核,1:审核成功,2:审核失败}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:待审核,1:审核成功,2:审核失败}", position = 30)
    private String status;

    /**
     * 审核内容
     */
    @ApiModelProperty(name = "content", value = "审核内容", position = 40)
    private String content;

    /**
     * 审核人
     */
    @ApiModelProperty(name = "updater", value = "审核人", position = 50)
    private Long updater;

    /**
     * 审核人名称
     */
    @ApiModelProperty(name = "updaterName", value = "审核人名称", position = 60)
    private String updaterName;

    /**
     * 审核时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "审核时间", position = 70)
    private Date updateDatetime;

}
