package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 修改降价竞拍
 *
 * <AUTHOR> ycj
 * @since : 2022-05-23 16:43
 */
@Data
public class DegressionAuctionModifyOrderNoReq {

    /**
     * 
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "", position = 10)
    private Long id;

    @ApiModelProperty(name = "orderNo", value = "顺序", position = 270)
    @Min(value = 0,message = "顺序不能小于0")
    @NotNull(message = "顺序不能为空")
    private Integer orderNo;
}
