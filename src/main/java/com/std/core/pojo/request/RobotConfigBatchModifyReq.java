package com.std.core.pojo.request;

import com.std.core.util.EnumValue;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * 修改机器人等级配置
 *
 * <AUTHOR> ycj
 * @since : 2022-08-29 10:08
 */
@Data
public class RobotConfigBatchModifyReq {

    /**
     * 序号
     */
    @NotEmpty(message = "idList不能为空")
    @ApiModelProperty(name = "idList", value = "序号", position = 10)
    private List<Long> idList;

    /**
     * 状态 {0:上架,2:下架}
     */
    @NotBlank(message = "status不能为空")
    @ApiModelProperty(name = "status", value = "状态 {0:上架,2:下架}", position = 40)
    @EnumValue(strValues = {"0", "1"}, message = "状态错误")
    private String status;

}
