package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 新增apikey 配置
 *
 * <AUTHOR> xieyj
 * @since : 2021-02-03 13:38
 */
@Data
public class CheckBundlingReq {


    @NotBlank(message = "交易所不能为空")
    @ApiModelProperty(name = "exchangeNo", value = "交易所", required = true, position = 30)
    private String exchangeNo;

  
}
