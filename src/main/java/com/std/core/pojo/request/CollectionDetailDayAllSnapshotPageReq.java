package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 分页查询某天共有的用户藏品清单
 *
 * <AUTHOR> ycj
 * @since : 2022-04-16 15:55
 */
@Data
public class CollectionDetailDayAllSnapshotPageReq extends BasePageReq {

    /**
     * 
     */
    @ApiModelProperty(name = "id", value = "", position = 10)
    private Long id;

    /**
     * 藏品型号id
     */
    @ApiModelProperty(name = "collectionDetailId", value = "藏品型号id", position = 20)
    private Long collectionDetailId;

    /**
     * 拥有者id
     */
    @ApiModelProperty(name = "ownerId", value = "拥有者id", position = 30)
    private Long ownerId;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 40)
    private Date createDatetime;

}
