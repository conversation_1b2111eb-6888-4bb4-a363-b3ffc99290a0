package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 列表查询挑战条件明细
 *
 * <AUTHOR> xieyj
 * @since : 2022-05-26 11:13
 */
@Data
public class ChallengeConditionDetailListFrontReq extends BaseListReq {

    /**
     * 
     */
    @ApiModelProperty(name = "id", value = "", position = 10)
    private Long id;

    /**
     * 挑战id
     */
    @ApiModelProperty(name = "challengeId", value = "挑战id", position = 20)
    private Long challengeId;

    /**
     * 条件id
     */
    @ApiModelProperty(name = "conditionId", value = "条件id", position = 30)
    private Long conditionId;

    /**
     * 作品id
     */
    @ApiModelProperty(name = "collectionId", value = "作品id", position = 40)
    private Long collectionId;

    /**
     * 作品需要拥有数量
     */
    @ApiModelProperty(name = "collectionQuantity", value = "作品需要拥有数量", position = 50)
    private Integer collectionQuantity;

}
