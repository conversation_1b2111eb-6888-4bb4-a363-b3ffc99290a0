package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * 分页查询我的坑位售卖
 *
 * <AUTHOR> ycj
 * @since : 2022-05-06 16:19
 */
@Data
public class PitSellerPageFrontMyJoinReq extends BasePageReq {

    /**
     * 状态 {0:进行中,1:已结束,2:失败}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:进行中,1:已结束,2:失败}", position = 40)
    private String status;

    /**
     * 状态列表
     */
    @ApiModelProperty(name = "statusList", value = "状态列表", position = 41)
    private List<String> statusList;
}
