package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * H5授权回调请求
 *
 * <AUTHOR> system
 * @since : 2024-07-29
 */
@Data
public class H5AuthCallbackReq {

    /**
     * 用户唯一标识
     */
    @NotBlank(message = "用户唯一标识不能为空")
    @ApiModelProperty(name = "uniqueId", value = "用户唯一标识", required = true, position = 10)
    private String uniqueId;

    /**
     * 授权状态（可选，用于验证）
     */
    @ApiModelProperty(name = "isAuthorize", value = "授权状态 1:已授权 0:拒绝授权", position = 20)
    private String isAuthorize;
}
