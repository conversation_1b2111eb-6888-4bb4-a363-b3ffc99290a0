package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * PFP图片URL批量上传请求
 *
 * <AUTHOR> system
 * @since : 2024-07-30
 */
@Data
public class PfpImageUrlBatchUploadReq {

    /**
     * 期数ID
     */
    @NotNull(message = "期数ID不能为空")
    @ApiModelProperty(name = "periodId", value = "期数ID", required = true, position = 10)
    private Long periodId;

    /**
     * 图片URL列表
     */
    @NotEmpty(message = "图片URL列表不能为空")
    @ApiModelProperty(name = "imageUrls", value = "图片URL列表", required = true, position = 20)
    private List<String> imageUrls;

    /**
     * 图片命名规则
     */
    @ApiModelProperty(name = "namingRule", value = "图片命名规则：AUTO-自动编号，FILENAME-使用文件名", position = 30)
    private String namingRule = "AUTO";

    /**
     * 起始编号（当命名规则为AUTO时使用）
     */
    @ApiModelProperty(name = "startNumber", value = "起始编号", position = 40)
    private Integer startNumber = 1;

    /**
     * 图片前缀（用于OSS存储路径）
     */
    @ApiModelProperty(name = "imagePrefix", value = "图片前缀", position = 50)
    private String imagePrefix = "pfp";
}
