package com.std.core.pojo.request;

import com.std.core.util.EnumValue;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 修改业务渠道关系表
 *
 * <AUTHOR> ycj
 * @since : 2022-02-17 16:06
 */
@Data
public class BusinessChannelBatchModifyReq {

    /**
     * 序号
     */
    @NotEmpty(message = "idList不能为空")
    @ApiModelProperty(name = "idList", value = "序号", position = 10)
    private List<Long> idList;

    @ApiModelProperty(name = "status", value = "状态 0:关闭，1：开启", position = 30)
    @NotBlank(message = "状态不能为空")
    @EnumValue(strValues = {"0", "1"}, message = "参数错误")
    private String status;
}
