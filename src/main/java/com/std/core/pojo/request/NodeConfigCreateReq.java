package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 新增星级节点配置
 *
 * <AUTHOR> Leo
 * @since : 2020-06-07 15:24
 */
@Data
public class NodeConfigCreateReq {

      /**
       * 节点名称
       */
      @ApiModelProperty(name = "name", value = "节点名称", required = true, position = 20)
      @NotBlank(message = "节点名称不能为空")
      private String name;

      /**
       * 节点星级
       */
      @ApiModelProperty(name = "level", value = "节点星级", required = true, position = 30)
      @NotNull(message = "节点星级不能为空")
      private String level;

      /**
       * 团队业绩最小值
       */
      @ApiModelProperty(name = "teamPerformance", value = "团队业绩最小值", required = true, position = 40)
      @NotNull(message = "团队业绩最小值不能为空")
      private BigDecimal teamPerformance;

      /**
       * 直推团队最小数量
       */
      @ApiModelProperty(name = "firstTeamNumber", value = "直推团队最小数量", required = true, position = 50)
      @NotNull(message = "直推团队最小数量不能为空")
      private Integer firstTeamNumber;

      /**
       * 直推团队业绩最小值
       */
      @ApiModelProperty(name = "firstTeamPerformance", value = "直推团队业绩最小值", required = true, position = 60)
      @NotNull(message = "直推团队业绩最小值不能为空")
      private BigDecimal firstTeamPerformance;

      /**
       * 直推人员星级
       */
      @ApiModelProperty(name = "firstPersonLevel", value = "直推人员星级", required = true, position = 70)
      @NotNull(message = "直推人员星级不能为空")
      private Integer firstPersonLevel;

      /**
       * 直推人员星级节点人数最小值
       */
      @ApiModelProperty(name = "firstPersonLevelNumber", value = "直推人员星级节点人数最小值", required = true, position = 80)
      @NotNull(message = "直推人员星级节点人数最小值不能为空")
      private Integer firstPersonLevelNumber;

      /**
       * 节点收益比例
       */
      @ApiModelProperty(name = "rate", value = "节点收益比例", required = true, position = 90)
      @NotNull(message = "节点收益比例不能为空")
      private BigDecimal rate;

      /**
       * 创建时间
       */
      @ApiModelProperty(name = "createTime", value = "创建时间", required = true, position = 120)
      @NotBlank(message = "创建时间不能为空")
      private String createTime;

      /**
       * 最后更新时间
       */
      @ApiModelProperty(name = "updateTime", value = "最后更新时间", required = true, position = 150)
      private String updateTime;


}
