package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 新增版权区作品期数
 *
 * <AUTHOR> ycj
 * @since : 2021-11-03 20:27
 */
@Data
public class CollectionPeriodCreateBlindBoxReq {

    /**
     * 名称(宝藏计划展示)
     */
    @ApiModelProperty(name = "name", value = "名称，(0=版权区自取 1=衍生区可不传，取藏品名称)", position = 20)
    private String name;
    /**
     * 板块类别
     */
    @ApiModelProperty(name = "plateCategory", value = "板块类别", position = 21)
    private String plateCategory;

    /**
     * 文件类型
     */
    @ApiModelProperty(name = "fileType", value = "文件类型 0:图片,1:音频,2:视频,3:3d文件", position = 50)
    @NotBlank(message = "文件类型不能为空")
    private String fileType;

    /**
     * 标签列表
     */
    @ApiModelProperty(name = "tagList", value = "标签列表", position = 70)
    @NotEmpty(message = "标签列表不能为空")
    private List<String> tagList;

    /**
     * 封面图
     */
    @ApiModelProperty(name = "coverFileUrl", value = "封面图", position = 80)
    @NotBlank(message = "封面图不能为空")
    private String coverFileUrl;

    /**
     * 藏品可售卖编号列表
     */
    @ApiModelProperty(name = "sellCollectionList", value = "售卖的盲盒的列表", required = true, position = 100)
    @NotNull(message = "售卖的盲盒的列表不能为空")
    @Valid
    private List<CollectionPeriodCreateBlindBoxDetailCReq> sellCollectionList;

    /**
     * 开始发售时间
     */
    @ApiModelProperty(name = "startSellDate", value = "开始发售时间", required = true, position = 110)
    @NotBlank(message = "开始发售时间不能为空")
    private String startSellDate;

    /**
     * 价格
     */
    @ApiModelProperty(name = "price", value = "价格", required = true, position = 120)
    @NotNull(message = "价格不能为空")
    @DecimalMin(value = "0", message = "价格不能小于0")
    private BigDecimal price;

    /**
     * 单人最大购买份数
     */
    @ApiModelProperty(name = "buyMax", value = "单人最大购买个数", position = 50)
    @NotNull(message = "单人最大购买个数不能为空")
    @Min(value = 0, message = "单人最大购买个数不能小于0")
    private Integer buyMax;

    /**
     * 序号
     */
    @ApiModelProperty(name = "orderNo", value = "序号", position = 60)
    private Integer orderNo;

    /**
     * 优先抢购分钟数
     */
    @ApiModelProperty(name = "advanceMins", value = "提前抢购分钟数，默认0", position = 70)
    @Min(value = 0, message = "优先抢购分钟数不能小于0")
    private Integer advanceMins;

    /**
     * 优先购买权需要作品列表
     */
    @ApiModelProperty(name = "lockTime", value = "优先购买权需要作品列表", position = 70)
    private List<CollectionPeriodPriorityBuyCreateReq> collectionPeriodPriorityBuyList;

    /**
     * 作品介绍
     */
    @ApiModelProperty(name = "content", value = "作品介绍(用图片)", position = 90)
    @NotBlank(message = "作品介绍(用图片)不能为空")
    private String content;

//    /**
//     * 权益内容
//     */
//    @ApiModelProperty(name = "rightContent", value = "权益内容", position = 93)
//    private String rightContent;
//
//    /**
//     * 权益类型0=文字 1=图片 2=暂无权益
//     */
//    @ApiModelProperty(name = "rightType", value = "权益类型0=文字 1=图片 2=暂无权益", position = 93)
//    @NotBlank(message = "权益类型不能为空")
//    private String rightType;

    /**
     * 折扣作品列表
     */
    @ApiModelProperty(name = "discountCollectionList", value = "折扣作品列表", position = 100)
    private List<PeriodDiscountDetailCreateReq> discountCollectionList;

    /**
     * 口令标志(0=不是口令 1=是口令)
     */
    @ApiModelProperty(name = "wordFlag", value = "口令标志(0=不是口令 1=是口令)")
    private String wordFlag;

    @ApiModelProperty(name = "channelId", value = "分发渠道", position = 36)
    @NotNull(message = "分发渠道不能为空")
    private Long channelId;
}
