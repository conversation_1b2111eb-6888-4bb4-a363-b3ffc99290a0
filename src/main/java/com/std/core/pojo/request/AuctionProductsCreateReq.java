package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 新增竞拍作品商品
 *
 * <AUTHOR> xieyj
 * @since : 2021-07-11 17:09
 */
@Data
public class AuctionProductsCreateReq {

    /**
     * 藏品id
     */
    @ApiModelProperty(name = "collectionDetailId", value = "藏品id", position = 30)
    private Long collectionDetailId;

    /**
     * 保证金
     */
    @ApiModelProperty(name = "bond", value = "保证金", required = true, position = 50)
    @NotNull(message = "保证金不能为空")
    @DecimalMin(value = "0", message = "保证金不能小于0")
    private BigDecimal bond;

    /**
     * 起拍价
     */
    @ApiModelProperty(name = "startPrice", value = "起拍价", required = true, position = 60)
    @NotNull(message = "起拍价不能为空")
    @DecimalMin(value = "0.01", message = "起拍价不能小于0.01")
    private BigDecimal startPrice;

    /**
     * 加价幅度
     */
    @ApiModelProperty(name = "priceAuction", value = "加价幅度", required = true, position = 70)
    @NotNull(message = "加价幅度不能为空")
    @DecimalMin(value = "0", message = "加价幅度不能小于0")
    private BigDecimal priceAuction;

    /**
     * 开拍时间
     */
    @ApiModelProperty(name = "startTime", value = "开拍时间", required = true, position = 160)
    @NotBlank(message = "开拍时间不能为空")
    private String startTime;

    /**
     * 拍卖时限(分)
     */
    @ApiModelProperty(name = "timeLimit", value = "拍卖时限(分)", required = true, position = 180)
    @NotNull(message = "拍卖时限(分)不能为空")
    private Integer timeLimit;

    /**
     * 延拍时长(分)
     */
    @ApiModelProperty(name = "delayedMinutes", value = "延拍时长(分)", required = true, position = 190)
    @NotNull(message = "延拍时长(分)不能为空")
    private Integer delayedMinutes;

}
