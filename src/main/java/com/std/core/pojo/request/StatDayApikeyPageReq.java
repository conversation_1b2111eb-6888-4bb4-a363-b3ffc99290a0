package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 分页查询秘钥每日盈亏统计
 *
 * <AUTHOR> LEO
 * @since : 2021-02-07 16:54
 */
@Data
public class StatDayApikeyPageReq extends BasePageReq {

    /**
     * 
     */
    @ApiModelProperty(name = "id", value = "", position = 10)
    private Long id;

    /**
     * 用户id
     */
    @ApiModelProperty(name = "userId", value = "用户id", position = 20)
    private Long userId;

    /**
     * 用户关键信息（手机号/身份证/姓名）
     */
    @ApiModelProperty(name = "keywords", value = "用户关键信息（手机号/身份证/姓名）", position = 21)
    private String keywords;

    /**
     * 秘钥id
     */
    @ApiModelProperty(name = "apikeyId", value = "秘钥id", position = 30)
    private Long apikeyId;

    /**
     * 日期
     */
    @ApiModelProperty(name = "date", value = "日期", position = 40)
    private String date;

    /**
     * 每日盈亏
     */
    @ApiModelProperty(name = "income", value = "每日盈亏", position = 50)
    private BigDecimal income;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createTime", value = "创建时间", position = 60)
    private Date createTime;

}
