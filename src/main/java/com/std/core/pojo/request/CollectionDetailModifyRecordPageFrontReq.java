package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 分页查询藏品修改记录
 *
 * <AUTHOR> xieyj
 * @since : 2022-04-25 15:39
 */
@Data
public class CollectionDetailModifyRecordPageFrontReq extends BasePageReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 类型(0=修改名称)
     */
    @ApiModelProperty(name = "type", value = "类型(0=修改名称)", position = 20)
    private String type;

    /**
     * 作品id
     */
    @ApiModelProperty(name = "collectionId", value = "作品id", position = 30)
    private Long collectionId;

    /**
     * 藏品id
     */
    @ApiModelProperty(name = "collectionDetailId", value = "藏品id", position = 40)
    private Long collectionDetailId;

    /**
     * 之前值
     */
    @ApiModelProperty(name = "beforeValue", value = "之前值", position = 50)
    private String beforeValue;

    /**
     * 修改值
     */
    @ApiModelProperty(name = "modifyValue", value = "修改值", position = 60)
    private String modifyValue;

    /**
     * 状态(0=待审核 1=审核通过 2=审核不通过)
     */
    @ApiModelProperty(name = "status", value = "状态(0=待审核 1=审核通过 2=审核不通过)", position = 70)
    private String status;

    /**
     * 状态列表
     */
    @ApiModelProperty(name = "statusList", value = "状态列表", position = 71)
    private List<String> statusList;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 80)
    private Date createDatetime;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updator", value = "更新人", position = 90)
    private Long updator;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 100)
    private Date updateDatetime;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注", position = 110)
    private String remark;

}
