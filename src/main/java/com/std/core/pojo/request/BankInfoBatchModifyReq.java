package com.std.core.pojo.request;

import com.std.core.util.EnumValue;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 修改银行信息
 *
 * <AUTHOR> ycj
 * @since : 2022-03-10 11:01
 */
@Data
public class BankInfoBatchModifyReq {

    /**
     * 编号
     */
    @NotEmpty(message = "idList不能为空")
    @ApiModelProperty(name = "idList", value = "编号", position = 10)
    private List<Long> idList;

    /**
     * 状态（0下架 1上架）
     */
    @ApiModelProperty(name = "status", value = "状态（0下架 1上架）", position = 60)
    @EnumValue(strValues = {"0", "1"}, message = "参数错误")
    private String status;

}
