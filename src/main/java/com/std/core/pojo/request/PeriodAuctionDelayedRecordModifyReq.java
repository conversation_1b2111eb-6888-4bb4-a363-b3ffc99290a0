package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import java.util.Date;
import lombok.Data;

/**
 * 修改期数竞拍延时记录
 *
 * <AUTHOR> ycj
 * @since : 2022-03-29 21:44
 */
@Data
public class PeriodAuctionDelayedRecordModifyReq {

    /**
     * 
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "", position = 10)
    private Long id;

    /**
     * 期数编号
     */
    @ApiModelProperty(name = "periodId", value = "期数编号", position = 20)
    private Long periodId;

    /**
     * 
     */
    @ApiModelProperty(name = "userId", value = "", position = 30)
    private Long userId;

    /**
     * 延迟时的创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "延迟时的创建时间", position = 40)
    private String createDatetime;

    /**
     * 结束时间
     */
    @ApiModelProperty(name = "endDatetime", value = "结束时间", position = 50)
    private String endDatetime;

}
