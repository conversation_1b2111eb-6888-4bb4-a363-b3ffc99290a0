package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 修改作品权益明细
 *
 * <AUTHOR> ycj
 * @since : 2022-07-06 18:03
 */
@Data
public class CollectionRightsModifyReq {

    /**
     * id
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    @NotNull
    private Long id;

    /**
     * 权益类型0=文字 1=图片 2=暂无权益
     */
    @ApiModelProperty(name = "rightType", value = "权益类型2=暂无权益 3=文字列表", position = 93)
    @NotBlank(message = "权益类型不能为空")
    private String rightType;

    /**
     * 权益内容
     */
    @ApiModelProperty(name = "rightContent", value = "权益内容", position = 93)
    private String rightContent;

    /**
     * 权益明细列表
     */
    @ApiModelProperty(name = "rightsDetailList", value = "权益明细列表", position = 10)
    private List<CollectionRightsDetailCreateReq> rightsDetailList;

}
