package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 分页查询降价竞拍
 *
 * <AUTHOR> ycj
 * @since : 2022-05-23 16:43
 */
@Data
public class DegressionAuctionPageFrontReq extends BasePageReq {
//
//    /**
//     * 竞拍名称
//     */
//    @ApiModelProperty(name = "name", value = "竞拍名称", position = 20)
//    private String name;


    /**
     * 状态 {0:待开始,1:售卖中,2:已结束}
     */
    @ApiModelProperty(name = "soldStatus", value = "状态 {0:待开始,1:售卖中,2:已结束}", position = 50)
    private String soldStatus;

}
