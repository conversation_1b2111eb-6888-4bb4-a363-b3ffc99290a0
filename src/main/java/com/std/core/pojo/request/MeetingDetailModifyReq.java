package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import java.util.Date;
import lombok.Data;

/**
 * 修改直播会议详情
 *
 * <AUTHOR> wzh
 * @since : 2023-05-04 13:18
 */
@Data
public class MeetingDetailModifyReq {

//    /**
//     * 序号
//     */
//    @NotNull(message = "id不能为空")
//    @ApiModelProperty(name = "id", value = "序号", position = 10)
//    private Long id;
//
    /**
     * 会议ID
     */
    @ApiModelProperty(name = "meetingId", value = "会议ID", position = 20)
    @NotNull(message = "会议ID不能为空")
    private Long meetingId;
//
//    /**
//     * 开始时间
//     */
//    @ApiModelProperty(name = "startDatetime", value = "开始时间", position = 30)
//    private String startDatetime;
//
//    /**
//     * 结束时间
//     */
//    @ApiModelProperty(name = "endDatetime", value = "结束时间", position = 40)
//    private String endDatetime;

}
