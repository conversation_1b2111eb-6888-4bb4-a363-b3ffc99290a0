package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 列表查询审核记录
 *
 * <AUTHOR> ycj
 * @since : 2022-06-14 20:06
 */
@Data
public class ApproveRecordListFrontReq extends BaseListReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 类型 {0:发行方申请,1:作品申请}
     */
    @ApiModelProperty(name = "refType", value = "类型 {0:发行方申请,1:作品申请}", position = 20)
    private String refType;

    /**
     * 关联序号
     */
    @ApiModelProperty(name = "refId", value = "关联序号", position = 30)
    private Long refId;

    /**
     * 审核人
     */
    @ApiModelProperty(name = "creater", value = "审核人", position = 40)
    private Long creater;

    /**
     * 审核人
     */
    @ApiModelProperty(name = "createrName", value = "审核人", position = 50)
    private String createrName;

    /**
     * 审核时间
     */
    @ApiModelProperty(name = "createDatetime", value = "审核时间", position = 60)
    private Date createDatetime;

    /**
     * 审核意见
     */
    @ApiModelProperty(name = "opinion", value = "审核意见", position = 70)
    private String opinion;

}
