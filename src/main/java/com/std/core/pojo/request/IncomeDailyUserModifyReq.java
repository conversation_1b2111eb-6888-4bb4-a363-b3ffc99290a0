package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import java.util.Date;
import lombok.Data;

/**
 * 修改用户每日分佣
 *
 * <AUTHOR> LEO
 * @since : 2021-02-07 15:47
 */
@Data
public class IncomeDailyUserModifyReq {

    /**
     * 主键编号
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "主键编号", position = 10)
    private Long id;

    /**
     * 用户ID
     */
    @ApiModelProperty(name = "userId", value = "用户ID", position = 20)
    private Long userId;

    /**
     * 当日等级
     */
    @ApiModelProperty(name = "nodeType", value = "当日等级", position = 30)
    private String nodeType;

    /**
     * 日期
     */
    @ApiModelProperty(name = "incomeDate", value = "日期", position = 40)
    private String incomeDate;

    /**
     * 当日盈利
     */
    @ApiModelProperty(name = "amount", value = "当日盈利", position = 50)
    private BigDecimal amount;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 60)
    private String createDatetime;

}
