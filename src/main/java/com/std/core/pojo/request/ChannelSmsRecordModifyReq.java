package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import java.util.Date;
import lombok.Data;

/**
 * 修改三方短信重发记录
 *
 * <AUTHOR> ycj
 * @since : 2022-05-05 10:21
 */
@Data
public class ChannelSmsRecordModifyReq {

    /**
     * 序号
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 用户序号
     */
    @ApiModelProperty(name = "userId", value = "用户序号", position = 20)
    private Long userId;

    /**
     * 渠道类型 {0:易宝}
     */
    @ApiModelProperty(name = "channelType", value = "渠道类型 {0:易宝}", position = 30)
    private String channelType;

    /**
     * 关联序号
     */
    @ApiModelProperty(name = "refId", value = "关联序号", position = 40)
    private Integer refId;

    /**
     * 发送类型 {0:个人商户入驻}
     */
    @ApiModelProperty(name = "refType", value = "发送类型 {0:个人商户入驻}", position = 50)
    private String refType;

    /**
     * 入参
     */
    @ApiModelProperty(name = "request", value = "入参", position = 60)
    private String request;

    /**
     * 回参
     */
    @ApiModelProperty(name = "respond", value = "回参", position = 70)
    private String respond;

    /**
     * 入网请求号
     */
    @ApiModelProperty(name = "requestNo", value = "入网请求号", position = 80)
    private String requestNo;

    /**
     * 访问类型
     */
    @ApiModelProperty(name = "requestType", value = "访问类型", position = 90)
    private String requestType;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "creater", value = "创建人", position = 100)
    private Long creater;

    /**
     * 创建人名
     */
    @ApiModelProperty(name = "createrName", value = "创建人名", position = 110)
    private String createrName;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 120)
    private String createDatetime;

}
