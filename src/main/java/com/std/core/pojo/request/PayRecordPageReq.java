package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 分页查询支付记录
 *
 * <AUTHOR> xieyj
 * @since : 2021-07-25 14:23
 */
@Data
public class PayRecordPageReq extends BasePageReq {

    /**
     * 自增主键
     */
    @ApiModelProperty(name = "id", value = "自增主键", position = 10)
    private Long id;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号", position = 20)
    private Long userId;

    /**
     * 用户关键信息（手机号/身份证/姓名）
     */
    @ApiModelProperty(name = "keywords", value = "用户关键信息（手机号/身份证/姓名）", position = 21)
    private String keywords;

    /**
     * 支付渠道 wechat、alipy等
     */
    @ApiModelProperty(name = "payType", value = "支付渠道 wechat、alipy等", position = 30)
    private String payType;

    /**
     * 支付方式 app、h5、web等
     */
    @ApiModelProperty(name = "payMethod", value = "支付方式 app、h5、web等", position = 40)
    private String payMethod;

    /**
     * 支付金额
     */
    @ApiModelProperty(name = "amount", value = "支付金额", position = 50)
    private BigDecimal amount;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createTime", value = "创建时间", position = 60)
    private Date createTime;

    /**
     * 回调时间
     */
    @ApiModelProperty(name = "callbackTime", value = "回调时间", position = 70)
    private Date callbackTime;

    /**
     * 状态 0:待回调,1:支付成功,2:支付失败
     */
    @ApiModelProperty(name = "status", value = "状态 0:待回调,1:支付成功,2:支付失败", position = 80)
    private String status;

    /**
     * 状态列表
     */
    @ApiModelProperty(name = "statusList", value = "状态列表", position = 81)
    private List<String> statusList;

    /**
     * 业务类型0:充值,1:一口价订单支付
     */
    @ApiModelProperty(name = "bizType", value = "业务类型0:充值,1:一口价订单支付", position = 90)
    private String bizType;

    /**
     * 关联业务编号
     */
    @ApiModelProperty(name = "bizCode", value = "关联业务编号", position = 100)
    private Long bizCode;

    /**
     * 状态0:待处理,1:已处理
     */
    @ApiModelProperty(name = "bizStatus", value = "状态0:待处理,1:已处理", position = 110)
    private String bizStatus;

}
