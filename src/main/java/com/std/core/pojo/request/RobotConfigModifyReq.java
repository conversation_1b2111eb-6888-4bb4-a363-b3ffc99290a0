package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 修改机器人等级配置
 *
 * <AUTHOR> ycj
 * @since : 2022-08-29 10:08
 */
@Data
public class RobotConfigModifyReq {

    /**
     * 序号
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

//    /**
//     * 藏品序号
//     */
//    @ApiModelProperty(name = "collectionId", value = "藏品序号", required = true, position = 20)
//    @NotNull(message = "藏品序号不能为空")
//    private Long collectionId;

    /**
     * 等级
     */
    @ApiModelProperty(name = "level", value = "等级", required = true, position = 30)
    @NotBlank(message = "等级不能为空")
    private String level;

    /**
     * 每小时概率
     */
    @ApiModelProperty(name = "probability", value = "每小时概率", required = true, position = 50)
    @NotNull(message = "每小时概率不能为空")
    @DecimalMin(value = "0", message = "概率不能小于0")
    private BigDecimal probability;

    /**
     * 派遣时间
     */
    @ApiModelProperty(name = "sendTime", value = "派遣时间", required = true, position = 60)
    @NotNull(message = "派遣时间不能为空")
    @Min(value = 0, message = "派遣时间不能小于0")
    private Integer sendTime;

}
