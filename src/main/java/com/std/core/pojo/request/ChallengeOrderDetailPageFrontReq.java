package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 分页查询挑战兑换订单明细
 *
 * <AUTHOR> ycj
 * @since : 2021-12-29 15:45
 */
@Data
public class ChallengeOrderDetailPageFrontReq extends BasePageReq {

    /**
     * 
     */
    @ApiModelProperty(name = "id", value = "", position = 10)
    private Long id;

    /**
     * 订单id
     */
    @ApiModelProperty(name = "orderId", value = "订单id", position = 20)
    private Long orderId;

    /**
     * 挑战id
     */
    @ApiModelProperty(name = "challengeId", value = "挑战id", position = 30)
    private Long challengeId;

    /**
     * 用户id
     */
    @ApiModelProperty(name = "userId", value = "用户id", position = 40)
    private Long userId;

    /**
     * 用户关键信息（手机号/身份证/姓名）
     */
    @ApiModelProperty(name = "keywords", value = "用户关键信息（手机号/身份证/姓名）", position = 41)
    private String keywords;

    /**
     * 藏品id
     */
    @ApiModelProperty(name = "collectionDetailId", value = "藏品id", position = 50)
    private Long collectionDetailId;

}
