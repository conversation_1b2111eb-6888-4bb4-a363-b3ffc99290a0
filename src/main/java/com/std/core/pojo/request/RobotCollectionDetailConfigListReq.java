package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 列表查询机器人挖宝藏品配置
 *
 * <AUTHOR> wzh
 * @since : 2023-05-25 10:55
 */
@Data
public class RobotCollectionDetailConfigListReq extends BaseListReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 藏品序号
     */
    @ApiModelProperty(name = "collectionId", value = "藏品序号", position = 20)
    private Long collectionId;

    /**
     * 藏品名称
     */
    @ApiModelProperty(name = "collectionName", value = "藏品名称", position = 30)
    private String collectionName;

    /**
     * 机器人序号
     */
    @ApiModelProperty(name = "robotId", value = "机器人序号", position = 40)
    private Long robotId;

    /**
     * 机器人名称
     */
    @ApiModelProperty(name = "robotName", value = "机器人名称", position = 50)
    private String robotName;

    /**
     * 状态 {0:待上架,1:已上架,1:已下架}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:待上架,1:已上架,1:已下架}", position = 60)
    private String status;

    /**
     * 增加的概率
     */
    @ApiModelProperty(name = "probability", value = "增加的概率", position = 70)
    private BigDecimal probability;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "creator", value = "创建人", position = 80)
    private Long creator;

    /**
     * 创建人名
     */
    @ApiModelProperty(name = "createName", value = "创建人名", position = 90)
    private String createName;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 100)
    private Date createDatetime;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 110)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称", position = 120)
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 130)
    private Date updateDatetime;

    /**
     * 备注说明
     */
    @ApiModelProperty(name = "remark", value = "备注说明", position = 140)
    private String remark;

}
