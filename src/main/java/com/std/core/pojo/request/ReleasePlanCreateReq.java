package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* 新增释放计划
*
* <AUTHOR> ycj
* @since : 2020-10-19 15:58
*/
@Data
public class ReleasePlanCreateReq {

      /**
      * 解锁包序号
      */
      @ApiModelProperty(name = "unlockId", value = "解锁包序号", required = true, position = 20)
      @NotNull(message = "解锁包序号不能为空")
      private Long unlockId;

      /**
      * 释放数量
      */
      @ApiModelProperty(name = "releaseAmount", value = "释放数量", required = true, position = 30)
      @NotNull(message = "释放数量不能为空")
      private String releaseAmount;

      /**
      * 实际释放数量
      */
      @ApiModelProperty(name = "actualReleaseAmount", value = "实际释放数量", required = true, position = 40)
      @NotNull(message = "实际释放数量不能为空")
      private BigDecimal actualReleaseAmount;

      /**
      * 释放方式 
      */
      @ApiModelProperty(name = "releaseMode", value = "释放方式 ", required = true, position = 50)
      @NotBlank(message = "释放方式 不能为空")
      private String releaseMode;

      /**
      * 状态 
      */
      @ApiModelProperty(name = "status", value = "状态 ", required = true, position = 60)
      @NotBlank(message = "状态 不能为空")
      private String status;

      /**
      * 计划释放时间
      */
      @ApiModelProperty(name = "planReleaseDatetime", value = "计划释放时间", required = true, position = 70)
      @NotBlank(message = "计划释放时间不能为空")
      private Date planReleaseDatetime;

      /**
      * 实际释放时间
      */
      @ApiModelProperty(name = "actualReleaseDatetime", value = "实际释放时间", required = true, position = 80)
      @NotBlank(message = "实际释放时间不能为空")
      private String actualReleaseDatetime;


}
