package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 新增支付三方分账明细单
 *
 * <AUTHOR> ycj
 * @since : 2022-03-27 20:14
 */
@Data
public class ThirdDivideDetailCreateReq {

    /**
     * 分账类型(0=渠道商 1=平台)
     */
    @ApiModelProperty(name = "type", value = "分账类型(0=渠道商 1=平台)", position = 20)
    private String type;

    /**
     * 分账订单类型
     */
    @ApiModelProperty(name = "orderType", value = "分账订单类型", position = 30)
    private String orderType;

    /**
     * 订单号
     */
    @ApiModelProperty(name = "orderId", value = "订单号", position = 40)
    private Long orderId;

    /**
     * 支付订单号
     */
    @ApiModelProperty(name = "payOrderNo", value = "支付订单号", required = true, position = 50)
    @NotBlank(message = "支付订单号不能为空")
    private String payOrderNo;

    /**
     * 支付三方订单号
     */
    @ApiModelProperty(name = "payThirdNo", value = "支付三方订单号", required = true, position = 60)
    @NotBlank(message = "支付三方订单号不能为空")
    private String payThirdNo;

    /**
     * 分账金额
     */
    @ApiModelProperty(name = "amount", value = "分账金额", position = 70)
    private BigDecimal amount;

    /**
     * 分账时间
     */
    @ApiModelProperty(name = "createTime", value = "分账时间", required = true, position = 80)
    private String createTime;

    /**
     * 状态(0=待处理，1=处理中 2=成功 3=失败)
     */
    @ApiModelProperty(name = "status", value = "状态(0=待处理，1=处理中 2=成功 3=失败)", position = 90)
    private String status;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", required = true, position = 100)
    private String updateDatetime;

}
