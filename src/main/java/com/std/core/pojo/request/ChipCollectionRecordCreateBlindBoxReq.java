package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 新增芯片领藏品活动记录
 *
 * <AUTHOR> ycj
 * @since : 2022-08-24 14:23
 */
@Data
public class ChipCollectionRecordCreateBlindBoxReq {

    /**
     * 活动序号
     */
    @ApiModelProperty(name = "activityId", value = "活动序号", position = 20)
    @NotNull(message = "活动序号不能为空")
    private Long activityId;

    /**
     * 芯片编号
     */
    @ApiModelProperty(name = "chipCode", value = "芯片编号", position = 30)
    @NotBlank(message = "芯片编号不能为空")
    private String chipCode;

}
