package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 新增直播会议参与记录
 *
 * <AUTHOR> wzh
 * @since : 2023-05-04 10:14
 */
@Data
public class MeetingRecordCreateReq {

    /**
     * 会议ID
     */
    @ApiModelProperty(name = "meetingId", value = "会议ID", required = true, position = 20)
    @NotNull(message = "会议ID不能为空")
    private Long meetingId;

    /**
     * 密码
     */
    @ApiModelProperty(name = "pwd", value = "密码", position = 50)
    private String pwd;
}
