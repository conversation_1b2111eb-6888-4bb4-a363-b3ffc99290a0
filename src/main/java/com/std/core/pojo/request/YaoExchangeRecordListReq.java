package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 列表查询爻转化订单
 *
 * <AUTHOR> ycj
 * @since : 2022-11-10 17:57
 */
@Data
public class YaoExchangeRecordListReq extends BaseListReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 用户序号
     */
    @ApiModelProperty(name = "userId", value = "用户序号", position = 20)
    private Long userId;

    /**
     * 阴爻数
     */
    @ApiModelProperty(name = "yinYao", value = "阴爻数", position = 30)
    private BigDecimal yinYao;

    /**
     * 阳爻数
     */
    @ApiModelProperty(name = "yangYao", value = "阳爻数", position = 40)
    private BigDecimal yangYao;

    /**
     * 单次兑换藏品数
     */
    @ApiModelProperty(name = "quantity", value = "单次兑换藏品数", position = 50)
    private Integer quantity;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 60)
    private Date createDatetime;

}
