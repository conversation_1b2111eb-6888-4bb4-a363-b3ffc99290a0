package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 列表查询赋能商城活动
 *
 * <AUTHOR> ycj
 * @since : 2022-05-05 14:45
 */
@Data
public class GoodsActivityListFrontReq extends BaseListReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 状态 {0:进行中,1:已解锁,2:已结束}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:进行中,1:已解锁,2:已结束}", position = 20)
    private String status;

    /**
     * 兑换功能是否开启,0:关闭,1:开启
     */
    @ApiModelProperty(name = "exchangeFunction", value = "兑换功能是否开启,0:关闭,1:开启", position = 30)
    private String exchangeFunction;

    /**
     * 解锁时间
     */
    @ApiModelProperty(name = "unlockDatetime", value = "解锁时间", position = 40)
    private Date unlockDatetime;

    /**
     * 兑换终止时间
     */
    @ApiModelProperty(name = "closeDatetime", value = "兑换终止时间", position = 50)
    private Date closeDatetime;

    /**
     * 实际兑换终止时间
     */
    @ApiModelProperty(name = "actualCloseDatetime", value = "实际兑换终止时间", position = 60)
    private Date actualCloseDatetime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(name = "endDatetime", value = "活动结束时间", position = 70)
    private Date endDatetime;

    /**
     * 实际活动结束时间
     */
    @ApiModelProperty(name = "actualEndDatetime", value = "实际活动结束时间", position = 80)
    private Date actualEndDatetime;

}
