package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 新增元粟转赠明细
 *
 * <AUTHOR> ycj
 * @since : 2022-11-11 14:41
 */
@Data
public class MetaMilletTransferDetailCreateReq {

    /**
     * 币种
     */
    @ApiModelProperty(name = "currency", value = "币种", required = true, position = 30)
    @NotBlank(message = "币种不能为空")
    private String currency;

    /**
     * 转赠数量
     */
    @ApiModelProperty(name = "quantity", value = "转赠数量", required = true, position = 50)
    @NotNull(message = "转赠数量不能为空")
    @DecimalMin(value = "0",message = "转赠数量不能小于0")
    private BigDecimal quantity;

}
