package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * 分页查询C端用户
 *
 * <AUTHOR> Leo
 * @since : 2020-05-18 20:09
 */
@Data
public class CuserPageReq extends BasePageReq {

    /**
     * 用户ID
     */
    @ApiModelProperty(name = "userId", value = "用户ID", position = 1)
    private Long userId;

    /**
     * 状态
     */
    @ApiModelProperty(name = "status", value = "状态 dict=user.status", position = 2)
    private String status;

    /**
     * 状态列表
     */
    @ApiModelProperty(name = "statusList", value = "状态 dict=user.status", position = 2)
    private List<String> statusList;

    /**
     * 推荐人
     */
    @ApiModelProperty(name = "userReferee", value = "推荐人", position = 3)
    private Long userReferee;
    /**
     * 用户名称
     */
    @ApiModelProperty(name = "userName", value = "用户名称", position = 25)
    private String userName;

    /**
     * 用户等级
     */
    @ApiModelProperty(name = "level", value = "用户等级", position = 30)
    private String level;

    /**
     * 节点等级
     */
    @ApiModelProperty(name = "nodeLevel", value = "节点等级", position = 40)
    private String nodeLevel;

    /**
     * 登录名称
     */
    @ApiModelProperty(name = "loginName", value = "登录名称")
    private String loginName;

    /**
     * 手机号
     */
    @ApiModelProperty(name = "mobile", value = "手机号")
    private String mobile;
    /**
     * 姓名
     */
    @ApiModelProperty(name = "realName", value = "姓名")
    private String realName;

    @ApiModelProperty(name = "keywords", value = "模糊查询关键字")
    private String keywords;

    @ApiModelProperty(name = "keywords2", value = "推荐人模糊查询关键字")
    private String keywords2;

    @ApiModelProperty(name = "channelFlag", value = "公司标识")
    private String channelFlag;

    @ApiModelProperty(name = "memberFlag", value = "激活标识")
    private String memberFlag;

    /**
     * 否是游离会员
     */
    @ApiModelProperty(name = "isFreeUser", value = "否是游离会员 是传1，不是空")
    private String isFreeUser;

    @ApiModelProperty(name = "isChannel", value = "否是渠道商 是传1，不是空")
    private String isChannel;

    @ApiModelProperty(name = "nodeLevelType", value = "等级类型 0:会员等级,1:节点等级")
    private String nodeLevelType;

    @ApiModelProperty(name = "channelId", value = "渠道群主")
    private Long channelId;

    /**
     * 渠道类型 0=个人渠道商 1=机构渠道商
     */
    @ApiModelProperty(name = "channelType", value = "渠道类型 0=个人渠道商 1=机构渠道商")
    private String channelType;

    @ApiModelProperty(name = "isRealName", value = "是否实名 0：否，1：是")
    private String isRealName;
    /**
     * 分组
     */
    @ApiModelProperty(name = "grouping", value = "分组(数据字典:user.grouping)")
    private String grouping;

    /**
     * 实名跳转方式(0=人脸识别 1=人工提交认证)
     */
    @ApiModelProperty(name = "identifyStyle", value = "实名跳转方式(0=人脸识别 1=人工提交认证)", position = 120)
    private String identifyStyle;

    @ApiModelProperty(name = "recoveryStatus", value = "清退状态")
    private String recoveryStatus;

    @ApiModelProperty(name = "loginStatus", value = "登陆状态 0:重新输入,1:输入验证码,2:锁定中")
    private String loginStatus;

    @ApiModelProperty(name = "channelMerchantId", value = "来源渠道1=麦塔 2=境善巽 3=渔光之城")
    private Long channelMerchantId;
}
