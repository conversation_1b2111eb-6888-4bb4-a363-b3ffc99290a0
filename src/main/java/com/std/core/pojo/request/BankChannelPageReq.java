package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分页查询渠道银行信息
 *
 * <AUTHOR> ycj
 * @since : 2022-03-10 20:30
 */
@Data
public class BankChannelPageReq extends BasePageReq {

    /**
     * 编号
     */
    @ApiModelProperty(name = "id", value = "编号", position = 10)
    private Long id;

    /**
     * 银行编号
     */
    @ApiModelProperty(name = "bankInfoId", value = "银行编号", position = 20)
    private Long bankInfoId;

    /**
     * 银行卡类型
     */
    @ApiModelProperty(name = "type", value = "银行卡类型", position = 30)
    private String type;

    /**
     * 渠道类型 {0:宝付}
     */
    @ApiModelProperty(name = "channelType", value = "渠道类型 {0:宝付}", position = 30)
    private String channelType;

    /**
     * 状态 {0:下架,1:上架}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:下架,1:上架}", position = 40)
    private String status;

    /**
     * 银行关键字
     */
    @ApiModelProperty(name = "keywords", value = "银行关键字", position = 20)
    private String keywords;
}
