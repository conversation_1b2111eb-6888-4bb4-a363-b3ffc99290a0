package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 修改作品期数
 *
 * <AUTHOR> ycj
 * @since : 2021-11-03 20:27
 */
@Data
public class CollectionPeriodBatchUpAndDownReq {

    /**
     * 
     */
    @NotEmpty(message = "idList不能为空")
    @ApiModelProperty(name = "idList", value = "", position = 10)
    private List<Long> idList;

    @ApiModelProperty(name = "status",value = "状态 {1:已上架,2:已下架}")
    private String status;

}
