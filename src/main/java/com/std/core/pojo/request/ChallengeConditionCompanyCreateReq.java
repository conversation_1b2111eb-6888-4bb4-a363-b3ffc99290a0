package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 新增挑战条件明细
 *
 * <AUTHOR> xieyj
 * @since : 2022-05-26 11:12
 */
@Data
public class ChallengeConditionCompanyCreateReq {

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称", required = true, position = 20)
    @NotBlank(message = "名称不能为空")
    @Length(min = 1,max = 255,message = "条件名称过长")
    private String name;

    /**
     * 满足条件的数量
     */
    @ApiModelProperty(name = "quantity", value = "满足条件的数量", required = true, position = 30)
    @NotNull(message = "满足条件的数量不能为空")
    private Integer quantity;

    /**
     * 回收类型 0:回收,1:赋能
     */
    @ApiModelProperty(name = "backType", value = "回收类型 0:回收,1:赋能", position = 40)
    private String backType;

    /**
     * 序号
     */
    @ApiModelProperty(name = "orderNo", value = "序号", required = true, position = 50)
    @NotNull(message = "序号不能为空")
    private Integer orderNo;

    @Valid
    @ApiModelProperty(name = "challengeConditionDetailList", value = "挑战兑换藏品明细列表", position = 60)
    @NotEmpty(message = "挑战兑换藏品明细列表不能为空")
    private List<ChallengeConditionDetailCreateReq> challengeConditionDetailList;

}
