package com.std.core.service;

import com.std.core.pojo.domain.CollectionSeries;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.CollectionSeriesDetailRes;
import com.std.core.pojo.response.CollectionSeriesListRes;
import com.std.core.pojo.response.CollectionSeriesPageRes;
import java.util.List;

/**
 * 作品系列Service
 *
 * <AUTHOR> wzh
 * @since : 2023-08-08 17:09
 */
public interface ICollectionSeriesService {

    /**
     * 新增作品系列
     *
     * @param req 新增作品系列入参
     * @param operator 操作人
     */
    void create(CollectionSeriesCreateReq req, User operator);

    /**
     * 删除作品系列
     *
     * @param id 主键ID
     */
     void remove(Long id);

    /**
     * 修改作品系列
     *
     * @param req 修改作品系列入参
     * @param operator 操作人
     */
    void modify(CollectionSeriesModifyReq req, User operator);
    void mobatchUpDowndify(CollectionSeriesBatchModifyReq request, User operator);
    /**
     * 详情查询作品系列
     *
     * @param id 主键ID
     * @return 作品系列详情数据
     */
     CollectionSeries detail(Long id);
    CollectionSeries detailSimple(Long id);
    CollectionSeries detailForUpdate(Long id);

    /**
     * 分页查询作品系列
     *
     * @param req 分页查询作品系列入参
     * @return 作品系列分页数据
     */
     List<CollectionSeries> page(CollectionSeriesPageReq req);

    /**
     * 列表查询作品系列
     *
     * @param req 列表查询作品系列入参
     * @return 作品系列列表数据
     */
     List<CollectionSeries> list(CollectionSeriesListReq req);

    /**
     * 前端详情查询作品系列
     *
     * @param id 主键ID
     * @return 作品系列详情数据
     */
    CollectionSeriesDetailRes detailFront(Long id);

    /**
     * 前端分页查询作品系列
     *
     * @param req 分页查询作品系列入参
     * @return 作品系列分页数据
     */
     List<CollectionSeriesPageRes> pageFront(CollectionSeriesPageFrontReq req);

    /**
     * 前端列表查询作品系列
     *
     * @param req 列表查询作品系列入参
     * @return 作品系列列表数据
     */
     List<CollectionSeriesListRes> listFront(CollectionSeriesListFrontReq req);



}