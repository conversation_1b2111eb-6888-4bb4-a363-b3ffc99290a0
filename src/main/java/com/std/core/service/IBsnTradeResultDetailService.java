package com.std.core.service;

import com.std.core.pojo.domain.BsnTradeResultDetail;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.BsnTradeResultDetailCreateReq;
import com.std.core.pojo.request.BsnTradeResultDetailListFrontReq;
import com.std.core.pojo.request.BsnTradeResultDetailListReq;
import com.std.core.pojo.request.BsnTradeResultDetailModifyReq;
import com.std.core.pojo.request.BsnTradeResultDetailPageFrontReq;
import com.std.core.pojo.request.BsnTradeResultDetailPageReq;
import com.std.core.pojo.response.BsnTradeResultDetailDetailRes;
import com.std.core.pojo.response.BsnTradeResultDetailListRes;
import com.std.core.pojo.response.BsnTradeResultDetailPageRes;
import java.util.List;

/**
 * bsn交易结果明细Service
 *
 * <AUTHOR> xieyj
 * @since : 2023-05-06 17:25
 */
public interface IBsnTradeResultDetailService {

    /**
     * 新增bsn交易结果明细
     *
     * @param req 新增bsn交易结果明细入参
     * @param operator 操作人
     */
    void create(BsnTradeResultDetailCreateReq req, User operator);

    /**
     * 删除bsn交易结果明细
     *
     * @param id 主键ID
     */
    void remove(Long id);

    /**
     * 修改bsn交易结果明细
     *
     * @param req 修改bsn交易结果明细入参
     * @param operator 操作人
     */
    void modify(BsnTradeResultDetailModifyReq req, User operator);
    void modify(BsnTradeResultDetail req);

    /**
     * 详情查询bsn交易结果明细
     *
     * @param id 主键ID
     * @return bsn交易结果明细详情数据
     */
    BsnTradeResultDetail detail(Long id);

    /**
     * 分页查询bsn交易结果明细
     *
     * @param req 分页查询bsn交易结果明细入参
     * @return bsn交易结果明细分页数据
     */
    List<BsnTradeResultDetail> page(BsnTradeResultDetailPageReq req);

    /**
     * 列表查询bsn交易结果明细
     *
     * @param req 列表查询bsn交易结果明细入参
     * @return bsn交易结果明细列表数据
     */
    List<BsnTradeResultDetail> list(BsnTradeResultDetailListReq req);
    List<BsnTradeResultDetail> list(BsnTradeResultDetail req);

    /**
     * 前端详情查询bsn交易结果明细
     *
     * @param id 主键ID
     * @return bsn交易结果明细详情数据
     */
    BsnTradeResultDetailDetailRes detailFront(Long id);

    /**
     * 前端分页查询bsn交易结果明细
     *
     * @param req 分页查询bsn交易结果明细入参
     * @return bsn交易结果明细分页数据
     */
    List<BsnTradeResultDetailPageRes> pageFront(BsnTradeResultDetailPageFrontReq req);

    /**
     * 前端列表查询bsn交易结果明细
     *
     * @param req 列表查询bsn交易结果明细入参
     * @return bsn交易结果明细列表数据
     */
    List<BsnTradeResultDetailListRes> listFront(BsnTradeResultDetailListFrontReq req);

    List<BsnTradeResultDetail> selectList(Long collectionId, String status);
}