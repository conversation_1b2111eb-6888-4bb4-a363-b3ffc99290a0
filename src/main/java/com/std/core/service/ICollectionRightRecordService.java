package com.std.core.service;

import com.std.core.pojo.domain.CollectionRightRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.CollectionRightRecordCreateReq;
import com.std.core.pojo.request.CollectionRightRecordListReq;
import com.std.core.pojo.request.CollectionRightRecordListFrontReq;
import com.std.core.pojo.request.CollectionRightRecordModifyReq;
import com.std.core.pojo.request.CollectionRightRecordPageReq;
import com.std.core.pojo.request.CollectionRightRecordPageFrontReq;
import com.std.core.pojo.response.CollectionRightRecordDetailRes;
import com.std.core.pojo.response.CollectionRightRecordListRes;
import com.std.core.pojo.response.CollectionRightRecordPageRes;

import java.util.List;

/**
 * 权益使用明细Service
 *
 * <AUTHOR> ycj
 * @since : 2022-07-25 19:48
 */
public interface ICollectionRightRecordService {

    /**
     * 新增权益使用明细
     *
     * @param req      新增权益使用明细入参
     * @param operator 操作人
     */
    void create(CollectionRightRecordCreateReq req, User operator);

    void create(CollectionRightRecord rightRecord);

    /**
     * 删除权益使用明细
     *
     * @param id 主键ID
     */
    void remove(Long id);

    /**
     * 修改权益使用明细
     *
     * @param req      修改权益使用明细入参
     * @param operator 操作人
     */
    void modify(CollectionRightRecordModifyReq req, User operator);

    /**
     * 详情查询权益使用明细
     *
     * @param id 主键ID
     * @return 权益使用明细详情数据
     */
    CollectionRightRecord detail(Long id);

    /**
     * 分页查询权益使用明细
     *
     * @param req 分页查询权益使用明细入参
     * @return 权益使用明细分页数据
     */
    List<CollectionRightRecord> page(CollectionRightRecordPageReq req);

    /**
     * 列表查询权益使用明细
     *
     * @param req 列表查询权益使用明细入参
     * @return 权益使用明细列表数据
     */
    List<CollectionRightRecord> list(CollectionRightRecordListReq req);

    List<CollectionRightRecord> list(CollectionRightRecord req);

    /**
     * 前端详情查询权益使用明细
     *
     * @param id 主键ID
     * @return 权益使用明细详情数据
     */
    CollectionRightRecordDetailRes detailFront(Long id);

    /**
     * 前端分页查询权益使用明细
     *
     * @param req 分页查询权益使用明细入参
     * @return 权益使用明细分页数据
     */
    List<CollectionRightRecordPageRes> pageFront(CollectionRightRecordPageFrontReq req);

    /**
     * 前端列表查询权益使用明细
     *
     * @param req 列表查询权益使用明细入参
     * @return 权益使用明细列表数据
     */
    List<CollectionRightRecordListRes> listFront(CollectionRightRecordListFrontReq req);


    void removeByrRefId(Long id, String type);

    void removeDropByrRefId(Long id, String type, String dropType);
}