package com.std.core.service;

import com.std.core.pojo.domain.MeetingPresenters;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.MeetingPresentersCreateReq;
import com.std.core.pojo.request.MeetingPresentersListFrontReq;
import com.std.core.pojo.request.MeetingPresentersListReq;
import com.std.core.pojo.request.MeetingPresentersModifyReq;
import com.std.core.pojo.request.MeetingPresentersPageFrontReq;
import com.std.core.pojo.request.MeetingPresentersPageReq;
import com.std.core.pojo.response.MeetingPresentersDetailRes;
import com.std.core.pojo.response.MeetingPresentersListOssRes;
import com.std.core.pojo.response.MeetingPresentersListRes;
import com.std.core.pojo.response.MeetingPresentersPageRes;
import java.util.List;

/**
 * 会议直播主播Service
 *
 * <AUTHOR> wzh
 * @since : 2023-06-01 18:43
 */
public interface IMeetingPresentersService {

    /**
     * 新增会议直播主播
     *
     * @param req 新增会议直播主播入参
     * @param operator 操作人
     */
    void create(MeetingPresentersCreateReq req, User operator);

    void batchCreate(List<MeetingPresenters> presentersList, Long meetingId);

    /**
     * 删除会议直播主播
     *
     * @param id 主键ID
     */
    void remove(Long id);

    /**
     * 修改会议直播主播
     *
     * @param req 修改会议直播主播入参
     * @param operator 操作人
     */
    void modify(MeetingPresentersModifyReq req, User operator);

    /**
     * 详情查询会议直播主播
     *
     * @param id 主键ID
     * @return 会议直播主播详情数据
     */
    MeetingPresenters detail(Long id);

    /**
     * 分页查询会议直播主播
     *
     * @param req 分页查询会议直播主播入参
     * @return 会议直播主播分页数据
     */
    List<MeetingPresenters> page(MeetingPresentersPageReq req);

    /**
     * 列表查询会议直播主播
     *
     * @param req 列表查询会议直播主播入参
     * @return 会议直播主播列表数据
     */
    List<MeetingPresenters> list(MeetingPresentersListReq req);

    List<MeetingPresenters> list(MeetingPresenters req);

    Integer selectCount(Long meetingId, Long userId);

    /**
     * 前端详情查询会议直播主播
     *
     * @param id 主键ID
     * @return 会议直播主播详情数据
     */
    MeetingPresentersDetailRes detailFront(Long id);

    /**
     * 前端分页查询会议直播主播
     *
     * @param req 分页查询会议直播主播入参
     * @return 会议直播主播分页数据
     */
    List<MeetingPresentersPageRes> pageFront(MeetingPresentersPageFrontReq req);

    /**
     * 前端列表查询会议直播主播
     *
     * @return 会议直播主播列表数据
     */
    List<MeetingPresentersListRes> listFront(Long meetingId, User operator);
    List<MeetingPresentersListRes> listFront(Long meetingId);
    List<MeetingPresentersListOssRes> listFront(List<MeetingPresentersListRes> listRes, Long userId);

    List<MeetingPresenters> detailByMeeting(Long meetingId);

}