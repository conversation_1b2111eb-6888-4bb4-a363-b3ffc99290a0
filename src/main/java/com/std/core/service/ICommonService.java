package com.std.core.service;

import com.aliyun.oss.model.SimplifiedObjectMeta;
import com.std.core.pojo.response.AliOSSRes;
import com.std.core.pojo.response.OssUploadTokenRes;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;

public interface ICommonService {

    String getQiniutoken();


    AliOSSRes getAliOssToken();


    SimplifiedObjectMeta getAliFileMeta(String name);

    BigDecimal getFileSize(String name);

    String httpUploadFileToAliyunOss(String fileUrl, String fileName) throws Exception;

    String getFileFullUrl(String fileUrl);

    String putObject(String filePath);

    String putObject(String desImage, String v4UUID);

    String putObjectByName(String filePath, String objectName);

    String putObjectTest();

    String deleteObject(String objectName);

    /**
     * 批量上传文件到OSS
     *
     * @param files 文件列表
     * @param prefix 文件前缀路径
     * @return 上传结果URL列表
     */
    List<String> batchUploadFiles(List<MultipartFile> files, String prefix);

    /**
     * 批量从URL上传文件到OSS
     *
     * @param imageUrls 图片URL列表
     * @param prefix 文件前缀路径
     * @return 上传结果URL列表
     */
    List<String> batchUploadFromUrls(List<String> imageUrls, String prefix);

    /**
     * 上传单个文件到OSS
     *
     * @param file 文件
     * @param objectName OSS对象名称
     * @return OSS文件URL
     */
    String uploadFile(MultipartFile file, String objectName);

    /**
     * 上传字节数组到OSS
     *
     * @param data 文件数据
     * @param objectName OSS对象名称
     * @return OSS文件URL
     */
    String uploadBytes(byte[] data, String objectName);


    /**
     * 从OSS下载文件为字节数组
     *
     * @param ossUrl OSS文件URL
     * @return 文件字节数组
     */
    byte[] downloadFromOss(String ossUrl);

    /**
     * 删除OSS文件
     *
     * @param ossUrl OSS文件URL
     * @return 删除结果
     */
    boolean deleteOssFile(String ossUrl);
}
