package com.std.core.service.impl;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import com.aliyun.oss.model.SimplifiedObjectMeta;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.auth.sts.AssumeRoleRequest;
import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.qiniu.util.Auth;
import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.core.config.AliOSSConfig;
import com.std.core.define.RedisKeyList;
import com.std.core.enums.EConfigType;
import com.std.core.enums.EErrorCode;
import org.springframework.web.multipart.MultipartFile;
import com.std.core.pojo.response.AliOSSRes;
import com.std.core.service.ICommonService;
import com.std.core.service.IConfigService;
import com.std.core.util.FileUtil;
import com.std.core.util.IdGeneratorUtil;
import com.std.core.util.RedisUtil;
import com.std.core.util.SysConstants;
import com.std.core.util.SysConstantsCache;
import io.micrometer.core.instrument.util.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.junit.Test;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR> silver
 * @since : 2020-03-07 12:31
 */

@Service
@Slf4j
public class CommonServiceImpl implements ICommonService {

    @Resource
    private IConfigService configService;

    @Resource
    private AliOSSConfig aliOSSConfig;

    @Resource
    private RedisUtil redisUtil;

    private static Long redisLockTimeLong = 600L;

    @Override
    public String getQiniutoken() {
        Map<String, String> configMap = configService.listByType(EConfigType.QINIU.QINIU.getCode());
        if (null == configMap || configMap.isEmpty()) {
            throw new BizException(EErrorCode.AUTH00008);
        }

        String accessKey = configMap.get(EConfigType.QINIU.QINIU_ACCESS_KEY.getCode());
        String secretKey = configMap.get(EConfigType.QINIU.QINIU_SECRET_KEY.getCode());
        String bucket = configMap.get(EConfigType.QINIU.QINIU_BUCKET.getCode());
        if (StringUtils.isBlank(accessKey) || StringUtils.isBlank(secretKey)
                || StringUtils.isBlank(bucket)) {
            throw new BizException(EErrorCode.AUTH00008);
        }

        Auth auth = Auth.create(accessKey, secretKey);
        return auth.uploadToken(bucket);
    }

    @Override
    public AliOSSRes getAliOssToken() {
        try {
            String mtAliyunOssInfo = RedisKeyList.MT_ALIYUN_OSS_INFO;
            AliOSSRes aliOSSRes;
//            if (redisUtil.hasKey(mtAliyunOssInfo)) {
//                aliOSSRes = JSON.parseObject(JSON.toJSONString(redisUtil.get(mtAliyunOssInfo)), AliOSSRes.class);
//            } else {
//                aliOSSRes = getAliOSSRes();
//                if (null != aliOSSRes) {
//                    redisUtil.set(mtAliyunOssInfo, aliOSSRes, redisLockTimeLong);
//                }
//            }

            aliOSSRes = getAliOSSRes();

            return aliOSSRes;
        } catch (Exception e) {
            throw new BizException(EErrorCode.E500003.getCode(), e.getMessage());
        }
    }

    @NotNull
    private AliOSSRes getAliOSSRes() throws ClientException {
        // 添加endpoint（直接使用STS endpoint，前两个参数留空，无需添加region ID）
        DefaultProfile.addEndpoint("", "", "Sts", aliOSSConfig.getEndpoint());
        // 构造default profile（参数留空，无需添加region ID）
        IClientProfile profile = DefaultProfile.getProfile("", aliOSSConfig.getAccessKeyId(), aliOSSConfig.getAccessKeySecret());
        // 用profile构造client
        DefaultAcsClient client = new DefaultAcsClient(profile);
        final AssumeRoleRequest request = new AssumeRoleRequest();
        request.setMethod(MethodType.POST);
        request.setRoleArn(aliOSSConfig.getRoleArn());
        request.setRoleSessionName(IdGeneratorUtil.generateShortUuid());
//            request.setPolicy(policy); // 若policy为空，则用户将获得该角色下所有权限
        request.setDurationSeconds(3000L); // 设置凭证有效时间
        final AssumeRoleResponse response = client.getAcsResponse(request);

        AliOSSRes aliOSSRes = EntityUtils.copyData(response.getCredentials(), AliOSSRes.class);
        aliOSSRes.setBucket(aliOSSConfig.getBucket());
        aliOSSRes.setEndpoint(aliOSSConfig.getBucketEndpoint());
        aliOSSRes.setOssEndpoint(aliOSSConfig.getBucketOssEndpoint());
        aliOSSRes.setFilePath(aliOSSConfig.getBucketFilePath());
        return aliOSSRes;
    }


    @Override
    public SimplifiedObjectMeta getAliFileMeta(String name) {
        try {
            // 创建OSSClient实例。
            OSS ossClient = new OSSClientBuilder()
                    .build(aliOSSConfig.getBucketEndpoint(), aliOSSConfig.getAccessKeyId(), aliOSSConfig.getAccessKeySecret());
            // 获取文件的部分元信息。
            SimplifiedObjectMeta objectMeta = ossClient.getSimplifiedObjectMeta(aliOSSConfig.getBucket(), name);
//            System.out.println(objectMeta.getSize());
//            System.out.println(objectMeta.getETag());
//            System.out.println(objectMeta.getLastModified());

            // 关闭OSSClient。
            ossClient.shutdown();

            return objectMeta;
        } catch (Exception e) {
            throw new BizException(EErrorCode.E500003.getCode(), e.getMessage());
        }
    }


    @Override
    public BigDecimal getFileSize(String name) {
        try {
            name = URLDecoder.decode(name, "UTF-8");
            if (!name.contains("https")) {
                name = name.replace("http", "https");
            }
        } catch (Exception e) {
            throw new BizException(EErrorCode.CORE00000.getCode(), "转码失败");
        }
        if (name.contains(aliOSSConfig.getBucketFilePath())) {
            name = name.replace(aliOSSConfig.getBucketFilePath() + "/", "");
        }
        SimplifiedObjectMeta objectMeta = getAliFileMeta(name);

        return BigDecimal.valueOf(objectMeta.getSize()).divide(BigDecimal.valueOf(SysConstants.FILE_SIZE_MB), 2, RoundingMode.UP);
    }

    @Override
    public String httpUploadFileToAliyunOss(String fileUrl, String fileName) throws Exception {
        String suffix = fileUrl.substring(fileUrl.lastIndexOf("."));
        fileUrl = replaceUrl(fileUrl);
        URL url = new URL(fileUrl);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        //设置超时间为3秒
        conn.setConnectTimeout(3 * 1000);
        //防止屏蔽程序抓取而返回403错误
        conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");

        //得到输入流
        InputStream inputStream = conn.getInputStream();
        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder()
                .build(aliOSSConfig.getBucketEndpoint(), aliOSSConfig.getAccessKeyId(), aliOSSConfig.getAccessKeySecret());
        PutObjectRequest putObjectRequest = new PutObjectRequest(aliOSSConfig.getBucket(), fileName,
                new ByteArrayInputStream(FileUtil.readInputStream(inputStream)));
        ossClient.putObject(putObjectRequest);
        return aliOSSConfig.getBucketFilePath() + "/" + fileName + suffix;
    }

    @Override
    public String getFileFullUrl(String fileUrl) {
        if (!fileUrl.contains("http") && !fileUrl.contains("https")) {
            return aliOSSConfig.getBucketFilePath().concat("/").concat(fileUrl);
        }

        return fileUrl;
    }

    @Override
    public String putObject(String filePath) {
        // Endpoint以华东1（杭州）为例，其它Region请按实际情况填写。
        String endpoint = "https://oss-cn-hangzhou.aliyuncs.com";
        // 阿里云账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM用户进行API访问或日常运维，请登录RAM控制台创建RAM用户。
        String accessKeyId = aliOSSConfig.getAccessKeyId();
        String accessKeySecret = aliOSSConfig.getAccessKeySecret();
        // 填写Bucket名称，例如examplebucket。
        String bucketName = aliOSSConfig.getBucket();
        // 填写Object完整路径，完整路径中不能包含Bucket名称，例如exampledir/exampleobject.txt。
        long timeMillis = System.currentTimeMillis();
        String objectName = "exampledir/" + timeMillis + ".png";
//        String objectName = "exampledir/result.png";
        // 填写本地文件的完整路径，例如D:\\localpath\\examplefile.txt。
        // 如果未指定本地路径，则默认从示例程序所属项目对应本地路径中上传文件。
//        String filePath= "/Users/<USER>/Desktop/photo-test/1.png";

        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

        try {
            // 创建PutObjectRequest对象。
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, objectName, new File(filePath));
            // 如果需要上传时设置存储类型和访问权限，请参考以下示例代码。
            // ObjectMetadata metadata = new ObjectMetadata();
            // metadata.setHeader(OSSHeaders.OSS_STORAGE_CLASS, StorageClass.Standard.toString());
            // metadata.setObjectAcl(CannedAccessControlList.Private);
            // putObjectRequest.setMetadata(metadata);

            // 设置该属性可以返回response。如果不设置，则返回的response为空。
            putObjectRequest.setProcess("true");

            // 上传文件。
            PutObjectResult result = ossClient.putObject(putObjectRequest);
            // 如果上传成功，则返回200。
            System.out.println(result.getResponse().getStatusCode());
        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
        } catch (com.aliyun.oss.ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }

        objectName = aliOSSConfig.getBucketFilePath().concat("/").concat(objectName);
        return objectName;
    }

    @Override
    public String putObject(String filePath, String v4UUID) {
        String objectName = "mbear_two/" + v4UUID + ".png";
        objectName = putObjectByName(filePath, objectName);
        return objectName;
    }

    @Override
    public String putObjectByName(String filePath, String objectName){
        // Endpoint以华东1（杭州）为例，其它Region请按实际情况填写。
        String endpoint = "https://oss-cn-hangzhou.aliyuncs.com";
        // 阿里云账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM用户进行API访问或日常运维，请登录RAM控制台创建RAM用户。
        String accessKeyId = aliOSSConfig.getAccessKeyId();
        String accessKeySecret = aliOSSConfig.getAccessKeySecret();
        // 填写Bucket名称，例如examplebucket。
        String bucketName = aliOSSConfig.getBucket();
        // 填写Object完整路径，完整路径中不能包含Bucket名称，例如exampledir/exampleobject.txt。

//        String objectName = "exampledir/result.png";
        // 填写本地文件的完整路径，例如D:\\localpath\\examplefile.txt。
        // 如果未指定本地路径，则默认从示例程序所属项目对应本地路径中上传文件。
//        String filePath= "/Users/<USER>/Desktop/photo-test/1.png";

        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

        try {
            // 创建PutObjectRequest对象。
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, objectName, new File(filePath));
            // 如果需要上传时设置存储类型和访问权限，请参考以下示例代码。
            // ObjectMetadata metadata = new ObjectMetadata();
            // metadata.setHeader(OSSHeaders.OSS_STORAGE_CLASS, StorageClass.Standard.toString());
            // metadata.setObjectAcl(CannedAccessControlList.Private);
            // putObjectRequest.setMetadata(metadata);

            // 设置该属性可以返回response。如果不设置，则返回的response为空。
            putObjectRequest.setProcess("true");

            // 上传文件。
            PutObjectResult result = ossClient.putObject(putObjectRequest);
            // 如果上传成功，则返回200。
            System.out.println(result.getResponse().getStatusCode());
        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
        } catch (com.aliyun.oss.ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }

        objectName = aliOSSConfig.getBucketFilePath().concat("/").concat(objectName);
        return objectName;
    }
    @Override
    public String putObjectTest() {
        String filePath = "/Users/<USER>/project/clkj-project/meta/PFP-period/PFP/2000/Mutant Bear (1999).png";
        String v4UUID = UUID.randomUUID().toString().replace("-", "");
        System.out.println("v4UUID:" + v4UUID);
        // Endpoint以华东1（杭州）为例，其它Region请按实际情况填写。
        String endpoint = "https://oss-cn-hangzhou.aliyuncs.com";
        // 阿里云账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM用户进行API访问或日常运维，请登录RAM控制台创建RAM用户。
        String accessKeyId = "LTAI5tFH7oZ6KMKbiBLU3K5T";
        String accessKeySecret = "******************************";
        // 填写Bucket名称，例如examplebucket。
        String bucketName = "metat";
        // 填写Object完整路径，完整路径中不能包含Bucket名称，例如exampledir/exampleobject.txt。

//        String objectName = "mbear/" + v4UUID + ".png";
        String objectName = "mbear/d11662c39248406db2586d74c17674b8.png";
        System.out.println("objectName:" + objectName);
//        String objectName = "exampledir/result.png";
        // 填写本地文件的完整路径，例如D:\\localpath\\examplefile.txt。
        // 如果未指定本地路径，则默认从示例程序所属项目对应本地路径中上传文件。
//        String filePath= "/Users/<USER>/Desktop/photo-test/1.png";

        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

        try {
            // 创建PutObjectRequest对象。
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, objectName, new File(filePath));
            // 如果需要上传时设置存储类型和访问权限，请参考以下示例代码。
            // ObjectMetadata metadata = new ObjectMetadata();
            // metadata.setHeader(OSSHeaders.OSS_STORAGE_CLASS, StorageClass.Standard.toString());
            // metadata.setObjectAcl(CannedAccessControlList.Private);
            // putObjectRequest.setMetadata(metadata);

            // 设置该属性可以返回response。如果不设置，则返回的response为空。
            putObjectRequest.setProcess("true");

            // 上传文件。
            PutObjectResult result = ossClient.putObject(putObjectRequest);
            // 如果上传成功，则返回200。
            System.out.println(result.getResponse().getStatusCode());
        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
        } catch (com.aliyun.oss.ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }

        objectName = aliOSSConfig.getBucketFilePath().concat("/").concat(objectName);
        return objectName;
    }


    @Override
    public String deleteObject(String objectName) {
        // Endpoint以华东1（杭州）为例，其它Region请按实际情况填写。
        String endpoint = "https://oss-cn-hangzhou.aliyuncs.com";
        // 阿里云账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM用户进行API访问或日常运维，请登录RAM控制台创建RAM用户。
        String accessKeyId = aliOSSConfig.getAccessKeyId();
        String accessKeySecret = aliOSSConfig.getAccessKeySecret();
        // 填写Bucket名称，例如examplebucket。
        String bucketName = aliOSSConfig.getBucket();
        // 填写Object完整路径，完整路径中不能包含Bucket名称，例如exampledir/exampleobject.txt。

        objectName = objectName.replace(aliOSSConfig.getBucketFilePath().concat("/"), "");
        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

        try {
            log.info("删除阿里云图片开始");
            ossClient.deleteObject(bucketName, objectName);
            System.out.println("success");
        } catch (OSSException oe) {
            log.error("删除阿里云图片失败，原因oe{}", oe);
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
        } catch (com.aliyun.oss.ClientException ce) {
            log.error("删除阿里云图片失败，原因ce{}", ce);
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }



        return "删除成功";
    }

    @Override
    public List<String> batchUploadFiles(List<MultipartFile> files, String prefix) {
        List<String> resultUrls = new ArrayList<>();

        for (int i = 0; i < files.size(); i++) {
            MultipartFile file = files.get(i);
            try {
                // 生成唯一文件名
                String fileName = generateFileName(file.getOriginalFilename(), prefix, i + 1);
                String ossUrl = uploadFile(file, fileName);
                resultUrls.add(ossUrl);
                log.info("文件上传成功: {} -> {}", file.getOriginalFilename(), ossUrl);
            } catch (Exception e) {
                log.error("文件上传失败: {}", file.getOriginalFilename(), e);
                resultUrls.add(null); // 添加null表示上传失败
            }
        }

        return resultUrls;
    }

    @Override
    public List<String> batchUploadFromUrls(List<String> imageUrls, String prefix) {
        List<String> resultUrls = new ArrayList<>();

        for (int i = 0; i < imageUrls.size(); i++) {
            String imageUrl = imageUrls.get(i);
            try {
                // 生成唯一文件名
                String fileName = generateFileNameFromUrl(imageUrl, prefix, i + 1);
                String ossUrl = httpUploadFileToAliyunOss(imageUrl, fileName);
                resultUrls.add(ossUrl);
                log.info("URL图片上传成功: {} -> {}", imageUrl, ossUrl);
            } catch (Exception e) {
                log.error("URL图片上传失败: {}", imageUrl, e);
                resultUrls.add(null); // 添加null表示上传失败
            }
        }

        return resultUrls;
    }

    @Override
    public String uploadFile(MultipartFile file, String objectName) {
        try {
            // 创建OSSClient实例
            OSS ossClient = new OSSClientBuilder()
                    .build(aliOSSConfig.getBucketEndpoint(), aliOSSConfig.getAccessKeyId(), aliOSSConfig.getAccessKeySecret());

            // 上传文件
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                    aliOSSConfig.getBucket(),
                    objectName,
                    file.getInputStream()
            );

            ossClient.putObject(putObjectRequest);
            ossClient.shutdown();

            // 返回完整的文件URL
            return aliOSSConfig.getBucketFilePath() + "/" + objectName;

        } catch (Exception e) {
            log.error("文件上传到OSS失败", e);
            throw new BizException(EErrorCode.E500003.getCode(), "文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String originalFilename, String prefix, int index) {
        String extension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }

        String uuid = UUID.randomUUID().toString().replace("-", "");
        return prefix + "/" + uuid + "_" + index + extension;
    }

    /**
     * 从URL生成文件名
     */
    private String generateFileNameFromUrl(String imageUrl, String prefix, int index) {
        String extension = "";
        if (imageUrl.contains(".")) {
            String urlPath = imageUrl.substring(imageUrl.lastIndexOf("/") + 1);
            if (urlPath.contains(".")) {
                extension = urlPath.substring(urlPath.lastIndexOf("."));
                // 只保留常见的图片扩展名
                if (!extension.matches("\\.(jpg|jpeg|png|gif|bmp|webp)$")) {
                    extension = ".png"; // 默认扩展名
                }
            } else {
                extension = ".png"; // 默认扩展名
            }
        } else {
            extension = ".png"; // 默认扩展名
        }

        String uuid = UUID.randomUUID().toString().replace("-", "");
        return prefix + "/" + uuid + "_" + index + extension;
    }

    @Override
    public String uploadBytes(byte[] data, String objectName) {
        try {
            // 创建OSSClient实例
            OSS ossClient = new OSSClientBuilder()
                    .build(aliOSSConfig.getBucketEndpoint(), aliOSSConfig.getAccessKeyId(), aliOSSConfig.getAccessKeySecret());

            // 上传字节数组
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                    aliOSSConfig.getBucket(),
                    objectName,
                    new ByteArrayInputStream(data)
            );

            ossClient.putObject(putObjectRequest);
            ossClient.shutdown();

            // 返回完整的文件URL
            return aliOSSConfig.getBucketFilePath() + "/" + objectName;

        } catch (Exception e) {
            log.error("字节数组上传到OSS失败", e);
            throw new BizException(EErrorCode.E500003.getCode(), "文件上传失败: " + e.getMessage());
        }
    }

    @Override
    public byte[] downloadFromOss(String ossUrl) {
        try {
            // 从完整URL中提取对象名称
            String objectName = extractObjectNameFromUrl(ossUrl);

            // 创建OSSClient实例
            OSS ossClient = new OSSClientBuilder()
                    .build(aliOSSConfig.getBucketEndpoint(), aliOSSConfig.getAccessKeyId(), aliOSSConfig.getAccessKeySecret());

            // 下载文件
            OSSObject ossObject = ossClient.getObject(aliOSSConfig.getBucket(), objectName);

            // 读取文件内容
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[8192];
            int bytesRead;

            try (InputStream inputStream = ossObject.getObjectContent()) {
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    baos.write(buffer, 0, bytesRead);
                }
            }

            ossClient.shutdown();

            log.info("从OSS下载文件成功: {}", objectName);
            return baos.toByteArray();

        } catch (Exception e) {
            log.error("从OSS下载文件失败: {}", ossUrl, e);
            throw new BizException(EErrorCode.E500003.getCode(), "从OSS下载文件失败: " + e.getMessage());
        }
    }

    @Override
    public boolean deleteOssFile(String ossUrl) {
        try {
            // 从完整URL中提取对象名称
            String objectName = extractObjectNameFromUrl(ossUrl);

            // 创建OSSClient实例
            OSS ossClient = new OSSClientBuilder()
                    .build(aliOSSConfig.getBucketEndpoint(), aliOSSConfig.getAccessKeyId(), aliOSSConfig.getAccessKeySecret());

            // 删除文件
            ossClient.deleteObject(aliOSSConfig.getBucket(), objectName);
            ossClient.shutdown();

            log.info("删除OSS文件成功: {}", objectName);
            return true;

        } catch (Exception e) {
            log.error("删除OSS文件失败: {}", ossUrl, e);
            return false;
        }
    }

    /**
     * 从OSS URL中提取对象名称
     */
    private String extractObjectNameFromUrl(String ossUrl) {
        if (ossUrl == null || ossUrl.isEmpty()) {
            throw new IllegalArgumentException("OSS URL不能为空");
        }

        // 移除域名部分，保留对象名称
        String baseUrl = aliOSSConfig.getBucketFilePath();
        if (ossUrl.startsWith(baseUrl)) {
            String objectName = ossUrl.substring(baseUrl.length());
            // 移除开头的斜杠
            if (objectName.startsWith("/")) {
                objectName = objectName.substring(1);
            }
            return objectName;
        }

        // 如果不是标准格式，尝试从URL中解析
        try {
            java.net.URL url = new java.net.URL(ossUrl);
            String path = url.getPath();
            if (path.startsWith("/")) {
                path = path.substring(1);
            }
            return path;
        } catch (Exception e) {
            throw new IllegalArgumentException("无效的OSS URL格式: " + ossUrl);
        }
    }

    public static void main(String[] args) {
        String str = "https://metat.oss-accelerate.aliyuncs.com/mbear/d11662c39248406db2586d74c17674b8.png";
        String toBeDeleted = "https://metat.oss-accelerate.aliyuncs.com/mbear/";
        String result = str.replace(toBeDeleted, "");
        System.out.println(result); // 输出: Hello
    }
    private String replaceUrl(String url) {

        String ipfsRoot = configService.getStringValue(SysConstantsCache.IPFS_ROOT_HTTP_URL);
        if (url.contains("ipfs://")) {
            url = url.replace("ipfs://", ipfsRoot);
        }
        return url;
    }

//    public static String testHttpUploadFileToAliyunOss(String fileUrl, String fileName) throws Exception {
//
//        URL url = new URL(fileUrl);
//        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
//        //设置超时间为3秒
//        conn.setConnectTimeout(3 * 1000);
//        //防止屏蔽程序抓取而返回403错误
//        conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
//
//        //得到输入流
//        InputStream inputStream = conn.getInputStream();
//
//        String keyid = "LTAI5tMzAMgj37GEMG8iHQSa";
//        String secret = "******************************";
//        String endpoint = "http://oss-accelerate.aliyuncs.com";
//        String bucket = "lianstreets-bucket";
//        OSS ossClient = new OSSClientBuilder().build(endpoint, keyid, secret);
//        PutObjectRequest putObjectRequest = new PutObjectRequest(bucket, fileName,
//                new ByteArrayInputStream(FileUtil.readInputStream(inputStream)));
//
//        ossClient.putObject(putObjectRequest);
//        return "https://lianstreets-bucket.oss-accelerate.aliyuncs.com/" + fileName;
//    }
//
//    public static void main(String[] args) {
//        String url = "https://goblingoonslair.com/static/goblin-images/33.PNG";
//        String name = IdGeneratorUtil.generateTimeMatch();
//        System.out.println(name);
//
//        try {
//            System.out.println(testHttpUploadFileToAliyunOss(url, name));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }



}

