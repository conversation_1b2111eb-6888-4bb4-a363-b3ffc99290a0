package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.EProducedRefType;
import com.std.core.enums.EProducedStatus;
import com.std.core.enums.EUserKind;
import com.std.core.mapper.ProducedMapper;
import com.std.core.pojo.domain.Company;
import com.std.core.pojo.domain.Produced;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.ProducedDetailRes;
import com.std.core.pojo.response.ProducedListRes;
import com.std.core.pojo.response.ProducedPageRes;
import com.std.core.service.ICompanyService;
import com.std.core.service.IProducedService;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 出品方ServiceImpl
 *
 * <AUTHOR> ycj
 * @since : 2022-06-14 15:31
 */
@Service
public class ProducedServiceImpl implements IProducedService {

    @Resource
    private ProducedMapper producedMapper;

    @Resource
    private ICompanyService companyService;

    /**
     * 新增出品方
     *
     * @param req      新增出品方入参
     * @param operator 操作人
     */
    @Override
    public void create(ProducedCreateReq req, User operator) {
        Produced produced = EntityUtils.copyData(req, Produced.class);
        if (EUserKind.SYS.getCode().equals(operator.getKind())) {
            produced.setRefType(EProducedRefType.PRODUCED_REFTYPE_0.getCode());
        } else if (EUserKind.BP.getCode().equals(operator.getKind())) {
            produced.setRefType(EProducedRefType.PRODUCED_REFTYPE_1.getCode());
            produced.setRefId(operator.getCompanyId());
        }

        produced.setStatus(EProducedStatus.PRODUCED_STATUS_0.getCode());
        produced.setUpdater(operator.getId());
        produced.setUpdaterName(operator.getLoginName());
        produced.setUpdateDatetime(new Date());
        producedMapper.insertSelective(produced);
    }

    @Override
    public void companyCreate(ProducedCreateReq req, User operator) {
        Produced produced = EntityUtils.copyData(req, Produced.class);
        produced.setRefType(EProducedRefType.PRODUCED_REFTYPE_1.getCode());
        produced.setRefId(operator.getCompanyId());
        produced.setStatus(EProducedStatus.PRODUCED_STATUS_0.getCode());
        produced.setUpdater(operator.getId());
        produced.setUpdaterName(operator.getLoginName());
        produced.setUpdateDatetime(new Date());
        producedMapper.insertSelective(produced);
    }

    @Override
    public void create(Produced req) {
        producedMapper.insertSelective(req);
    }

    /**
     * 删除出品方
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        producedMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改出品方
     *
     * @param req      修改出品方入参
     * @param operator 操作人
     */
    @Override
    public void modify(ProducedModifyReq req, User operator) {
        Produced detail = detail(req.getId());
        if (EUserKind.BP.getCode().equals(operator.getKind()) && EProducedRefType.PRODUCED_REFTYPE_0.getCode().equals(detail.getRefType())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无修改资格");
        } else if (EUserKind.BP.getCode().equals(operator.getKind()) && EProducedRefType.PRODUCED_REFTYPE_1.getCode().equals(detail.getRefType()) && !detail.getRefId().equals(operator.getCompanyId())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无修改资格");
        }

        Produced produced = EntityUtils.copyData(req, Produced.class);
        produced.setUpdater(operator.getId());
        produced.setUpdaterName(operator.getLoginName());
        produced.setUpdateDatetime(new Date());
        producedMapper.updateByPrimaryKeySelective(produced);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpDown(ProducedBatchModifyReq request, User operator) {
        for (Long id : request.getIdList()) {
            Produced produced = detail(id);

            if (EUserKind.BP.getCode().equals(operator.getKind()) && EProducedRefType.PRODUCED_REFTYPE_0.getCode().equals(produced.getRefType())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无修改资格");
            } else if (EUserKind.BP.getCode().equals(operator.getKind()) && EProducedRefType.PRODUCED_REFTYPE_1.getCode().equals(produced.getRefType()) && !produced.getRefId().equals(operator.getCompanyId())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无修改资格");
            }
            produced.setStatus(request.getStatus());
            produced.setUpdater(operator.getId());
            produced.setUpdaterName(operator.getLoginName());
            produced.setUpdateDatetime(new Date());
            producedMapper.updateByPrimaryKeySelective(produced);
        }
    }

    /**
     * 详情查询出品方
     *
     * @param id 主键ID
     * @return 出品方对象
     */
    @Override
    public Produced detail(Long id) {
        Produced produced = producedMapper.selectByPrimaryKey(id);
        if (null == produced) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        if (EProducedRefType.PRODUCED_REFTYPE_0.getCode().equals(produced.getRefType())) {
            produced.setCompanyName("麦塔平台");
        } else {
            Company company = companyService.detail(produced.getRefId());
            produced.setCompanyName(company.getName());
        }
        return produced;
    }

    @Override
    public Produced detail(Long id, User operator) {
        Produced produced = producedMapper.selectByPrimaryKey(id);
        if (null == produced) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        if (EUserKind.BP.getCode().equals(operator.getKind())) {
            if (EProducedRefType.PRODUCED_REFTYPE_0.getCode().equals(produced.getRefType())
                    || (EProducedRefType.PRODUCED_REFTYPE_1.getCode().equals(produced.getRefType()) && !operator.getCompanyId().equals(produced.getRefId()))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权访问");
            }
        } else if (!EUserKind.SYS.getCode().equals(operator.getKind())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权访问");
        }

        if (EProducedRefType.PRODUCED_REFTYPE_0.getCode().equals(produced.getRefType())) {
            produced.setCompanyName("麦塔平台");
        } else {
            Company company = companyService.detail(produced.getRefId());
            produced.setCompanyName(company.getName());
        }
        return produced;
    }

    /**
     * 分页查询出品方
     *
     * @param req      分页查询出品方入参
     * @param operator
     * @return 分页出品方对象
     */
    @Override
    public List<Produced> page(ProducedPageReq req, User operator) {
        Produced condition = EntityUtils.copyData(req, Produced.class);
        condition.setOrderBy("t.id desc");
        if (EUserKind.SYS.getCode().equals(operator.getKind())) {
            condition.setRefType(null);
            condition.setRefId(req.getRefId());
        } else if (EUserKind.BP.getCode().equals(operator.getKind())) {
            condition.setRefType(EProducedRefType.PRODUCED_REFTYPE_1.getCode());
            condition.setRefId(operator.getCompanyId());
        } else {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权访问");
        }

        List<Produced> producedList = producedMapper.selectByCondition(condition);

        for (Produced produced : producedList) {
            if (EProducedRefType.PRODUCED_REFTYPE_0.getCode().equals(produced.getRefType())) {
                produced.setCompanyName("麦塔平台");
            } else {
                Company company = companyService.detail(produced.getRefId());
                produced.setCompanyName(company.getName());
            }
        }
        return producedList;
    }

    /**
     * 列表查询出品方
     *
     * @param req      列表查询出品方入参
     * @param operator
     * @return 列表出品方对象
     */
    @Override
    public List<Produced> list(ProducedListReq req, User operator) {
        Produced condition = EntityUtils.copyData(req, Produced.class);
        condition.setOrderBy("t.id desc");
        List<Produced> producedList = new ArrayList<>();
        if (EUserKind.SYS.getCode().equals(operator.getKind())) {

            producedList = producedMapper.selectByCondition(condition);

            if (null != req.getRefId()) {
                condition.setRefType(EProducedRefType.PRODUCED_REFTYPE_1.getCode());
                condition.setRefId(req.getRefId());

            }
        } else if (EUserKind.BP.getCode().equals(operator.getKind())) {
            condition.setRefType(EProducedRefType.PRODUCED_REFTYPE_1.getCode());
            condition.setRefId(operator.getCompanyId());
            producedList = producedMapper.selectByCondition(condition);
        }

        for (Produced produced : producedList) {
            if (EProducedRefType.PRODUCED_REFTYPE_0.getCode().equals(produced.getRefType())) {
                produced.setCompanyName("麦塔平台");
            } else {
                Company company = companyService.detail(produced.getRefId());
                produced.setCompanyName(company.getName());
            }
        }
        return producedList;
    }

    /**
     * 前端详情查询出品方
     *
     * @param id 主键ID
     * @return 出品方对象
     */
    @Override
    public ProducedDetailRes detailFront(Long id) {
        ProducedDetailRes res = new ProducedDetailRes();

        Produced produced = producedMapper.selectByPrimaryKey(id);
        if (null == produced) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        BeanUtils.copyProperties(produced, res);

        return res;
    }

    /**
     * 前端分页查询出品方
     *
     * @param req 前端分页查询出品方入参
     * @return 分页出品方对象
     */
    @Override
    public List<ProducedPageRes> pageFront(ProducedPageFrontReq req) {
        Produced condition = EntityUtils.copyData(req, Produced.class);
        List<Produced> producedList = producedMapper.selectByCondition(condition);

        List<ProducedPageRes> resList = producedList.stream().map((entity) -> {
            ProducedPageRes res = new ProducedPageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(producedList, resList);
    }

    /**
     * 前端列表查询出品方
     *
     * @param req 前端列表查询出品方入参
     * @return 列表出品方对象
     */
    @Override
    public List<ProducedListRes> listFront(ProducedListFrontReq req) {
        Produced condition = EntityUtils.copyData(req, Produced.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), Produced.class));

        List<Produced> producedList = producedMapper.selectByCondition(condition);

        List<ProducedListRes> resList = producedList.stream().map((entity) -> {
            ProducedListRes res = new ProducedListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

}