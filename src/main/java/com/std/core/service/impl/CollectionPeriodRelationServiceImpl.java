package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.define.RedisKeyList;
import com.std.core.enums.EErrorCode;
import com.std.core.mapper.CollectionPeriodRelationMapper;
import com.std.core.pojo.domain.AwardEntity;
import com.std.core.pojo.domain.CollectionPeriodRelation;
import com.std.core.pojo.domain.PeriodCollectionStatistics;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.CollectionPeriodCollectionPageRes;
import com.std.core.pojo.response.CollectionPeriodRelationDetailRes;
import com.std.core.pojo.response.CollectionPeriodRelationListRes;
import com.std.core.pojo.response.CollectionPeriodRelationPageRes;
import com.std.core.service.ICollectionPeriodRelationService;
import com.std.core.util.RedisUtil;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * 藏品作品期数ServiceImpl
 *
 * <AUTHOR> ycj
 * @since : 2021-12-14 15:45
 */
@Service
public class CollectionPeriodRelationServiceImpl implements ICollectionPeriodRelationService {

    @Resource
    private CollectionPeriodRelationMapper collectionPeriodRelationMapper;

    @Resource
    private RedisUtil redisUtil;

    private static Long redisLockTime = 600L;

    /**
     * 新增藏品作品期数
     *
     * @param req 新增藏品作品期数入参
     * @param operator 操作人
     */
    @Override
    public void create(CollectionPeriodRelationCreateReq req, User operator) {
        CollectionPeriodRelation collectionPeriodRelation = EntityUtils.copyData(req, CollectionPeriodRelation.class);
        collectionPeriodRelationMapper.insertSelective(collectionPeriodRelation);
    }

    /**
     * 删除藏品作品期数
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        collectionPeriodRelationMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改藏品作品期数
     *
     * @param req 修改藏品作品期数入参
     * @param operator 操作人
     */
    @Override
    public void modify(CollectionPeriodRelationModifyReq req, User operator) {
        CollectionPeriodRelation collectionPeriodRelation = EntityUtils.copyData(req, CollectionPeriodRelation.class);
        collectionPeriodRelationMapper.updateByPrimaryKeySelective(collectionPeriodRelation);
    }

    @Override
    public void modify(CollectionPeriodRelation req) {
        collectionPeriodRelationMapper.updateByPrimaryKeySelective(req);
    }

    /**
     * 详情查询藏品作品期数
     *
     * @param id 主键ID
     * @return 藏品作品期数对象
     */
    @Override
    public CollectionPeriodRelation detail(Long id) {
        CollectionPeriodRelation collectionPeriodRelation = collectionPeriodRelationMapper.selectByPrimaryKey(id);
        if (null == collectionPeriodRelation) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return collectionPeriodRelation;
    }

    @Override
    public CollectionPeriodRelation detailRelation(Long periodId, Long collectionId) {
        CollectionPeriodRelation data;

        CollectionPeriodRelation condition = new CollectionPeriodRelation();
        condition.setPeriodId(periodId);
        condition.setCollectionId(collectionId);
        List<CollectionPeriodRelation> collectionPeriodRelationList = collectionPeriodRelationMapper.selectByCondition(condition);
//        List<CollectionPeriodRelation> collectionPeriodRelationList = collectionPeriodRelationMapper.selectRelation(condition);

        if (CollectionUtils.isNotEmpty(collectionPeriodRelationList)) {
            data = collectionPeriodRelationList.get(0);
        } else {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "期数关联藏品数据异常");
        }

        return data;
    }

    /**
     * 分页查询藏品作品期数
     *
     * @param req 分页查询藏品作品期数入参
     * @return 分页藏品作品期数对象
     */
    @Override
    public List<CollectionPeriodRelation> page(CollectionPeriodRelationPageReq req) {
        CollectionPeriodRelation condition = EntityUtils.copyData(req, CollectionPeriodRelation.class);

        List<CollectionPeriodRelation> collectionPeriodRelationList = collectionPeriodRelationMapper.selectByCondition(condition);

        return collectionPeriodRelationList;
    }

    /**
     * 列表查询藏品作品期数
     *
     * @param req 列表查询藏品作品期数入参
     * @return 列表藏品作品期数对象
     */
    @Override
    public List<CollectionPeriodRelation> list(CollectionPeriodRelationListReq req) {
        CollectionPeriodRelation condition = EntityUtils.copyData(req, CollectionPeriodRelation.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), CollectionPeriodRelation.class));

        return collectionPeriodRelationMapper.selectByCondition(condition);
    }

    @Override
    public List<CollectionPeriodRelation> list(CollectionPeriodRelation relation) {

        return collectionPeriodRelationMapper.selectByCondition(relation);
    }

    @Override
    public List<CollectionPeriodRelation> list(Long periodId) {
        CollectionPeriodRelation condition = new CollectionPeriodRelation();
        condition.setPeriodId(periodId);
        return collectionPeriodRelationMapper.selectByCondition(condition);
    }

    /**
     * 前端详情查询藏品作品期数
     *
     * @param id 主键ID
     * @return 藏品作品期数对象
     */
    @Override
    public CollectionPeriodRelationDetailRes detailFront(Long id) {
        CollectionPeriodRelationDetailRes res = new CollectionPeriodRelationDetailRes();

        CollectionPeriodRelation collectionPeriodRelation = collectionPeriodRelationMapper.selectByPrimaryKey(id);
        if (null == collectionPeriodRelation) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        BeanUtils.copyProperties(collectionPeriodRelation, res);

        return res;
    }

    @Override
    public CollectionPeriodRelation detailForUpdate(Long id) {


        CollectionPeriodRelation collectionPeriodRelation = collectionPeriodRelationMapper.selectForUpdate(id);
        if (null == collectionPeriodRelation) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }


        return collectionPeriodRelation;
    }

    /**
     * 前端分页查询藏品作品期数
     *
     * @param req 前端分页查询藏品作品期数入参
     * @return 分页藏品作品期数对象
     */
    @Override
    public List<CollectionPeriodRelationPageRes> pageFront(CollectionPeriodRelationPageFrontReq req) {
        CollectionPeriodRelation condition = EntityUtils.copyData(req, CollectionPeriodRelation.class);
        List<CollectionPeriodRelation> collectionPeriodRelationList = collectionPeriodRelationMapper.selectByCondition(condition);

        List<CollectionPeriodRelationPageRes> resList = collectionPeriodRelationList.stream().map((entity) -> {
            CollectionPeriodRelationPageRes res = new CollectionPeriodRelationPageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(collectionPeriodRelationList, resList);
    }

    /**
     * 前端列表查询藏品作品期数
     *
     * @param req 前端列表查询藏品作品期数入参
     * @return 列表藏品作品期数对象
     */
    @Override
    public List<CollectionPeriodRelationListRes> listFront(CollectionPeriodRelationListFrontReq req) {
        CollectionPeriodRelation condition = EntityUtils.copyData(req, CollectionPeriodRelation.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), CollectionPeriodRelation.class));

        List<CollectionPeriodRelation> collectionPeriodRelationList = collectionPeriodRelationMapper.selectByCondition(condition);

        List<CollectionPeriodRelationListRes> resList = collectionPeriodRelationList.stream().map((entity) -> {
            CollectionPeriodRelationListRes res = new CollectionPeriodRelationListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

    @Override
    public Integer getPeriodLockTime(Long period) {
        Integer lockTime = 0;
        String redisKey = String.format(RedisKeyList.MT_PERIOD_LOCK_TIME_KEY, period);
        if (redisUtil.hasKey(redisKey)) {
            lockTime = (Integer) redisUtil.get(redisKey);
        } else {
            lockTime = collectionPeriodRelationMapper.getPeriodLockTime(period);
            redisUtil.set(redisKey, lockTime, redisLockTime);
        }
        return lockTime;
    }

    @Override
    public Integer getPeriodTransformLimitTime(Long period) {
        Integer transformLimitTime = 0;
        String redisKey = String.format(RedisKeyList.MT_PERIOD_TRANSFORM_LIMIT_TIME_KEY, period);
        if (redisUtil.hasKey(redisKey)) {
            transformLimitTime = (Integer) redisUtil.get(redisKey);
        } else {
            transformLimitTime = collectionPeriodRelationMapper.getPeriodTransformLimitTime(period);
            redisUtil.set(redisKey, transformLimitTime, redisLockTime);
        }
        return transformLimitTime;
    }

    @Override
    public List<AwardEntity> ListByPeriodId(Long periodId, String config, List<Long> idList) {
        if (null == periodId) {
            return null;
        }

        return collectionPeriodRelationMapper.ListByPeriodId(periodId, config, idList);
    }

    @Override
    public Integer selectSellCollectionCount(Long companyId) {
        return collectionPeriodRelationMapper.selectSellCollectionCount(companyId);
    }

    @Override
    public List<PeriodCollectionStatistics> periodCollectionStatistics(CollectionPagePeriodCollectionStatisticsReq request) {
        return collectionPeriodRelationMapper.selectPeriodCollectionStatistics(request);
    }

    @Override
    public List<CollectionPeriodCollectionPageRes> detailListByPeriodId(Long periodId, Integer collectionNumber) {

        return collectionPeriodRelationMapper.selectListByPeriodId(periodId, collectionNumber);
    }

    @Override
    public List<Long> listRank(Long periodId, int limit) {
        return collectionPeriodRelationMapper.selectCollectionIdListRank(periodId,limit);
    }

    @Override
    public CollectionPeriodRelation listRankAndHaveRemain(Long periodId, int limit) {
        return collectionPeriodRelationMapper.selectRankAndHaveRemain(periodId,limit);
    }

    @Override
    public CollectionPeriodRelation listRankAndHaveRemainByToken(Long periodId, int limit) {
        return collectionPeriodRelationMapper.selectRankAndHaveRemainByToken(periodId,limit);
    }

    @Override
    public List<CollectionPeriodRelation> listPeriodId(Long periodId, String periodStatus) {
        return collectionPeriodRelationMapper.selectPeriodId(periodId,periodStatus);
    }

    @Override
    public List<CollectionPeriodRelation> listByCollectionId(Long collectionId) {
        CollectionPeriodRelation condition = new CollectionPeriodRelation();
        condition.setCollectionId(collectionId);
        List<CollectionPeriodRelation> collectionPeriodRelationList = collectionPeriodRelationMapper.selectByCondition(condition);
        return collectionPeriodRelationList;
    }
}