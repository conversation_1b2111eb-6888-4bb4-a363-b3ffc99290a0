package com.std.core.service.impl;

import com.ais.common.utils.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.std.common.base.BaseIdReq;
import com.std.common.base.BasePageReq;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.enums.ESmsOutBizType;
import com.std.common.exception.BizException;
import com.std.common.service.ISmsOutService;
import com.std.common.utils.DateUtil;
import com.std.common.utils.EntityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import com.std.common.utils.PwdUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.AgoraIO.agora.media.RtcTokenBuilder2;
import com.std.core.config.AgoraConfig;
import com.std.core.define.RedisKeyList;
import com.std.core.enums.EAgoraRole;
import com.std.core.enums.EBoolean;
import com.std.core.enums.EChannelType;
import com.std.core.enums.ECollectionDetailOwnerType;
import com.std.core.enums.ECollectionDetailRecordTradeType;
import com.std.core.enums.ECollectionDetailStatus;
import com.std.core.enums.EConfigType;
import com.std.core.enums.EConfigType.AUTH;
import com.std.core.enums.EConfigType.INCOME;
import com.std.core.enums.EContractChain;
import com.std.core.enums.ECurrency;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.EIdentifyOrderStatus;
import com.std.core.enums.EIdentifyStyle;
import com.std.core.enums.EJourBizTypeSystem;
import com.std.core.enums.EJourBizTypeUser;
import com.std.core.enums.ENoticeWindowStatus;
import com.std.core.enums.ESmsRefType;
import com.std.core.enums.ESystemAccount;
import com.std.core.enums.ESystemName;
import com.std.core.enums.EUserIdentifyStatus;
import com.std.core.enums.EUserKind;
import com.std.core.enums.EUserLogType;
import com.std.core.enums.EUserNodeLevel;
import com.std.core.enums.EUserNodeLevelType;
import com.std.core.enums.EUserRecoveryStatus;
import com.std.core.enums.EUserStatus;
import com.std.core.mapper.CuserMapper;
import com.std.core.mapper.UserMapper;
import com.std.core.pojo.domain.Account;
import com.std.core.pojo.domain.ChannelMerchant;
import com.std.core.pojo.domain.Collection;
import com.std.core.pojo.domain.CollectionDetail;
import com.std.core.pojo.domain.CollectionDetailRecord;
import com.std.core.pojo.domain.Config;
import com.std.core.pojo.domain.Cuser;
import com.std.core.pojo.domain.IdentifyOrder;
import com.std.core.pojo.domain.NodeConfig;
import com.std.core.pojo.domain.NoticeWindow;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.domain.UserAuthChannel;
import com.std.core.pojo.domain.UserChainAddress;
import com.std.core.pojo.domain.UserRecoveryCollection;
import com.std.core.pojo.domain.UserRecoveryRecord;
import com.std.core.pojo.domain.UserResetRecord;
import com.std.core.pojo.request.AgoraTokenReq;
import com.std.core.pojo.request.CuserBatchResetReq;
import com.std.core.pojo.request.CuserCreateReq;
import com.std.core.pojo.request.CuserListReq;
import com.std.core.pojo.request.CuserLogoffReq;
import com.std.core.pojo.request.CuserModifyIdentifyStyleReq;
import com.std.core.pojo.request.CuserModifyReq;
import com.std.core.pojo.request.CuserPageReq;
import com.std.core.pojo.request.CuserSetRateReq;
import com.std.core.pojo.request.CuserSetVirtualIncomeReq;
import com.std.core.pojo.request.ModifyUserMobileReq;
import com.std.core.pojo.request.NoticeWindowReadReq;
import com.std.core.pojo.request.ResetRefUserReq;
import com.std.core.pojo.request.UserChangeRefereeReq;
import com.std.core.pojo.request.UserChannelSettingCancelReq;
import com.std.core.pojo.request.UserChannelSettingReq;
import com.std.core.pojo.request.UserCompanyRegisterReq;
import com.std.core.pojo.request.UserGetPhotoReq;
import com.std.core.pojo.request.UserGroupingSettingReq;
import com.std.core.pojo.request.UserLoginChannelReq;
import com.std.core.pojo.request.UserLoginReq;
import com.std.core.pojo.request.UserLoginRes;
import com.std.core.pojo.request.UserModifyReq;
import com.std.core.pojo.request.UserRegisterByOssReq;
import com.std.core.pojo.request.UserRegisterReq;
import com.std.core.pojo.request.UserStatusReq;
import com.std.core.pojo.request.ThirdPartyUserRegisterReq;
import com.std.core.pojo.request.XmlyBindMobileReq;
import com.std.core.pojo.request.XmlyCheckBindMobileReq;
import com.std.core.pojo.response.AgoraTokenRes;
import com.std.core.pojo.response.CommunityUserRes;
import com.std.core.pojo.response.SmsUnreadMessagesRes;
import com.std.core.pojo.response.SubUserRes;
import com.std.core.pojo.response.TeamUserRes;
import com.std.core.pojo.response.UserDeatilRes;
import com.std.core.pojo.response.UserGetPhotoRes;
import com.std.core.pojo.response.UserHomeRes;
import com.std.core.pojo.response.UserMyCommunityHeadRes;
import com.std.core.pojo.response.UserMyTeamRes;
import com.std.core.pojo.response.UserNodeLevelSummaryRes;
import com.std.core.service.IAccountService;
import com.std.core.service.IChannelMerchantService;
import com.std.core.service.ICollectionDetailRecordService;
import com.std.core.service.ICollectionDetailService;
import com.std.core.service.ICollectionService;
import com.std.core.service.IConfigService;
import com.std.core.service.ICuserService;
import com.std.core.service.IIdentifyOrderService;
import com.std.core.service.ILoginErrorRecordService;
import com.std.core.service.INodeConfigService;
import com.std.core.service.INoticeWindowService;
import com.std.core.service.IPeriodUserReadService;
import com.std.core.service.ISmsReadService;
import com.std.core.service.ISmsService;
import com.std.core.service.IUserAuthChannelService;
import com.std.core.service.IUserChainAddressService;
import com.std.core.service.IUserLogService;
import com.std.core.service.IUserNodeLevelService;
import com.std.core.service.IUserRecoveryCollectionService;
import com.std.core.service.IUserRecoveryRecordService;
import com.std.core.service.IUserRoleService;
import com.std.core.service.IUserService;
import com.std.core.service.IYaoTaskConfigService;
import com.std.core.util.AmountUtil;
import com.std.core.util.IdGeneratorUtil;
import com.std.core.util.InviteCodeUtil;
import com.std.core.util.PrivacyUtil;
import com.std.core.util.RedisLock;
import com.std.core.util.RedisUtil;
import com.std.core.util.SysConstants;
import com.std.core.util.SysProperties;
import com.std.core.util.TokenUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mindrot.jbcrypt.BCrypt;
import org.redisson.api.RBloomFilter;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * C端用户ServiceImpl
 *
 * <AUTHOR> Leo
 * @since : 2020-05-18 20:09
 */
@Service
@Slf4j
public class CuserServiceImpl implements ICuserService {

    @Resource
    private IUserService userService;

    @Resource
    private CuserMapper cuserMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    private IUserNodeLevelService userNodeLevelService;

    @Resource
    private IConfigService configService;

    @Resource
    private IUserLogService userLogService;

    @Resource
    private ISmsService smsService;

    @Resource
    private ISmsOutService smsOutService;

    @Resource
    private IUserRoleService userRoleService;

    @Resource
    private SysProperties sysProperties;

    @Resource
    private INodeConfigService nodeConfigService;

    @Resource
    private IPeriodUserReadService periodUserReadService;

    @Resource
    private TokenUtil tokenUtil;

    @Autowired
    protected RedisUtil redisUtil;

    @Resource
    private IIdentifyOrderService identifyOrderService;

    @Resource
    private IUserRecoveryRecordService userRecoveryRecordService;

    @Resource
    private IUserRecoveryCollectionService userRecoveryCollectionService;

    @Resource
    private IAccountService accountService;

    @Resource
    private ICollectionDetailService collectionDetailService;

    @Resource
    private ICollectionService collectionService;

    @Resource
    private ICollectionDetailRecordService collectionDetailRecordService;

    @Resource
    private INoticeWindowService noticeWindowService;

    @Resource
    private IUserAuthChannelService userAuthChannelService;

    @Resource
    private IChannelMerchantService channelMerchantService;

    @Resource
    private ISmsReadService smsReadService;

    @Resource
    private ILoginErrorRecordService loginErrorRecordService;

    @Resource
    private IYaoTaskConfigService yaoTaskConfigService;

    @Resource
    private AgoraConfig agoraConfig;

    @Resource
    private IUserChainAddressService userChainAddressService;

    @Resource
    private RedisLock redisLock;

    @Value("${meta.lock.timeout}")
    private Long metaLockTimeout;

    @Resource
    private RedissonClient redissonClient;


    /**
     * 新增C端用户
     *
     * @param req 新增C端用户入参
     * @param operator 操作人
     */
    @Override
    public void create(CuserCreateReq req, User operator) {
        Cuser cuser = EntityUtils.copyData(req, Cuser.class);
        cuserMapper.insertSelective(cuser);
    }

    /**
     * 怀南会产品服务条款
     */
    @Override
    public Config detailRegisterServiceConfig() {
        String key = "register_service_config";

        return configService.detailByKey(key);
    }

    /**
     * 删除C端用户
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        cuserMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改C端用户
     *
     * @param req 修改C端用户入参
     * @param operator 操作人
     */
    @Override
    public void modify(CuserModifyReq req, User operator) {
        Cuser cuser = EntityUtils.copyData(req, Cuser.class);

        cuserMapper.updateByPrimaryKeySelective(cuser);
    }

    @Override
    public void modify(Cuser cuser) {
        cuserMapper.updateByPrimaryKeySelective(cuser);
    }

    /**
     * 设置状态
     */
    @Override
    public void modifyStatus(UserStatusReq req, User operator, String ip) {
        Cuser cuser = detail(req.getId());
        User userDetail = userMapper.selectByPrimaryKey(cuser.getUserId());
        if (EUserStatus.CLOSE.getCode().equals(userDetail.getStatus()) || EUserStatus.SYS_CLOSE.getCode().equals(userDetail.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前用户已注销，不能操作");
        }

        if (EUserStatus.LOCK.getCode().equals(req.getStatus()) && StringUtils.isBlank(req.getRemark())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "锁定理由不能为空");
        }

        User user = new User();
        user.setId(cuser.getUserId());
        user.setStatus(req.getStatus());
        user.setUpdater(operator.getId());
        user.setUpdateDatetime(new Date());
        user.setRemark(req.getRemark());
        userMapper.updateByPrimaryKeySelective(user);

        String newStatus = EUserStatus.getUserStatus(req.getStatus()).getValue();
        String content = "状态更改为" + newStatus + ",理由为：" + req.getRemark();
        userLogService.create(operator, EUserLogType.CUSER.getCode(), EUserLogType.CUSER.getValue(), ip, content);

        //删除token
        redisUtil.del(user.getId().toString());
        redisUtil.del(user.getId().toString());
    }

    /**
     * 系统注销状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doSysClose(Long userId, String remark, User operator, String ip) {
        Cuser cuser = detail(userId);
        User userDetail = userMapper.selectByPrimaryKey(cuser.getUserId());
        if (EUserStatus.CLOSE.getCode().equals(userDetail.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), cuser.getLoginName() + "用户已注销，不能操作");
        }

        User user = new User();
        user.setId(userDetail.getId());
        user.setStatus(EUserStatus.SYS_CLOSE.getCode());
        user.setUpdater(operator.getId());
        user.setUpdateDatetime(new Date());
        user.setRemark(remark);
        userMapper.updateByPrimaryKeySelective(user);

        String content = "状态更改为" + EUserStatus.SYS_CLOSE.getValue() + ",理由为：" + remark;
        userLogService.create(userDetail, EUserLogType.CUSER.getCode(), EUserLogType.CUSER.getValue(), ip, content);

        //删除token
        redisUtil.del(user.getId().toString());
    }

    /**
     * 详情查询C端用户
     *
     * @param id 主键ID
     * @return C端用户对象
     */
    @Override
    public Cuser detail(Long id) {
        Cuser cuser = cuserMapper.selectByPrimaryKey(id);
        if (null == cuser) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        cuser.setIsFreeUser(EBoolean.NO.getCode());
        if (null == cuser.getUserReferee()) {
            cuser.setIsFreeUser(EBoolean.YES.getCode());
        }
        return cuser;
    }

    /**
     * 分页查询C端用户
     *
     * @param req 分页查询C端用户入参
     * @return 分页C端用户对象
     */
    @Override
    public List<Cuser> page(CuserPageReq req) {
        Cuser condition = EntityUtils.copyData(req, Cuser.class);
        condition.setStatus(req.getStatus());
        condition.setChannelFlag(req.getChannelFlag());
        condition.setIsChannel(req.getIsChannel());
        condition.setChannelId(req.getChannelId());
        condition.setChannelType(req.getChannelType());
        condition.setIsRealName(null);
        if (StringUtils.isNotBlank(req.getIsRealName()) && EBoolean.YES.getCode().equals(req.getIsRealName())) {
            condition.setIsRealName(EBoolean.YES.getCode());
        } else if (StringUtils.isNotBlank(req.getIsRealName()) && EBoolean.NO.getCode().equals(req.getIsRealName())) {
            condition.setNoRealName(EBoolean.YES.getCode());
        }
        condition.setDate(new Date());

        if (StringUtils.isNotBlank(req.getIsFreeUser()) && EBoolean.YES.getCode().equals(req.getIsFreeUser())) {
            condition.setIsFreeUser(EBoolean.YES.getCode());
        } else if (StringUtils.isNotBlank(req.getIsFreeUser()) && EBoolean.NO.getCode().equals(req.getIsFreeUser())) {
            condition.setIsNotFreeUser(EBoolean.YES.getCode());
        }
        List<Cuser> cusers = cuserMapper.select(condition);

        for (Cuser cuser : cusers) {
            cuser.setLoginName(PrivacyUtil.encryptPhoneNo(cuser.getLoginName()));
            cuser.setMobile(PrivacyUtil.encryptPhoneNo(cuser.getMobile()));
            cuser.setEmail(PrivacyUtil.encryptEmail(cuser.getEmail()));

            if (null != cuser.getUserReferee()) {
                cuser.setRefereeUser(userService.dealUser(userService.detailSimpleInfo(cuser.getUserReferee())));
            }
            cuser.setIsRealName(StringUtils.isNotBlank(cuser.getRealName())
                    ? EBoolean.YES.getCode() : EBoolean.NO.getCode());

            cuser.setIsFreeUser(EBoolean.NO.getCode());
            if (null == cuser.getUserReferee()) {
                cuser.setIsFreeUser(EBoolean.YES.getCode());
            }

        }

        return cusers;
    }

    /**
     * 模糊查询
     */
    @Override
    public List<Cuser> vagueDeatil(CuserPageReq req) {
        Cuser cuser = EntityUtils.copyData(req, Cuser.class);

        return cuserMapper.selectVague(cuser);
    }

    /**
     * 列表查询C端用户
     *
     * @param req 列表查询C端用户入参
     * @return 列表C端用户对象
     */
    @Override
    public List<Cuser> list(CuserListReq req) {
        Cuser condition = EntityUtils.copyData(req, Cuser.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), Cuser.class));

        return cuserMapper.selectByCondition(condition);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JSONObject register(UserRegisterReq request, String ip) {

        String inviteFlag = configService.getStringValue(SysConstants.INVITE_FLAG);
        if (EBoolean.YES.getCode().equals(inviteFlag)) {
            if (StringUtils.isBlank(request.getInviteCode())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您填写您的邀请码");
            }
        }

        if (StringUtils.isBlank(request.getInviteCode())) {
            String defaultInviteCode = configService.getStringValue(SysConstants.DEFAULT_INVITE_CODE);
            if (StringUtils.isNotBlank(defaultInviteCode)) {
                User user = userService.getUserAndUnCheckByInviteCode(defaultInviteCode);
                if (user != null) {
                    request.setInviteCode(defaultInviteCode);
                }
            }
        }

        User user = userService.register(request, ip);

        cUserInsert(user);

        // 每日登录获得爻
        String lockId = "yaoLoginConfig" + user.getId();
        Long time = System.currentTimeMillis() + metaLockTimeout;

        if (!redisLock.lock(lockId, String.valueOf(time))) {
            //已经被锁定，啥也不做
        } else {
            try {
                // 每日登录获得爻
                yaoTaskConfigService.create(user);
            } finally {
                redisLock.unlock(lockId, String.valueOf(time));
            }
        }

        // 系统名称
        String systemName = configService.getStringValue(ESystemName.SYSTEM_NAME.getCode());

        // 默认阅读注册协议和隐私条款
        noticeWindowRead(SysConstants.REGISTERED_AGREEMENT_TEXTAREA, user);
        noticeWindowRead(SysConstants.PRIVACY_AGREEMENT_TEXTAREA, user);

        String title = "感谢注册";
        String content = "尊敬的" + request.getMobile().substring(0, 3) + "****" + request.getMobile().substring(8, 11)
                + "用户，您好，欢迎注册使用" + systemName + "产品";
        smsService.sendMyMsg(user, title, content, ESmsRefType.SYSTEM_MESSAGE.getCode(), ESmsRefType.SYSTEM_MESSAGE.getValue());

        // 用户加入布隆过滤器
        addNewUserToRedissonBloomFilter(user.getLoginName());
        return tokenUtil.generalToken(user.getId(), 5 * 24 * 60 * 60 * 1000L);
    }

    @Override
    public void addNewUserToRedissonBloomFilter(String loginName) {
        // 获取布隆过滤器
        RBloomFilter bf = redissonClient.getBloomFilter("meta-sms-filter");
        // 将新用户的登录名添加到布隆过滤器中
        bf.add(loginName);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public JSONObject companyRegister(UserCompanyRegisterReq req, String userKind, String ip) {
        // 生成发行方用户
        User user = userService.createCompany(req, userKind);

        return tokenUtil.generalToken(user.getId(), 5 * 24 * 60 * 60 * 1000L);
    }

    @Override
    public JSONObject doCheckBindMobile(XmlyCheckBindMobileReq request) {
        JSONObject jsonObject = new JSONObject();

        User user = userService.selectUserChannelUid(request.getChannelId(), request.getUid());

        if (null != user) {
            jsonObject = tokenUtil.generalToken(user.getId(), 5 * 24 * 60 * 60 * 1000L);
            if (StringUtils.isNotBlank(user.getLoginName())) {
                jsonObject.put("isBindMobile", EBoolean.YES.getCode());
            } else {
                jsonObject.put("isBindMobile", EBoolean.NO.getCode());
            }
        } else {
            jsonObject.put("isBindMobile", EBoolean.NO.getCode());
        }

        return jsonObject;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JSONObject doBindMobile(XmlyBindMobileReq request, String ip) {
        // 验证手机号
        userService.doCheckSmsCaptcha(request.getMobile(), request.getSmsCode(), ESmsOutBizType.THIRD_PLAT_REG.getCode());

        // 用户已注册，直接返回token
//        User data = userService.selectUserChannelUid(request.getChannelId(), request.getUid());
        UserAuthChannel authChannel = userAuthChannelService.detailByChannelUid(request.getChannelId(), request.getUid());
        if (null != authChannel) {
            return tokenUtil.generalToken(authChannel.getUserId(), 5 * 24 * 60 * 60 * 1000L);
        }

        // 查询手机号是否已存在，且未关联uid的
        User mobileUser = userService.selectUserByMobile(request.getMobile(), EUserKind.C.getCode());
        if (null != mobileUser) {
            UserAuthChannel userAuthChannel = userAuthChannelService.detailByChannel(mobileUser.getId(), request.getChannelId());
            if (null != userAuthChannel) {
                if (StringUtils.isNotBlank(userAuthChannel.getChannelUid())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "手机号已绑定uid");
                } else {
                    userAuthChannel.setChannelUid(request.getUid());
                    userAuthChannelService.modify(userAuthChannel);
                }
            } else {
                UserAuthChannel userChannel = new UserAuthChannel();
                userChannel.setUserId(mobileUser.getId());
                userChannel.setChannelId(request.getChannelId());
                userChannel.setChannelUid(request.getUid());
                userChannel.setCreateDatetime(new Date());
                userAuthChannelService.create(userChannel);
            }
            mobileUser.setChannelUid(request.getUid());
            mobileUser.setChannelMerchantId(request.getChannelId());
            userService.modifyUser(mobileUser);
            return tokenUtil.generalToken(mobileUser.getId(), 5 * 24 * 60 * 60 * 1000L);
        }

        String defaultInviteCode = configService.getStringValue(SysConstants.DEFAULT_INVITE_CODE);
        if (StringUtils.isNotBlank(defaultInviteCode)) {
            User user = userService.getUserAndUnCheckByInviteCode(defaultInviteCode);
            if (user != null) {
                request.setInviteCode(defaultInviteCode);
            }
        }

        String pwd = RandomUtil.generate6();
        User user = userService.doRegisterXmly(request, ip, pwd);

        cUserInsert(user);

        // 系统名称
        String systemName = configService.getStringValue(ESystemName.SYSTEM_NAME.getCode());

        // 默认阅读注册协议和隐私条款
        noticeWindowRead(SysConstants.REGISTERED_AGREEMENT_TEXTAREA, user);

        noticeWindowRead(SysConstants.PRIVACY_AGREEMENT_TEXTAREA, user);

        String title = "感谢注册";
        String content = "尊敬的" + request.getMobile().substring(0, 3) + "****" + request.getMobile().substring(8, 11)
                + "用户，您好，欢迎注册使用" + systemName + "产品";
        smsService.sendMyMsg(user, title, content, ESmsRefType.SYSTEM_MESSAGE.getCode(), ESmsRefType.SYSTEM_MESSAGE.getValue());
        content = content.concat("您的密码是" + pwd + "，请尽快修改");
        smsOutService.sendSmsOut(user.getMobile(), content, null);

        return tokenUtil.generalToken(user.getId(), 5 * 24 * 60 * 60 * 1000L);
    }

    private void noticeWindowRead(String configKey, User user) {
        NoticeWindow noticeWindow = new NoticeWindow();
        noticeWindow.setStatus(ENoticeWindowStatus.NOTICE_WINDOW_STATUS_1.getCode());
        noticeWindow.setConfigKey(configKey);
        List<NoticeWindow> list = noticeWindowService.list(noticeWindow);

        for (NoticeWindow notice : list) {
            NoticeWindowReadReq req = new NoticeWindowReadReq();
            req.setId(notice.getId());
            noticeWindowService.read(req, user);
        }
    }

    @Override
    public User ossRegister(UserRegisterByOssReq request) {
        User user = EntityUtils.copyData(request, User.class);
        Long userId = IdGeneratorUtil.generator();
        Date now = new Date();
        user.setId(userId);
        user.setKind(EUserKind.C.getCode());
        user.setNickname(userId.toString().substring(userId.toString().length() - 8, userId.toString().length()));
        user.setLoginName(user.getMobile());
        user.setLoginPwdStrength(PwdUtil.calculateSecurityLevel(user.getLoginPwd()));
        String hashedPassword = BCrypt.hashpw(user.getLoginPwd(), BCrypt.gensalt(12));
        user.setLoginPwd(hashedPassword);
//        user.setLoginPwd(MD5Util.md5(user.getLoginPwd()));
        user.setRegisterDatetime(now);
        user.setLastLoginDatetime(now);
        user.setStatus(EUserStatus.NORMAL.getCode());
        user.setPhoto(configService.getStringValue("user_photo"));
        userMapper.insertSelective(user);
        // 添加默认权限
        userRoleService.allotRole(user, sysProperties.getDefaultRoleC());

        return user;
    }

    @Override
    public UserLoginRes login(UserLoginReq request, Long channelId, String ip) {
        return userService.loginC(request, channelId, ip);
    }

    @Override
    public JSONObject loginChannel(UserLoginChannelReq request, Long channelId, String ip) {
        return userService.login(request, channelId, ip);
    }

    @Override
    public Cuser detailByUserId(Long userId) {
        Cuser condition = new Cuser();
        condition.setUserId(userId);
        List<Cuser> cuserList = cuserMapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(cuserList)) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "userId不存在");
        }
        return cuserList.get(0);
    }

    /**
     * 根据用户序号查询C端用户信息
     */
    @Override
    public Cuser detailByUser(Long userId) {
        Cuser cuser = cuserMapper.selectByUserId(userId);
        if (null == cuser) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), userId);
        }
        return cuser;
    }

    /**
     * 会员查询
     */
    @Override
    public List<UserDeatilRes> userDeatilRes() {

//        cuserMapper
        return null;
    }

    @Override
    public List<TeamUserRes> teamPage(CuserPageReq request) {
        List<TeamUserRes> teamUserList = new ArrayList<>();
        request.setNodeLevelType(EUserNodeLevelType.MEMBER.getCode());
        List<Cuser> cuserList = page(request);
        for (Cuser cuser : cuserList) {
            TeamUserRes teamUser = new TeamUserRes();
            teamUser.setUserId(cuser.getUserId());
            teamUser.setNickname(cuser.getNickname());
            teamUser.setRealName(cuser.getRealName());
            teamUser.setMobile(cuser.getMobile());
            teamUser.setStatus(cuser.getStatus());
            teamUser.setCreditScore(BigDecimal.ZERO);
            teamUser.setLevelName(cuser.getLevelName());
            teamUser.setMemberFlag(cuser.getMemberFlag());
            teamUser.setNodeLevel(EUserNodeLevel.LEVEL_0.getCode());
            User data = userService.detailBrief(cuser.getUserId());
            teamUser.setGrouping(data.getGrouping());
            // 今日盈利和累计盈利
            List<User> meList = new ArrayList<>();
            User me = new User();
            me.setId(cuser.getUserId());
            List<User> userList = new ArrayList<>();
            User user = new User();
            user.setId(cuser.getUserId());
            userList.add(user);
            teamUser.setAmount(BigDecimal.ZERO);
            //直推人数
            user = new User();
            user.setUserReferee(cuser.getUserId());
            user.setKind(EUserKind.C.getCode());
            Integer count = userService.selectCount(user);
            teamUser.setReferUserCount(count);
            //直推销售额
            userList = new ArrayList<>();
            userService.queryAllInferior(userList, cuser.getUserId(), 1);
            teamUser.setReferAmount(BigDecimal.ZERO);
            //团队人数
            userList = new ArrayList<>();
            userService.queryAllInferior(userList, cuser.getUserId(),
                    configService.getIntegerValue(EConfigType.INCOME.NODE_INCOME_LEVEL.getCode()));
            teamUser.setTeamCount(userList.size());
            //团队销售额（需要加上自己的）
            user = new User();
            user.setId(cuser.getUserId());
            userList.add(user);
            teamUser.setTeamAmount(BigDecimal.ZERO);
            teamUserList.add(teamUser);
        }
        return teamUserList;
    }

    /**
     * 重置推荐人
     */
    @Override
    public void changeReferUser(UserChangeRefereeReq request) {

        User user = userService.detail(request.getUserId());
        if (user.getId().equals(request.getUserReferee())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "上级用户不能为自己!");
        }

        User user2 = userService.detail(request.getUserReferee());
        if (user2.getId() == null) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "推荐人不能为空");
        }

        if (user2.getUserReferee() != null) {
            if (user2.getUserReferee().equals(user.getId())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "推荐人不能互为上级用户!");
            }
        }
//        if (user.getIsChannel().equals(EBoolean.YES.getCode())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您选择的用户为渠道商,无法更改!");
//        }

        //判断推荐人 是否是自己的下级，如果是，不允许调整
        //拿到自己的团队
        List<User> userList = new ArrayList<>();
        userService.queryAllInferiorNotInviteLevel(userList, user.getId());
        if (userList.stream().anyMatch(temp -> temp.getId().equals(request.getUserReferee()))) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您选择的上级属于自己团队中下级,无法更改!");
        }

        userList = new ArrayList<>();
        userService.queryAllInferiorNotInviteLevelSubChannel(userList, user.getId());
        user.setChannelFlag(user2.getChannelFlag());
        user.setChannelId(user2.getChannelId());
        user.setUserReferee(request.getUserReferee());
        if (user2.getIsChannel().equals(EBoolean.YES.getCode())) {
            user.setChannelId(user2.getId());
        }

        userMapper.updateByPrimaryKeySelective(user);

//        userList.add(user);
        //批量更新用户的渠道标识
        userService.batchModifyChannelFlag(userList, user2.getChannelFlag(), user2.getChannelId());

    }

    @Override
    public UserNodeLevelSummaryRes nodeLevelSummary(User operator) {
        UserNodeLevelSummaryRes res = new UserNodeLevelSummaryRes();

        // 全局收益配置
        Map<String, String> configMap = configService.listByType(INCOME.TYPE.getCode());
        BigDecimal performanceRate1 = new BigDecimal(configMap.get(INCOME.PERFORMANCE_RATE_1.getCode()))
                .divide(new BigDecimal("100"), 4, BigDecimal.ROUND_DOWN);
        BigDecimal performanceRate2 = new BigDecimal(configMap.get(INCOME.PERFORMANCE_RATE_2.getCode()))
                .divide(new BigDecimal("100"), 4, BigDecimal.ROUND_DOWN);

        // 查询1代团队列表(直推)
        List<User> teamList1 = userService.getUsersByUserReferee(operator.getId());
        res.setTeamCount(teamList1.size());

        // 查询2代团队列表(次推)
        List<User> teamList2 = new ArrayList<>();
        for (User user : teamList1) {
            teamList2.addAll(userService.getUsersByUserReferee(user.getId()));
        }

        String level = userNodeLevelService.selectHighPriority(operator.getId(), EUserNodeLevelType.DISTRIBUTION);

        NodeConfig nodeConfig = nodeConfigService.selectByLevel(level);
        if (null == nodeConfig) {
            res.setLevel(EUserNodeLevel.LEVEL_0.getValue());
        } else {
            res.setLevel(EUserNodeLevel.getUserNodeLevel(nodeConfig.getLevel()).getValue());
        }

        res.setNodeConfigList(nodeConfigService.selectConfigListAsc());

        return res;
    }

    @Override
    public UserMyTeamRes myTeam(User operator) {
        UserMyTeamRes res = new UserMyTeamRes();
//        String todayStr = DateUtil.getToday(DateUtil.DATA_TIME_PATTERN_9);
//
//        List<SubUserRes> subUserRes = userService.subUserList(operator);
//        if (CollectionUtils.isNotEmpty(subUserRes)) {
//            subUserRes.forEach(item -> {
//                List<User> meList = new ArrayList<>();
//                User me = new User();
//                me.setId(item.getId());
//                meList.add(me);
//                item.setTodayIncome(AmountUtil.toScaleAmount(xstrategyCycleHistoryService.selectTeamProfitByDate(meList, todayStr), 4));
//                item.setTotalIncome(AmountUtil.toScaleAmount(xstrategyCycleHistoryService.selectTeamProfit(meList), 4));
//            });
//        }
//
//        //总收益倒序
////        subUserRes = subUserRes.stream().sorted(Comparator.comparing(SubUserRes::getTotalIncome).reversed().thenComparing(SubUserRes::getMemberFlag).reversed()).collect(Collectors.toList());
//        subUserRes = subUserRes.stream().sorted(Comparator.comparing(SubUserRes::getTotalIncome).reversed()).collect(Collectors.toList());
//
//
//        int acviteCount = 0;
//        for (SubUserRes item : subUserRes) {
//            if (EBoolean.YES.getCode().equals(item.getMemberFlag())) {
//                acviteCount++;
//            }
//        }
//        res.setTeamCount(subUserRes.size());
//        res.setTeamActiveCount(acviteCount);
//        res.setTeamList(subUserRes);
//
//        // 团队层数默认为全局配置的节点层数，如果想无限级，就将此值配大
//        Map<String, String> configMap = configService.listByType(INCOME.TYPE.getCode());
//        Integer cycleLevel = Integer.valueOf(configMap.get(INCOME.NODE_INCOME_LEVEL.getCode()));
//        BigDecimal performanceRate1 = new BigDecimal(configMap.get(INCOME.PERFORMANCE_RATE_1.getCode()))
//                .divide(new BigDecimal("100"), 4, BigDecimal.ROUND_DOWN);
//        BigDecimal performanceRate2 = new BigDecimal(configMap.get(INCOME.PERFORMANCE_RATE_2.getCode()))
//                .divide(new BigDecimal("100"), 4, BigDecimal.ROUND_DOWN);
//
//        // 查询1代团队列表(直推)
//        List<User> teamList1 = userService.getUsersByUserReferee(operator.getId());
//
//        // 查询2代团队列表(次推)
//        List<User> teamList2 = new ArrayList<>();
//        for (User user : teamList1) {
//            teamList2.addAll(userService.getUsersByUserReferee(user.getId()));
//        }
//
//        BigDecimal teamTodayIncome1 = xstrategyCycleHistoryService.selectTeamProfitByDate(teamList1, todayStr).multiply(performanceRate1);
//        BigDecimal teamTodayIncome2 = xstrategyCycleHistoryService.selectTeamProfitByDate(teamList2, todayStr).multiply(performanceRate2);
//        res.setTeamTodayIncome(AmountUtil.toScaleAmount(teamTodayIncome1.add(teamTodayIncome2), 4));
//
//        BigDecimal teamTotalIncome1 = xstrategyCycleHistoryService.selectTeamProfit(teamList1).multiply(performanceRate1);
//        BigDecimal teamTotalIncome2 = xstrategyCycleHistoryService.selectTeamProfit(teamList2).multiply(performanceRate2);
//        res.setTeamTotalIncome(AmountUtil.toScaleAmount(teamTotalIncome1.add(teamTotalIncome2), 4));

        return res;
    }

    @Override
    public void setRate(CuserSetRateReq request) {
        Cuser cuser = new Cuser();
        cuser.setId(request.getId());
        cuser.setRate(request.getRate());
        cuserMapper.updateByPrimaryKeySelective(cuser);
    }

    /**
     * 设置用户分组
     */
    @Override
    public void userGroupingSetting(UserGroupingSettingReq req, User operator) {
        Cuser cuser = detail(req.getId());

        UserModifyReq userModifyReq = new UserModifyReq();
        userModifyReq.setId(cuser.getUserId());
        userModifyReq.setGrouping(req.getGrouping());
        userService.modifyByOss(userModifyReq, operator);
    }

    @Override
    public Cuser getSpecificUser() {
        return cuserMapper.getSpecificUser();
    }

    @Override
    public void setVirtualIncome(CuserSetVirtualIncomeReq request) {
        User user = userService.detail(request.getUserId(), EUserKind.C);
        Cuser cuser = detailByUser(user.getId());
        cuser.setVirtualIncome(request.getVirtualIncome());
        cuserMapper.updateByPrimaryKeySelective(cuser);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void userChannelSetting(UserChannelSettingReq req, User operator) {
        User user = userService.detailBrief(req.getId());

        user.setChannelFlag(req.getChannelFlag());
        //判断下级用户是否需要更改。
        List<User> userList = new ArrayList<>();
        if (req.getIsChange().equals(EBoolean.YES.getCode())) {
            //除去所有已经是渠道商的下级
//            userService.queryAllInferiorNotInviteLevel(userList, user.getId());
            userService.queryAllInferiorNotInviteLevelSubChannel(userList, user.getId());
        }

        userService.batchModifyChannelFlag(userList, req.getChannelFlag(), user.getId());

        user.setIsChannel(EBoolean.YES.getCode());
        userService.modifyUser(user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelChannelSetting(UserChannelSettingCancelReq req, User operator) {
        User user = userService.detailBrief(req.getId());
        if (EBoolean.NO.getCode().equals(user.getIsChannel())) {
            throw new BizException(EErrorCode.CORE00000.getCode(), "选择的用户不是渠道商，无需取消");
        }
        user.setIsChannel(EBoolean.NO.getCode());
        userService.modifyUser(user);

        //他的渠道用户归属于上一个渠道商
        List<User> userList = userService.getCommunityUserList(user.getId());
        Long channelUserId = user.getChannelId();

        if (null != channelUserId) {
            for (User data : userList) {
                data.setChannelId(channelUserId);
                userService.modifyUser(data);
            }
        }

    }

    @Override
    public void modifyMobile(ModifyUserMobileReq req, User operator) {
        User user = userService.detail(req.getId());
        userService.checkMobileExist(req.getMobile(), EUserKind.C.getCode());
        if (user.getLoginName().contains(user.getMobile())) {
            user.setLoginName(req.getMobile());
        }
        user.setMobile(req.getMobile());
        userService.modifyUser(user);

        String userCacheKey = String.format(RedisKeyList.MT_USER_INFO_KEY, user.getId().toString());
        redisUtil.set(userCacheKey, user);


    }

    @Override
    public void resetRefUser(ResetRefUserReq req, User operator) {

    }

    @Override
    public UserMyCommunityHeadRes myCommunityHead(User operator) {
        UserMyCommunityHeadRes res = new UserMyCommunityHeadRes();
        res.setRanking(0);
        res.setCommunityTodayIncome(BigDecimal.ZERO);
        res.setCommunityTotalIncome(BigDecimal.ZERO);
        res.setCount(0);
        res.setIsSHow(EBoolean.NO.getCode());
        res.setTodayIncome(BigDecimal.ZERO);
        res.setTotalIncome("0.0000");

        Long id = operator.getChannelId();

        if (operator.getIsChannel().equals(EBoolean.NO.getCode())) {
            if (id == null) {
                return res;
            }
        } else {
            id = operator.getId();
        }

        String todayStr = DateUtil.getToday(DateUtil.DATA_TIME_PATTERN_9);

        //拿到所有的成员
        List<User> meList = userService.getCommunityUserList(id);
        res.setCount(meList.size());

//        BigDecimal communityTodayIncome = BigDecimal.ZERO;
//        BigDecimal communityTotalIncome = BigDecimal.ZERO;
//        for (User user : meList) {
//            Cuser cuser = detailByUserId(user.getId());
//            List<User> newList = new ArrayList<>();
//            User me = new User();
//            me.setId(user.getId());
//            newList.add(me);
//            communityTodayIncome = communityTodayIncome.add(xstrategyCycleHistoryService.selectTeamProfitByDate(newList, todayStr).multiply(cuser.getVirtualIncome()));
//        }

        return res;
    }

    @Override
    public List<SubUserRes> myCommunity(User operator, BasePageReq request) {

        Long id = operator.getChannelId();
        if (operator.getIsChannel().equals(EBoolean.YES.getCode())) {
            id = operator.getId();
        }

        String todayStr = DateUtil.getToday(DateUtil.DATA_TIME_PATTERN_9);
        List<SubUserRes> communityUserList = new ArrayList<>();

        communityUserList.forEach(res -> {
            res.setTodayIncome(AmountUtil.toScaleAmount(res.getTodayIncome(), 4));
            res.setTotalIncome(dealVolume(AmountUtil.toScaleAmount(new BigDecimal(res.getTotalIncome()), 4)));
        });

        return communityUserList;

//        //拿到所有的成员
//        List<User> userist = userService.getCommunityUserList(id);
//
//
//        for (User user : userist) {
//            SubUserRes res = new SubUserRes();
//            BeanUtils.copyProperties(user, res);
//            List<User> meList = new ArrayList<>();
//            Cuser cuser = detailByUserId(user.getId());
//            User me = new User();
//            me.setId(user.getId());
//            meList.add(me);
//            res.setTodayIncome(xstrategyCycleHistoryService.selectTeamProfitByDate(meList, todayStr).setScale(4, BigDecimal.ROUND_DOWN).multiply(cuser.getVirtualIncome()));
//            res.setTotalIncome(dealVolume(AmountUtil.toScaleAmount(xstrategyCycleHistoryService.selectTeamProfit(meList), 4).multiply(cuser.getVirtualIncome())));
//            list.add(res);
//        }

//        return PageInfoUtil.listToPage(userist, list);
    }

    @Override
    public List<CommunityUserRes> communityPage(BaseIdReq request) {
        List<User> userist = userService.getCommunityUserList(request.getId());
        List<CommunityUserRes> resList = new ArrayList<>();
        for (User user : userist) {
            CommunityUserRes res = new CommunityUserRes();
            BeanUtils.copyProperties(user, res);
            res.setUserId(user.getId());
            String level = userNodeLevelService.selectHighPriority(user.getId(), EUserNodeLevelType.DISTRIBUTION);
            res.setLevelName(level);
            resList.add(res);
        }
        return resList;
    }

    @Override
    public CommunityUserRes communityRef(BaseIdReq req) {
        //查询自己
        User user = userService.detailBrief(req.getId());
        CommunityUserRes res = new CommunityUserRes();
        BeanUtils.copyProperties(user, res);
        res.setUserId(user.getId());
        String level = userNodeLevelService.selectHighPriority(user.getId(), EUserNodeLevelType.DISTRIBUTION);
        res.setLevelName(level);
        res.setCount(nextChannel(req.getId()).size());
        return res;
    }

    @Override
    public List<CommunityUserRes> communityRef2(BaseIdReq req) {
        List<CommunityUserRes> resList = new ArrayList<>();
        List<Cuser> cuserList = nextChannel(req.getId());
        for (Cuser cuser : cuserList) {
            CommunityUserRes resnew = new CommunityUserRes();
            BeanUtils.copyProperties(cuser, resnew);
            resnew.setUserId(cuser.getUserId());
            String levelnew = userNodeLevelService.selectHighPriority(cuser.getUserId(), EUserNodeLevelType.DISTRIBUTION);
            resnew.setLevelName(levelnew);
            resnew.setCount(nextChannel(cuser.getUserId()).size());
            resList.add(resnew);
        }
        return resList;
    }

    @Override
    public UserGetPhotoRes getUserPhotoByMobile(String mobile) {
        //手机或者邀请码转赠
        User user = userService.detailBrief(mobile, EUserKind.C.getCode());
        if (null == user) {
            if (mobile.length() < 6) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "用户ID格式错误,请检查");
            }

            UserChainAddress chainAddress = userChainAddressService.checkUserChainAddress(mobile, EContractChain.Meta.getCode());
            if (null != chainAddress) {
                user = userService.detail(chainAddress.getUserId(), EUserKind.C);
                UserGetPhotoRes res = new UserGetPhotoRes();
                res.setPhoto(user.getPhoto());
                res.setNickname(user.getNickname());
                res.setAddressFlag(EBoolean.YES.getCode());
                return res;
            }

            user = userService.getUserAndUnCheckByInviteCode(mobile);
            if (null == user) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "转赠用户不存在");
            }
            String inviteCode = InviteCodeUtil.toSerialCode(user.getInviteNo());
            if (!inviteCode.equals(mobile)) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "转赠用户ID错误");
            }
        }

        UserGetPhotoRes res = new UserGetPhotoRes();
        res.setPhoto(user.getPhoto());
        res.setNickname(user.getNickname());
        res.setAddressFlag(EBoolean.NO.getCode());
        return res;
    }

    @Override
    public UserGetPhotoRes checkUserByMobile(UserGetPhotoReq request) {
        //手机或者邀请码转赠
        User user = userService.detailBrief(request.getMobile(), EUserKind.C.getCode());
        UserGetPhotoRes res = new UserGetPhotoRes();
        if (null == user) {
            res.setFlag(EBoolean.NO.getCode());
        } else {
            res.setFlag(EBoolean.YES.getCode());
        }

        return res;
    }

    /**
     * 用户注销
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void logoff(User operator, CuserLogoffReq request) {
        if (!EUserStatus.NORMAL.getCode().equals(operator.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "你的账号状态不正常的，无法注销");
        }
        userService.doCheckSmsCaptcha(operator.getMobile(), request.getSmsCode(), ESmsOutBizType.CLOSE.getCode());

        userService.close(operator.getId());

        //删除token
        redisUtil.del(operator.getId().toString());
        //删除用户信息
        String userCacheKey = String.format(RedisKeyList.MT_USER_INFO_KEY, operator.getId().toString());
        redisUtil.del(userCacheKey);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchReset(User operator, CuserBatchResetReq request) {

        UserResetRecord userResetRecord = new UserResetRecord();
        userResetRecord.setContent(request.getIdList().toString());
        userResetRecord.setCreater(operator.getId());
        userResetRecord.setCreateDatetime(new Date());

        cuserMapper.insertResetRecord(userResetRecord);

        for (Long id : request.getIdList()) {
            Cuser cuser = cuserMapper.selectByPrimaryKey(id);
            User user = userService.detail(cuser.getUserId());
            if (StringUtils.isBlank(user.getIdNo())) {
                continue;
            }
            String userCacheKey = String.format(RedisKeyList.MT_USER_INFO_KEY, cuser.getUserId().toString());
            redisUtil.del(userCacheKey);

            IdentifyOrder order = new IdentifyOrder();
            order.setId(IdGeneratorUtil.generator());
            order.setUserId(cuser.getUserId());
            order.setStatus(EIdentifyOrderStatus.IDENTIFY_ORDER_STATUS_6.getCode());
            order.setRealName(user.getRealName());
            order.setCreateDatetime(new Date());
            order.setUpdater(operator.getId());
            order.setUpdateDatetime(new Date());
            order.setRemark("平台重置实名记录");

            identifyOrderService.create(order);

            Map<String, String> faceConfigMap = configService.getConfigsMap(AUTH.FACE.getCode());
            Integer facePersonTime = Integer.valueOf(faceConfigMap.get(SysConstants.FACE_PERSON_TIME));
            int userIngCount = identifyOrderService.selectApplyIngCount(user.getId());
            if (facePersonTime > userIngCount) {
                user.setIdentifyStyle(EIdentifyStyle.FACE.getCode());
            } else {
                user.setIdentifyStyle(EIdentifyStyle.MANUAL.getCode());
            }
            user.setIdentifyStatus(EUserIdentifyStatus.IDENTIFY_0.getCode());
            cuserMapper.resetRealName(user);

            //删除token
            redisUtil.del(user.getId().toString());
            redisUtil.del(user.getId().toString());
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchModifyIdentifyStyle(User operator, CuserModifyIdentifyStyleReq request) {

        List<Long> userIdList = new ArrayList<>();
        for (Long id : request.getIdList()) {
            Cuser cuser = cuserMapper.selectByPrimaryKey(id);
            User user = userService.detail(cuser.getUserId());
            // 人工跳过
            if (EIdentifyStyle.MANUAL.getCode().equals(user.getIdentifyStyle())) {
                continue;
            }
            String userCacheKey = String.format(RedisKeyList.MT_USER_INFO_KEY, cuser.getUserId().toString());
            redisUtil.del(userCacheKey);

            userIdList.add(user.getId());
        }
        // 手动实名标识修改
        if (CollectionUtils.isNotEmpty(userIdList)) {
            userMapper.resetManualIdentifyStyle(userIdList);
        }
    }

    /**
     * 批量清退用户
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchRecovery(User operator, User user) {

        Account account = accountService.getAccount(user.getId(), ECurrency.CNY.getCode());
        Account diamondAccount = accountService.getAccount(user.getId(), ECurrency.DIAMOND.getCode());

        UserRecoveryRecord record = new UserRecoveryRecord();
        record.setUserId(user.getId());
        record.setCnyAmount(account.getAmount());
        record.setDiamondAmount(diamondAccount.getAmount());
        record.setCreater(operator.getId());
        record.setCreaterName(operator.getLoginName());
        record.setCreateDatetime(new Date());
        userRecoveryRecordService.create(record);

        BigDecimal changeAmount = account.getAmount();
        // 用户账户减去金额
        accountService.changeAmount(account, account.getAmount().negate(), EChannelType.INNER.getCode(),
                record.getId().toString(), record.getId(),
                EJourBizTypeUser.Recovery.Recovery,
                EJourBizTypeUser.Recovery.Recovery,
                EJourBizTypeUser.Recovery.Recovery);

        //清退账户加钱
        Account systemAccount = accountService.getAccountForUpdate(ESystemAccount.BIZ.RECOVERY.getAccountNumber());
        accountService.changeAmount(systemAccount, changeAmount,
                EChannelType.INNER.getCode(),
                record.getId().toString(), record.getId(),
                EJourBizTypeSystem.Recovery.Recovery,
                EJourBizTypeSystem.Recovery.Recovery,
                EJourBizTypeSystem.Recovery.Recovery);

        BigDecimal diamondChangeAmount = diamondAccount.getAmount();
        // 用户账户减去金额
        accountService.changeAmount(diamondAccount, diamondAccount.getAmount().negate(), EChannelType.INNER.getCode(),
                record.getId().toString(), record.getId(),
                EJourBizTypeUser.Recovery.Recovery,
                EJourBizTypeUser.Recovery.Recovery,
                EJourBizTypeUser.Recovery.Recovery);

        //清退账户加钱
        Account systemDiamondAccount = accountService.getAccountForUpdate(ESystemAccount.BIZ.RECOVERY_DIAMOND.getAccountNumber());
        accountService.changeAmount(systemDiamondAccount, diamondChangeAmount,
                EChannelType.INNER.getCode(),
                record.getId().toString(), record.getId(),
                EJourBizTypeSystem.Recovery.Recovery,
                EJourBizTypeSystem.Recovery.Recovery,
                EJourBizTypeSystem.Recovery.Recovery);

        // 清退藏品
        List<UserRecoveryCollection> recoveryCollectionList = new ArrayList<>();
        CollectionDetail condition = new CollectionDetail();
        condition.setOwnerType(ECollectionDetailOwnerType.CUSER.getCode());
        condition.setOwnerId(user.getId());
        List<CollectionDetail> list = collectionDetailService.list(condition);

        Map<Long, Collection> collectionMap = new HashMap<>();
        for (CollectionDetail collectionDetail : list) {
            collectionDetail = collectionDetailService.detailForUpdate(collectionDetail.getId());
            // 藏品可使用
            if (ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_0.getCode().equals(collectionDetail.getStatus())) {
                collectionDetail.setOwnerType(ECollectionDetailOwnerType.RECOVERY.getCode());
                collectionDetail.setOwnerId(100000000000000000L);
                collectionDetailService.modify(collectionDetail);

                //之前的藏品流转状态变更
                CollectionDetailRecord collectionDetailRecord = new CollectionDetailRecord();
                collectionDetailRecord.setCollectionDetailId(collectionDetail.getId());
                collectionDetailRecord.setStatus(EBoolean.NO.getCode());
                collectionDetailRecordService.modify(collectionDetailRecord);

                //藏品流转记录
                collectionDetailRecordService.create(collectionDetail.getId(), ECollectionDetailOwnerType.CUSER.getCode(), user.getId(),
                        ECollectionDetailOwnerType.RECOVERY.getCode(), 100000000000000000L, BigDecimal.ZERO,
                        ECollectionDetailRecordTradeType.COLLECTION_DETAIL_RECORD_TRADE_TYPE_20.getCode(),
                        record.getId());

                UserRecoveryCollection recoveryCollection = new UserRecoveryCollection();
                recoveryCollection.setRecoveryId(record.getId());
                recoveryCollection.setUserId(user.getId());
                recoveryCollection.setCollectionId(collectionDetail.getCollectionId());
                recoveryCollection.setCollectionDetailId(collectionDetail.getId());

                Collection collection = collectionMap.get(collectionDetail.getCollectionId());
                if (null == collection) {
                    collection = collectionService.detailSimple(collectionDetail.getCollectionId());
                }
                recoveryCollection.setCollectionName(collection.getName());

                recoveryCollectionList.add(recoveryCollection);
            }
        }

        if (CollectionUtils.isNotEmpty(recoveryCollectionList)) {
            userRecoveryCollectionService.batchCreate(recoveryCollectionList);
        }

        User modifyUser = new User();
        modifyUser.setId(user.getId());
        modifyUser.setRecoveryStatus(EUserRecoveryStatus.E_USER_RECOVERY_STATUS_1.getCode());
        userService.modifyUser(modifyUser);
    }

    @Override
    public UserHomeRes getUserUnRead(User operator) {
        String smsNotReadFlag = EBoolean.NO.getCode();
        String periodNotReadFlag = EBoolean.NO.getCode();

        if (operator == null) {
            return new UserHomeRes(smsNotReadFlag, periodNotReadFlag);
        }

        ChannelMerchant channelMerchant = channelMerchantService.detailPlatChannel();
        SmsUnreadMessagesRes myUnreadCount = smsReadService.getMyUnreadExists(operator, channelMerchant);
        if (myUnreadCount.getMyUnreadCount() > 0 || myUnreadCount.getMsgMyUnreadCount() > 0) {
            smsNotReadFlag = EBoolean.YES.getCode();
        }

        if (!periodUserReadService.doCheckRead(operator)) {
            periodNotReadFlag = EBoolean.YES.getCode();
        }

        return new UserHomeRes(smsNotReadFlag, periodNotReadFlag);
    }

    static int tokenExpirationInSeconds = 86400;//AccessToken2 从生成到过期的时间长度，单位为秒.AccessToken2 的最大有效期为 24 小时。 如果你将此参数设为超过 24 小时的时间，AccessToken2 有效期依然为 24 小时。如果你将此参数设为 0，AccessToken2 立即过期。
    static int privilegeExpirationInSeconds = 86400;//从 AccessToken2 生成到所有权限过期的时间长度，单位为秒.如果你将此参数设为 0（默认值），则权限永不过期。

    @Override
    public AgoraTokenRes getAgoraToken(AgoraTokenReq request, User operator) {
        RtcTokenBuilder2 token = new RtcTokenBuilder2();
        RtcTokenBuilder2.Role subscriber;
//        subscriber = RtcTokenBuilder2.Role.ROLE_PUBLISHER;
        if (EAgoraRole.E_AGORA_ROLE_1.getCode().equals(request.getRole())) {
            subscriber = RtcTokenBuilder2.Role.ROLE_PUBLISHER;
        } else {
            subscriber = RtcTokenBuilder2.Role.ROLE_SUBSCRIBER;
        }

        String result = token.buildTokenWithUid(agoraConfig.getAppId(), agoraConfig.getAppCertificate(), request.getChannelName(),
                Integer.valueOf(operator.getInviteNo().toString()),
                subscriber, tokenExpirationInSeconds, privilegeExpirationInSeconds);
        System.out.println("Token with uid: " + result);

        AgoraTokenRes res = new AgoraTokenRes();
        res.setToken(result);
        res.setChannelName(request.getChannelName());
        res.setInviteNo(Integer.valueOf(operator.getInviteNo().toString()));
        return res;
    }

    private List<Cuser> nextChannel(Long userId) {
        if (null == userId) {
            return null;
        }

        Cuser user = new Cuser();
        user.setIsChannel(EBoolean.YES.getCode());
        user.setChannelId(userId);
        user.setNodeLevelType(EUserNodeLevelType.DISTRIBUTION.getCode());
        return cuserMapper.select(user);
    }

    private String dealVolume(BigDecimal result) {
        if (new BigDecimal("10000").compareTo(result) < 0 && new BigDecimal("100000000").compareTo(result) > 0) {
            return result.divide(new BigDecimal("10000"), 2, BigDecimal.ROUND_DOWN).toPlainString().concat("万");
        } else if (new BigDecimal("100000000").compareTo(result) < 0) {
            return result.divide(new BigDecimal("100000000"), 2, BigDecimal.ROUND_DOWN).toPlainString().concat("亿");
        } else {
            return result.setScale(2, BigDecimal.ROUND_DOWN).toPlainString();
        }
    }


    private void cUserInsert(User user) {
        Cuser cuser = new Cuser();
        cuser.setUserId(user.getId());
        cuser.setMemberFlag(EBoolean.NO.getCode());
        cuser.setCreateDatetime(user.getRegisterDatetime());
        cuserMapper.insertSelective(cuser);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public User registerThirdPartyUser(ThirdPartyUserRegisterReq request) {
        // 检查用户是否已存在
        User condition = new User();
        condition.setChannelUid(request.getUnique_id());
        condition.setKind(EUserKind.C.getCode());

        List<User> existingUsers = userMapper.selectByCondition(condition);
        if (CollectionUtils.isNotEmpty(existingUsers)) {
            log.info("第三方用户已存在，unique_id: {}", request.getUnique_id());
            return existingUsers.get(0);
        }

        // 创建新用户
        User user = new User();
        Long userId = IdGeneratorUtil.generator();
        Date now = new Date();

        user.setId(userId);
        user.setKind(EUserKind.C.getCode());
        user.setChannelUid(request.getUnique_id()); // 使用channelUid字段存储unique_id

        // 设置用户名
        if (StringUtils.isNotBlank(request.getUsername())) {
            user.setUserName(request.getUsername());
            user.setNickname(request.getUsername());
        } else {
            // 如果没有用户名，使用ID后8位作为昵称
            String nickname = userId.toString().substring(userId.toString().length() - 8);
            user.setNickname(nickname);
        }

        // 设置手机号
        if (StringUtils.isNotBlank(request.getMobile())) {
            user.setMobile(request.getMobile());
            user.setLoginName(request.getMobile());
        } else {
            // 如果没有手机号，使用unique_id作为登录名
            user.setLoginName(request.getUnique_id());
        }

        // 设置头像
        if (StringUtils.isNotBlank(request.getAvatar())) {
            user.setPhoto(request.getAvatar());
        } else {
            // 设置默认头像
            user.setPhoto(configService.getStringValue("user_photo"));
        }

        // 设置第三方来源标识
        if (StringUtils.isNotBlank(request.getThird_party_source())) {
            user.setRemark(request.getThird_party_source()); // 使用remark字段存储第三方来源
        }

        // 设置默认密码（可以后续让用户修改）
        String defaultPassword = "123456";
        String hashedPassword = BCrypt.hashpw(defaultPassword, BCrypt.gensalt(12));
        user.setLoginPwd(hashedPassword);
        user.setLoginPwdStrength(PwdUtil.calculateSecurityLevel(defaultPassword));

        user.setRegisterDatetime(now);
        user.setLastLoginDatetime(now);
        user.setStatus(EUserStatus.NORMAL.getCode());
        user.setCompanyId(-1L);

        // 设置默认介绍
        user.setIntroduce(configService.getStringValue(SysConstants.USER_DEFAULT_INTRODUCE));

        // 设置渠道商户
        ChannelMerchant channelMerchant = channelMerchantService.detailPlatChannel();
        user.setChannelMerchantId(channelMerchant.getId());

        // 保存用户
        int result = userMapper.insertSelective(user);

        if (result > 0) {
            // 执行C端用户相关操作
            cUserInsert(user);

            // 添加默认权限
            userRoleService.allotRole(user, sysProperties.getDefaultRoleC());

            // 用户等级升级
            userNodeLevelService.upgradeAuto(user.getId(), EUserNodeLevel.LEVEL_0.getCode(), EUserNodeLevelType.MEMBER);

            log.info("第三方用户注册成功，userId: {}, unique_id: {}", userId, request.getUnique_id());
        }

        return user;
    }

}