package com.std.core.service.impl;

import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EBoolean;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.EMeetingRecordStatus;
import com.std.core.enums.EMeetingRecordType;
import com.std.core.mapper.MeetingPresentersMapper;
import com.std.core.pojo.domain.MeetingApplication;
import com.std.core.pojo.domain.MeetingDetail;
import com.std.core.pojo.domain.MeetingPresenters;
import com.std.core.pojo.domain.MeetingRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.MeetingPresentersCreateReq;
import com.std.core.pojo.request.MeetingPresentersListFrontReq;
import com.std.core.pojo.request.MeetingPresentersListReq;
import com.std.core.pojo.request.MeetingPresentersModifyReq;
import com.std.core.pojo.request.MeetingPresentersPageFrontReq;
import com.std.core.pojo.request.MeetingPresentersPageReq;
import com.std.core.pojo.response.MeetingPresentersDetailRes;
import com.std.core.pojo.response.MeetingPresentersListOssRes;
import com.std.core.pojo.response.MeetingPresentersListRes;
import com.std.core.pojo.response.MeetingPresentersPageRes;
import com.std.core.service.IConfigService;
import com.std.core.service.IMeetingApplicationService;
import com.std.core.service.IMeetingDetailService;
import com.std.core.service.IMeetingPresentersService;
import com.std.core.service.IMeetingRecordService;
import com.std.core.service.IUserService;

import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * 会议直播主播ServiceImpl
 *
 * <AUTHOR> wzh
 * @since : 2023-06-01 18:43
 */
@Service
public class MeetingPresentersServiceImpl implements IMeetingPresentersService {

    @Resource
    private MeetingPresentersMapper meetingPresentersMapper;

    @Resource
    private IMeetingApplicationService meetingApplicationService;

    @Resource
    private IMeetingRecordService meetingRecordService;

    @Resource
    private IMeetingDetailService meetingDetailService;

    @Resource
    private IUserService userService;

    @Resource
    private IConfigService configService;

    /**
     * 新增会议直播主播
     *
     * @param req      新增会议直播主播入参
     * @param operator 操作人
     */
    @Override
    public void create(MeetingPresentersCreateReq req, User operator) {
        MeetingPresenters meetingPresenters = EntityUtils.copyData(req, MeetingPresenters.class);
        meetingPresentersMapper.insertSelective(meetingPresenters);
    }

    @Override
    public void batchCreate(List<MeetingPresenters> presentersList, Long meetingId) {
        meetingPresentersMapper.deleteByMeetingId(meetingId);
        if (CollectionUtils.isEmpty(presentersList)) {
            return;
        }
        meetingPresentersMapper.insertBatchSelective(presentersList);
    }

    /**
     * 删除会议直播主播
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        meetingPresentersMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改会议直播主播
     *
     * @param req      修改会议直播主播入参
     * @param operator 操作人
     */
    @Override
    public void modify(MeetingPresentersModifyReq req, User operator) {
        MeetingPresenters meetingPresenters = EntityUtils.copyData(req, MeetingPresenters.class);
        meetingPresentersMapper.updateByPrimaryKeySelective(meetingPresenters);
    }

    /**
     * 详情查询会议直播主播
     *
     * @param id 主键ID
     * @return 会议直播主播对象
     */
    @Override
    public MeetingPresenters detail(Long id) {
        MeetingPresenters meetingPresenters = meetingPresentersMapper.selectByPrimaryKey(id);
        if (null == meetingPresenters) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        // 转译UserId
        meetingPresenters.setUser(userService.selectSummaryInfo(meetingPresenters.getUserId()));

        return meetingPresenters;
    }

    /**
     * 分页查询会议直播主播
     *
     * @param req 分页查询会议直播主播入参
     * @return 分页会议直播主播对象
     */
    @Override
    public List<MeetingPresenters> page(MeetingPresentersPageReq req) {
        MeetingPresenters condition = EntityUtils.copyData(req, MeetingPresenters.class);

        List<MeetingPresenters> meetingPresentersList = meetingPresentersMapper.selectByCondition(condition);
        // 转译UserId
        meetingPresentersList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        return meetingPresentersList;
    }

    /**
     * 列表查询会议直播主播
     *
     * @param req 列表查询会议直播主播入参
     * @return 列表会议直播主播对象
     */
    @Override
    public List<MeetingPresenters> list(MeetingPresentersListReq req) {
        MeetingPresenters condition = EntityUtils.copyData(req, MeetingPresenters.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), MeetingPresenters.class));

        List<MeetingPresenters> meetingPresentersList = meetingPresentersMapper.selectByCondition(condition);
        // 转译UserId
        meetingPresentersList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        return meetingPresentersList;
    }

    @Override
    public List<MeetingPresenters> list(MeetingPresenters req) {

        List<MeetingPresenters> meetingPresentersList = meetingPresentersMapper.selectByCondition(req);

        return meetingPresentersList;
    }

    @Override
    public Integer selectCount(Long meetingId, Long userId) {
        if (null == meetingId || null == userId) {
            return 0;
        }

        MeetingPresenters condition = new MeetingPresenters(meetingId, userId);
        return meetingPresentersMapper.selectCount(condition);
    }

    /**
     * 前端详情查询会议直播主播
     *
     * @param id 主键ID
     * @return 会议直播主播对象
     */
    @Override
    public MeetingPresentersDetailRes detailFront(Long id) {
        MeetingPresentersDetailRes res = new MeetingPresentersDetailRes();

        MeetingPresenters meetingPresenters = meetingPresentersMapper.selectByPrimaryKey(id);
        if (null == meetingPresenters) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        // 转译UserId
        meetingPresenters.setUser(userService.selectSummaryInfo(meetingPresenters.getUserId()));

        BeanUtils.copyProperties(meetingPresenters, res);

        return res;
    }

    /**
     * 前端分页查询会议直播主播
     *
     * @param req 前端分页查询会议直播主播入参
     * @return 分页会议直播主播对象
     */
    @Override
    public List<MeetingPresentersPageRes> pageFront(MeetingPresentersPageFrontReq req) {
        MeetingPresenters condition = EntityUtils.copyData(req, MeetingPresenters.class);
        List<MeetingPresenters> meetingPresentersList = meetingPresentersMapper.selectByCondition(condition);
        // 转译UserId
        meetingPresentersList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        List<MeetingPresentersPageRes> resList = meetingPresentersList.stream().map((entity) -> {
            MeetingPresentersPageRes res = new MeetingPresentersPageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(meetingPresentersList, resList);
    }

    /**
     * 前端列表查询会议直播主播
     *
     * @return 列表会议直播主播对象
     */
    @Override
    public List<MeetingPresentersListRes> listFront(Long meetingId, User operator) {


        List<MeetingPresentersListRes> meetingPresentersListRes = listFront(meetingId);

        meetingPresentersListRes.forEach(x -> {
            x.setMyFlag(EBoolean.NO.getCode());
            if (operator.getId().equals(x.getUserId())) {
                x.setMyFlag(EBoolean.YES.getCode());
            }
        });

        return meetingPresentersListRes;
    }

    @Override
    public List<MeetingPresentersListRes> listFront(Long meetingId) {

        MeetingApplication meetingApplication = meetingApplicationService.detail(meetingId);

        MeetingDetail meetingDetail = meetingDetailService.detailNowUser(meetingApplication.getId());

        MeetingRecord meetingRecord = new MeetingRecord();
        meetingRecord.setMeetingId(meetingApplication.getId());
        meetingRecord.setParticipantType(EMeetingRecordType.E_MEETING_RECORD_TYPE_1.getCode());
        meetingRecord.setStatus(EMeetingRecordStatus.MEETING_RECORD_STATUS_0.getCode());
        List<MeetingRecord> list = meetingRecordService.list(meetingRecord);
        List<MeetingPresentersListRes> resList = list.stream().map((entity) -> {
            MeetingPresentersListRes res = new MeetingPresentersListRes();
            User user = userService.detailSimpleInfo(entity.getParticipantId());
            BeanUtils.copyProperties(entity, res);
            res.setUserId(user.getId());
            res.setPhoto(user.getPhoto());
            res.setInviteNo(user.getInviteNo());

            if (StringUtils.isBlank(res.getPhoto())) {
                res.setPhoto(configService.getStringValue("user_photo"));
            }
            res.setNickname(user.getNickname());

            if (StringUtils.isBlank(res.getNickname())) {
                res.setNickname(user.getRealName());
            }

            res.setAdminFlag(EBoolean.NO.getCode());
            if (meetingApplication.getUserMobile().equals(user.getMobile())) {
                res.setAdminFlag(EBoolean.YES.getCode());
            }

            res.setStatus(EBoolean.NO.getCode());
            if (null != meetingDetail && meetingDetail.getUserId().equals(entity.getParticipantId())) {
                res.setStatus(EBoolean.YES.getCode());
            }

//            res.setMyFlag(EBoolean.NO.getCode());
//            if (operator.getId().equals(entity.getParticipantId())) {
//                res.setMyFlag(EBoolean.YES.getCode());
//            }

            return res;
        }).collect(Collectors.toList());

        return resList;
    }

    @Override
    public List<MeetingPresentersListOssRes> listFront(List<MeetingPresentersListRes> meetingPresentersListRes, Long userId) {
        List<MeetingPresentersListRes> resList = meetingPresentersListRes;

        List<MeetingPresentersListOssRes> collect = resList.stream().map(x -> {
            MeetingPresentersListOssRes res = new MeetingPresentersListOssRes();
            BeanUtils.copyProperties(x, res);

            res.setMeetingId(x.getMeetingId().toString());
            res.setUserId(x.getUserId().toString());
            res.setInviteNo(x.getInviteNo().toString());
            res.setMyFlag(EBoolean.NO.getCode());
            if (userId.equals(x.getUserId())) {
                res.setMyFlag(EBoolean.YES.getCode());
            }

            return res;
        }).collect(Collectors.toList());


        return collect;
    }

    @Override
    public List<MeetingPresenters> detailByMeeting(Long meetingId) {
        MeetingPresenters condition = new MeetingPresenters();
        condition.setMeetingId(meetingId);

        List<MeetingPresenters> meetingPresentersList = meetingPresentersMapper.selectByCondition(condition);
        return meetingPresentersList;
    }

}