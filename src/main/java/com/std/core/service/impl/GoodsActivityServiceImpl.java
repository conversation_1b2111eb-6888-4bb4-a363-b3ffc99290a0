package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.EGoodsActivityStatus;
import com.std.core.mapper.GoodsActivityMapper;
import com.std.core.pojo.domain.GoodsActivity;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.GoodsActivityCreateReq;
import com.std.core.pojo.request.GoodsActivityListReq;
import com.std.core.pojo.request.GoodsActivityListFrontReq;
import com.std.core.pojo.request.GoodsActivityModifyReq;
import com.std.core.pojo.request.GoodsActivityPageReq;
import com.std.core.pojo.request.GoodsActivityPageFrontReq;
import com.std.core.pojo.response.GoodsActivityDetailRes;
import com.std.core.pojo.response.GoodsActivityListRes;
import com.std.core.pojo.response.GoodsActivityPageRes;
import com.std.core.service.IGoodsActivityService;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* 赋能商城活动ServiceImpl
*
* <AUTHOR> ycj
* @since : 2022-05-05 14:45
*/
@Service
public class GoodsActivityServiceImpl implements IGoodsActivityService {

    @Resource
    private GoodsActivityMapper goodsActivityMapper;

    /**
     * 新增赋能商城活动
     *
     * @param req 新增赋能商城活动入参
     * @param operator 操作人
     */
    @Override
    public void create(GoodsActivityCreateReq req, User operator) {
        GoodsActivity goodsActivity = EntityUtils.copyData(req, GoodsActivity.class);
        goodsActivityMapper.insertSelective(goodsActivity);
    }

    /**
     * 删除赋能商城活动
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        goodsActivityMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改赋能商城活动
     *
     * @param req 修改赋能商城活动入参
     * @param operator 操作人
     */
    @Override
    public void modify(GoodsActivityModifyReq req, User operator) {
        GoodsActivity goodsActivity = EntityUtils.copyData(req, GoodsActivity.class);

        if (goodsActivity.getIntegralDoubleStartDatetime().after(goodsActivity.getIntegralDoubleEndDatetime())){
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),"翻倍开始时间不能晚于结束时间");
        }
        goodsActivityMapper.updateByPrimaryKeySelective(goodsActivity);
    }

    @Override
    public void modify(GoodsActivity req) {
        goodsActivityMapper.updateByPrimaryKeySelective(req);

    }

    /**
     * 详情查询赋能商城活动
     *
     * @param id 主键ID
     * @return 赋能商城活动对象
     */
    @Override
    public GoodsActivity detail(Long id) {
        GoodsActivity goodsActivity = goodsActivityMapper.selectByPrimaryKey(id);
        if (null == goodsActivity) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return goodsActivity;
    }

    @Override
    public GoodsActivity detail() {
        GoodsActivity condition = new GoodsActivity();

        List<GoodsActivity> goodsActivityList = goodsActivityMapper.selectByCondition(condition);

        if (CollectionUtils.isNotEmpty(goodsActivityList)){
            return goodsActivityList.get(0);
        }
        return null;
    }

    /**
     * 分页查询赋能商城活动
     *
     * @param req 分页查询赋能商城活动入参
     * @return 分页赋能商城活动对象
     */
    @Override
    public List<GoodsActivity> page(GoodsActivityPageReq req) {
        GoodsActivity condition = EntityUtils.copyData(req, GoodsActivity.class);

        List<GoodsActivity> goodsActivityList = goodsActivityMapper.selectByCondition(condition);

        return goodsActivityList;
    }

    /**
     * 列表查询赋能商城活动
     *
     * @param req 列表查询赋能商城活动入参
     * @return 列表赋能商城活动对象
     */
    @Override
    public List<GoodsActivity> list(GoodsActivityListReq req) {
        GoodsActivity condition = EntityUtils.copyData(req, GoodsActivity.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), GoodsActivity.class));

        List<GoodsActivity> goodsActivityList = goodsActivityMapper.selectByCondition(condition);

        return goodsActivityList;
    }

    /**
     * 前端详情查询赋能商城活动
     *
     * @param id 主键ID
     * @return 赋能商城活动对象
     */
    @Override
    public GoodsActivityDetailRes detailFront(Long id) {
        GoodsActivityDetailRes res = new GoodsActivityDetailRes();

        GoodsActivity goodsActivity = goodsActivityMapper.selectByPrimaryKey(id);
        if (null == goodsActivity) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        BeanUtils.copyProperties(goodsActivity, res);

        return res;
    }

    /**
     * 前端分页查询赋能商城活动
     *
     * @param req 前端分页查询赋能商城活动入参
     * @return 分页赋能商城活动对象
     */
    @Override
    public List< GoodsActivityPageRes> pageFront(GoodsActivityPageFrontReq req) {
        GoodsActivity condition = EntityUtils.copyData(req, GoodsActivity.class);
        List<GoodsActivity> goodsActivityList = goodsActivityMapper.selectByCondition(condition);

        List< GoodsActivityPageRes> resList = goodsActivityList.stream().map((entity) -> {
            GoodsActivityPageRes res = new GoodsActivityPageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(goodsActivityList, resList);
    }

    /**
     * 前端列表查询赋能商城活动
     *
     * @param req 前端列表查询赋能商城活动入参
     * @return 列表赋能商城活动对象
     */
    @Override
    public List< GoodsActivityListRes> listFront(GoodsActivityListFrontReq req) {
        GoodsActivity condition = EntityUtils.copyData(req, GoodsActivity.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), GoodsActivity.class));

        List<GoodsActivity> goodsActivityList = goodsActivityMapper.selectByCondition(condition);

        List< GoodsActivityListRes> resList = goodsActivityList.stream().map((entity) -> {
            GoodsActivityListRes res = new GoodsActivityListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

}