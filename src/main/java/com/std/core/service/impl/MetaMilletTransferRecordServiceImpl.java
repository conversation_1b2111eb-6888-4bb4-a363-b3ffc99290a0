package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.*;
import com.std.core.mapper.MetaMilletTransferRecordMapper;
import com.std.core.pojo.domain.Account;
import com.std.core.pojo.domain.MetaMilletTransferDetail;
import com.std.core.pojo.domain.MetaMilletTransferRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.MetaMilletTransferRecordDetailRes;
import com.std.core.pojo.response.MetaMilletTransferRecordListRes;
import com.std.core.pojo.response.MetaMilletTransferRecordPageRes;
import com.std.core.service.IAccountService;
import com.std.core.service.IMetaMilletTransferDetailService;
import com.std.core.service.IMetaMilletTransferRecordService;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.std.core.service.IUserService;
import com.std.core.util.IdGeneratorUtil;
import com.std.core.util.InviteCodeUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 元粟转赠记录ServiceImpl
 *
 * <AUTHOR> ycj
 * @since : 2022-11-11 14:42
 */
@Service
public class MetaMilletTransferRecordServiceImpl implements IMetaMilletTransferRecordService {

    @Resource
    private MetaMilletTransferRecordMapper metaMilletTransferRecordMapper;

    @Resource
    private IUserService userService;

    @Resource
    private IAccountService accountService;

    @Resource
    private IMetaMilletTransferDetailService metaMilletTransferDetailService;

    /**
     * 新增元粟转赠记录
     *
     * @param req      新增元粟转赠记录入参
     * @param operator 操作人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(MetaMilletTransferRecordCreateReq req, User operator) {
        // 验证信息
        User toUser = checkCondition(req, operator);
        userService.checkTradePwd(operator.getId(), req.getPwd());

        Long recordId = IdGeneratorUtil.generator();
        List<MetaMilletTransferDetail> detailList = new ArrayList<>();
        Date date = new Date();
        BigDecimal quantity = BigDecimal.ZERO;
        List<String> currencyList = new ArrayList<>();
        for (MetaMilletTransferDetailCreateReq transferDetailCreateReq : req.getTransferMilletList()) {
            EYaoMilletConfigType millet = null;
            try {
                millet = EYaoMilletConfigType.getYaoMilletConfigCurrency(transferDetailCreateReq.getCurrency());
            } catch (Exception e) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "原粟种类错误");
            }

            if (currencyList.contains(transferDetailCreateReq.getCurrency())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), millet.getValue() + "原粟重复");
            }

            Account fromAccount = accountService.getAccount(operator.getId(), transferDetailCreateReq.getCurrency());
            if (transferDetailCreateReq.getQuantity().compareTo(fromAccount.getAvailableAmount()) > 0) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), millet.getValue() + "账户余额不足");
            }


            accountService.changeAmount(fromAccount, transferDetailCreateReq.getQuantity().negate(), EChannelType.INNER.getCode(), recordId.toString(), recordId, EJourBizTypeUser.MilletAccount.Transfer,
                    EJourBizTypeUser.MilletAccount.UserToUserTransferTo, EJourBizTypeUser.MilletAccount.UserToUserTransferTo, userService.dealUser(toUser).getLoginName());

            Account toAccount = accountService.getAccount(toUser.getId(), transferDetailCreateReq.getCurrency());
            accountService.changeAmount(toAccount, transferDetailCreateReq.getQuantity(), EChannelType.INNER.getCode(), recordId.toString(), recordId, EJourBizTypeUser.MilletAccount.Transfer,
                    EJourBizTypeUser.MilletAccount.UserToUserTransferFrom, EJourBizTypeUser.MilletAccount.UserToUserTransferFrom, userService.dealUser(operator).getLoginName());

            MetaMilletTransferDetail transferDetail = new MetaMilletTransferDetail();
            transferDetail.setTransferRecordId(recordId);
            transferDetail.setCurrency(transferDetailCreateReq.getCurrency());
            transferDetail.setMilletType(millet.getCode());
            transferDetail.setQuantity(transferDetailCreateReq.getQuantity());
            transferDetail.setCreateDatetime(date);

            detailList.add(transferDetail);

            quantity = quantity.add(transferDetailCreateReq.getQuantity());
        }

        // 批量插入转赠明细
        metaMilletTransferDetailService.batchCreate(detailList);

        MetaMilletTransferRecord metaMilletTransferRecord = new MetaMilletTransferRecord();
        metaMilletTransferRecord.setId(recordId);
        metaMilletTransferRecord.setFromUserId(operator.getId());
        metaMilletTransferRecord.setToUserId(toUser.getId());
        metaMilletTransferRecord.setQuantity(quantity);
        metaMilletTransferRecord.setKeyword(req.getLoginName());
        metaMilletTransferRecord.setCreateDatetime(date);
        metaMilletTransferRecordMapper.insertSelective(metaMilletTransferRecord);
    }

    private User checkCondition(MetaMilletTransferRecordCreateReq req, User operator) {
        //需要实名认证
        if (StringUtils.isBlank(operator.getRealName()) || StringUtils.isBlank(operator.getIdNo())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请先实名认证");
        }

        //手机或者邀请码转赠
        User toUser = userService.selectUserByMobile(req.getLoginName(), EUserKind.C.getCode());
        if (null == toUser) {
            if (req.getLoginName().length() < 6) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "用户ID格式错误,请检查");
            }

            toUser = userService.getUserAndCheckByInviteCode(req.getLoginName());
            String inviteCode = InviteCodeUtil.toSerialCode(toUser.getInviteNo());
            if (!inviteCode.equals(req.getLoginName())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "被转赠人邀请码不对,请检查");
            }

            if (null == toUser) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "被转赠人不存在");
            }
        } else if (operator.getId().equals(toUser.getId())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "不可转赠给本人！");
        }
        //转赠人状态判断
        if (!EUserStatus.NORMAL.getCode().equals(toUser.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "被转赠人状态异常，无法转赠");
        }

        // 检查转赠人是否开启原粟账户
        Account account = accountService.getAccount(toUser.getId(), ECurrency.DIAMOND.getCode());
        if (BigDecimal.ZERO.compareTo(account.getAvailableAmount()) < 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "转赠人还未开通原粟账户，不可转赠");
        }

        return toUser;
    }

    /**
     * 删除元粟转赠记录
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        metaMilletTransferRecordMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改元粟转赠记录
     *
     * @param req      修改元粟转赠记录入参
     * @param operator 操作人
     */
    @Override
    public void modify(MetaMilletTransferRecordModifyReq req, User operator) {
        MetaMilletTransferRecord metaMilletTransferRecord = EntityUtils.copyData(req, MetaMilletTransferRecord.class);
        metaMilletTransferRecordMapper.updateByPrimaryKeySelective(metaMilletTransferRecord);
    }

    /**
     * 详情查询元粟转赠记录
     *
     * @param id 主键ID
     * @return 元粟转赠记录对象
     */
    @Override
    public MetaMilletTransferRecord detail(Long id) {
        MetaMilletTransferRecord metaMilletTransferRecord = metaMilletTransferRecordMapper.selectByPrimaryKey(id);
        if (null == metaMilletTransferRecord) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        metaMilletTransferRecord.setFromUser(userService.selectSummaryInfo(metaMilletTransferRecord.getFromUserId()));
        metaMilletTransferRecord.setToUser(userService.selectSummaryInfo(metaMilletTransferRecord.getToUserId()));

        metaMilletTransferRecord.setMetaMilletTransferDetailList(metaMilletTransferDetailService.detailByRecord(metaMilletTransferRecord.getId()));

        return metaMilletTransferRecord;
    }

    /**
     * 分页查询元粟转赠记录
     *
     * @param req 分页查询元粟转赠记录入参
     * @return 分页元粟转赠记录对象
     */
    @Override
    public List<MetaMilletTransferRecord> page(MetaMilletTransferRecordPageReq req) {
        MetaMilletTransferRecord condition = EntityUtils.copyData(req, MetaMilletTransferRecord.class);

        List<MetaMilletTransferRecord> metaMilletTransferRecordList = metaMilletTransferRecordMapper.selectByCondition(condition);

        metaMilletTransferRecordList.forEach(x -> {
            x.setFromUser(userService.selectSummaryInfo(x.getFromUserId()));
            x.setToUser(userService.selectSummaryInfo(x.getToUserId()));
        });
        return metaMilletTransferRecordList;
    }

    /**
     * 列表查询元粟转赠记录
     *
     * @param req 列表查询元粟转赠记录入参
     * @return 列表元粟转赠记录对象
     */
    @Override
    public List<MetaMilletTransferRecord> list(MetaMilletTransferRecordListReq req) {
        MetaMilletTransferRecord condition = EntityUtils.copyData(req, MetaMilletTransferRecord.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), MetaMilletTransferRecord.class));

        List<MetaMilletTransferRecord> metaMilletTransferRecordList = metaMilletTransferRecordMapper.selectByCondition(condition);

        return metaMilletTransferRecordList;
    }

    /**
     * 前端详情查询元粟转赠记录
     *
     * @param id 主键ID
     * @return 元粟转赠记录对象
     */
    @Override
    public MetaMilletTransferRecordDetailRes detailFront(Long id) {
        MetaMilletTransferRecordDetailRes res = new MetaMilletTransferRecordDetailRes();

        MetaMilletTransferRecord metaMilletTransferRecord = metaMilletTransferRecordMapper.selectByPrimaryKey(id);
        if (null == metaMilletTransferRecord) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        BeanUtils.copyProperties(metaMilletTransferRecord, res);

        return res;
    }

    /**
     * 前端分页查询元粟转赠记录
     *
     * @param req 前端分页查询元粟转赠记录入参
     * @return 分页元粟转赠记录对象
     */
    @Override
    public List<MetaMilletTransferRecordPageRes> pageFront(MetaMilletTransferRecordPageFrontReq req) {
        MetaMilletTransferRecord condition = EntityUtils.copyData(req, MetaMilletTransferRecord.class);
        List<MetaMilletTransferRecord> metaMilletTransferRecordList = metaMilletTransferRecordMapper.selectByCondition(condition);

        List<MetaMilletTransferRecordPageRes> resList = metaMilletTransferRecordList.stream().map((entity) -> {
            MetaMilletTransferRecordPageRes res = new MetaMilletTransferRecordPageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(metaMilletTransferRecordList, resList);
    }

    /**
     * 前端列表查询元粟转赠记录
     *
     * @param req 前端列表查询元粟转赠记录入参
     * @return 列表元粟转赠记录对象
     */
    @Override
    public List<MetaMilletTransferRecordListRes> listFront(MetaMilletTransferRecordListFrontReq req) {
        MetaMilletTransferRecord condition = EntityUtils.copyData(req, MetaMilletTransferRecord.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), MetaMilletTransferRecord.class));

        List<MetaMilletTransferRecord> metaMilletTransferRecordList = metaMilletTransferRecordMapper.selectByCondition(condition);

        List<MetaMilletTransferRecordListRes> resList = metaMilletTransferRecordList.stream().map((entity) -> {
            MetaMilletTransferRecordListRes res = new MetaMilletTransferRecordListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

}