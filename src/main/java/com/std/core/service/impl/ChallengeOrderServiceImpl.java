package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.DateUtil;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EBoolean;
import com.std.core.enums.EChallengeCollectionBackType;
import com.std.core.enums.EChallengeDistributionType;
import com.std.core.enums.EChallengeHistoryType;
import com.std.core.enums.EChallengeOrderStatus;
import com.std.core.enums.EChallengeStartStatus;
import com.std.core.enums.EChallengeType;
import com.std.core.enums.ECollectionDetailBuyChannel;
import com.std.core.enums.ECollectionDetailRecordTradeType;
import com.std.core.enums.ECollectionDetailRefType;
import com.std.core.enums.ECollectionDetailStatus;
import com.std.core.enums.ECollectionPeriodCategory;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.EUserKind;
import com.std.core.mapper.ChallengeOrderMapper;
import com.std.core.pojo.domain.Address;
import com.std.core.pojo.domain.Challenge;
import com.std.core.pojo.domain.ChallengeOrder;
import com.std.core.pojo.domain.ChallengeOrderDetail;
import com.std.core.pojo.domain.Collection;
import com.std.core.pojo.domain.CollectionDetail;
import com.std.core.pojo.domain.Company;
import com.std.core.pojo.domain.CompanyEntity;
import com.std.core.pojo.domain.ContractToken;
import com.std.core.pojo.domain.Express;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ChallengeDeliveryReq;
import com.std.core.pojo.request.ChallengeOrderBatchDefaultReq;
import com.std.core.pojo.request.ChallengeOrderCreateReq;
import com.std.core.pojo.request.ChallengeOrderListFrontReq;
import com.std.core.pojo.request.ChallengeOrderListReq;
import com.std.core.pojo.request.ChallengeOrderModifyReq;
import com.std.core.pojo.request.ChallengeOrderPageFrontReq;
import com.std.core.pojo.request.ChallengeOrderPageReq;
import com.std.core.pojo.response.ChallengeDetailAwardRes;
import com.std.core.pojo.response.ChallengeDetailEntityRes;
import com.std.core.pojo.response.ChallengeDetailRes;
import com.std.core.pojo.response.ChallengeOrderDetailRes;
import com.std.core.pojo.response.ChallengeOrderExchangeDetailRes;
import com.std.core.pojo.response.ChallengeOrderListRes;
import com.std.core.pojo.response.ChallengeOrderPageRes;
import com.std.core.service.IAddressService;
import com.std.core.service.IChallengeConditionService;
import com.std.core.service.IChallengeOrderDetailService;
import com.std.core.service.IChallengeOrderService;
import com.std.core.service.IChallengeService;
import com.std.core.service.IChannelMerchantService;
import com.std.core.service.ICollectionDetailRecordService;
import com.std.core.service.ICollectionDetailService;
import com.std.core.service.ICollectionService;
import com.std.core.service.ICompanyChannelService;
import com.std.core.service.ICompanyEntityService;
import com.std.core.service.ICompanyService;
import com.std.core.service.IExpressService;
import com.std.core.service.IUserService;
import com.vdurmont.emoji.EmojiParser;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 挑战兑换订单ServiceImpl
 *
 * <AUTHOR> ycj
 * @since : 2021-12-28 16:27
 */
@Service
public class ChallengeOrderServiceImpl implements IChallengeOrderService {

    @Resource
    private ChallengeOrderMapper challengeOrderMapper;

    @Resource
    private IUserService userService;

    @Resource
    private IAddressService addressService;

    @Resource
    private ICompanyEntityService companyEntityService;

    @Resource
    private ICompanyService companyService;

    @Resource
    private ICollectionService collectionService;

    @Resource
    private IChallengeService challengeService;

    @Resource
    private ICollectionDetailRecordService collectionDetailRecordService;

    @Resource
    private ICollectionDetailService collectionDetailService;

    @Resource
    private IExpressService expressService;

    @Resource
    private IChallengeOrderDetailService challengeOrderDetailService;

    @Resource
    private IChallengeConditionService challengeConditionService;

    @Resource
    private IChannelMerchantService channelMerchantService;

    @Resource
    private ICompanyChannelService companyChannelService;

    /**
     * 新增挑战兑换订单
     *
     * @param req 新增挑战兑换订单入参
     * @param operator 操作人
     */
    @Override
    public void create(ChallengeOrderCreateReq req, User operator) {
        ChallengeOrder challengeOrder = EntityUtils.copyData(req, ChallengeOrder.class);
        challengeOrderMapper.insertSelective(challengeOrder);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(Challenge challenge, User user, Long addressId) {
        ChallengeOrder challengeOrder = new ChallengeOrder();
        challengeOrder.setChallengeId(challenge.getId());
        challengeOrder.setChallengeType(challenge.getType());
        challengeOrder.setCompanyId(challenge.getCompanyId());
        challengeOrder.setUserId(user.getId());
        challengeOrder.setCreateTime(new Date());
        challengeOrder.setStatus(EChallengeOrderStatus.CHALLENGE_ORDER_STATUS_1.getCode());
        challengeOrder.setChannelId(challenge.getChannelId());

        // 实物要落地收货地址
        if (EChallengeType.CHALLENGE_TYPE_1.getCode().equals(challenge.getType())) {
            challengeOrder.setStatus(EChallengeOrderStatus.CHALLENGE_ORDER_STATUS_0.getCode());
            Address address = addressService.detail(addressId);
            CompanyEntity companyEntity = companyEntityService.detail(challenge.getAwardRefId());
            challengeOrder.setAwardRefId(challenge.getAwardRefId());
            challengeOrder.setAwardName(companyEntity.getName());
            challengeOrder.setAwardPic(companyEntity.getImageUrl());
            challengeOrder.setReceiver(address.getName());
            challengeOrder.setReMobile(address.getPhone());
            challengeOrder.setReAddress(address.getProvince() + address.getCity() + address.getCounty() + address.getAddress());
        } else if (EChallengeType.CHALLENGE_TYPE_0.getCode().equals(challenge.getType())) {
            Collection collection = collectionService.detail(challenge.getAwardRefId());
            challengeOrder.setAwardRefId(challenge.getAwardRefId());
            challengeOrder.setAwardName(collection.getName());
            challengeOrder.setAwardPic(collection.getCoverFileUrl());
            challengeOrder.setIsDistribution(EChallengeDistributionType.CHALLENGE_TYPE_1.getCode());
        }
        challengeOrderMapper.insertSelective(challengeOrder);
        return challengeOrder.getId();
    }

    /**
     * 删除挑战兑换订单
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        challengeOrderMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改挑战兑换订单
     *
     * @param req 修改挑战兑换订单入参
     * @param operator 操作人
     */
    @Override
    public void modify(ChallengeOrderModifyReq req, User operator) {
        ChallengeOrder challengeOrder = EntityUtils.copyData(req, ChallengeOrder.class);
        challengeOrderMapper.updateByPrimaryKeySelective(challengeOrder);
    }

    @Override
    public void modify(ChallengeOrder req) {
        challengeOrderMapper.updateByPrimaryKeySelective(req);
    }

    /**
     * 详情查询挑战兑换订单
     *
     * @param id 主键ID
     * @return 挑战兑换订单对象
     */
    @Override
    public ChallengeOrder detailOss(Long id, User operator) {
        ChallengeOrder challengeOrder = challengeOrderMapper.selectByPrimaryKeyOss(id);
        if (null == challengeOrder) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        initOrder(challengeOrder);
        challengeOrder.setOrderDetailList(challengeOrderDetailService.selectByOrderId(challengeOrder.getId()));

        return challengeOrder;
    }

    @Override
    public ChallengeOrder detailSimple(Long id) {
        ChallengeOrder challengeOrder = challengeOrderMapper.selectByPrimaryKeyOss(id);
        if (null == challengeOrder) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return challengeOrder;
    }

    @Override
    public ChallengeOrder detail(Long challengeId, User operator) {
        if (null == operator) {
            return null;
        }
        ChallengeOrder condition = new ChallengeOrder();
        condition.setChallengeId(challengeId);
        condition.setUserId(operator.getId());

        List<ChallengeOrder> challengeOrderList = challengeOrderMapper.selectByCondition(condition);
        if (CollectionUtils.isNotEmpty(challengeOrderList)) {
            return challengeOrderList.get(0);
        }
        return null;
    }

    public ChallengeDetailEntityRes detailEntity(Long id) {
        ChallengeDetailEntityRes res = new ChallengeDetailEntityRes();

        ChallengeOrder challengeOrder = challengeOrderMapper.selectByPrimaryKey(id);
        if (null == challengeOrder) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "还没有兑换订单");
        }
        BeanUtils.copyProperties(challengeOrder, res);

        Express express = expressService.detailByKdnCode(challengeOrder.getLogisticsCompany());

        if (null == express) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "快递物流错误");
        }
        res.setLogisticsCompany(express.getName());
        res.setName(challengeOrder.getAwardName());
        res.setCoverPicUrl(challengeOrder.getAwardPic());
        return res;
    }

    /**
     * 分页查询挑战兑换订单
     *
     * @param req 分页查询挑战兑换订单入参
     * @return 分页挑战兑换订单对象
     */
    @Override
    public List<ChallengeOrder> page(ChallengeOrderPageReq req, User operator) {
        ChallengeOrder condition = EntityUtils.copyData(req, ChallengeOrder.class);

        if (EUserKind.BP.getCode().equals(operator.getKind())) {
            if (null != req.getChannelId()) {
                companyChannelService.checkPermission(operator, req.getChannelId());
            }

            condition.setCompanyId(operator.getCompanyId());
        }

        condition.setOrderBy("t.id desc");

        List<ChallengeOrder> challengeOrderList = challengeOrderMapper.selectByConditionOss(condition);
        // 转译UserId
        challengeOrderList.forEach(item -> {
            initOrder(item);
        });

        return challengeOrderList;
    }

    private void initOrder(ChallengeOrder item) {
        item.setUser(userService.selectSummaryInfo(item.getUserId()));
        item.setChannelName(channelMerchantService.detail(item.getChannelId()).getName());
        if (StringUtils.isNotBlank(item.getLogisticsCompany())) {
            Express express = expressService.detailByKdnCode(item.getLogisticsCompany());

            if (null == express) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "快递物流错误");
            }
            item.setLogisticsCompanyName(express.getName());
        }

    }

    @Override
    public List<ChallengeOrder> page(ChallengeOrder req) {

        return challengeOrderMapper.selectByCondition(req);
    }

    /**
     * 列表查询挑战兑换订单
     *
     * @param req 列表查询挑战兑换订单入参
     * @return 列表挑战兑换订单对象
     */
    @Override
    public List<ChallengeOrder> list(ChallengeOrderListReq req) {
        ChallengeOrder condition = EntityUtils.copyData(req, ChallengeOrder.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), ChallengeOrder.class));

        List<ChallengeOrder> challengeOrderList = challengeOrderMapper.selectByCondition(condition);
        // 转译UserId
        challengeOrderList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        return challengeOrderList;
    }

    @Override
    public List<ChallengeOrder> list(ChallengeOrder req) {
        return challengeOrderMapper.selectByCondition(req);
    }

    /**
     * 前端详情查询挑战兑换订单
     *
     * @param id 主键ID
     * @return 挑战兑换订单对象
     */
    @Override
    public ChallengeOrderDetailRes detailFront(Long id) {
        ChallengeOrderDetailRes res = challengeOrderMapper.selectByPrimaryKeyFront(id);

        Challenge challenge = challengeService.detail(res.getChallengeId());
        ChallengeDetailAwardRes awardRes = new ChallengeDetailAwardRes();
        awardRes.setType(res.getType());
        awardRes.setId(challenge.getAwardRefId());
        awardRes.setAwardQuantityUnit(ECollectionPeriodCategory.DERIVATIVE.getUnit());

        // 奖品信息
        if (EChallengeType.CHALLENGE_TYPE_1.getCode().equals(awardRes.getType())) {
            CompanyEntity companyEntity = companyEntityService.detail(challenge.getAwardRefId());
            awardRes.setName(companyEntity.getName());
            awardRes.setCoverFileUrl(companyEntity.getImageUrl());
            awardRes.setAwardQuantity(challenge.getAwardQuantity());
        } else if (EChallengeType.CHALLENGE_TYPE_0.getCode().equals(awardRes.getType())) {
            CollectionDetail collectionDetail = collectionDetailService
                    .detailByBiz(ECollectionDetailRefType.COLLECTION_DETAIL_REF_TYPE_8.getCode(), res.getId());
            if (null == collectionDetail) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "奖励发放延迟，请联系客服");
            }
            Collection collection = collectionService.detailSimple(collectionDetail.getCollectionId());
            awardRes.setId(collectionDetail.getId());
            awardRes.setName(collection.getName());
            awardRes.setLevelType(collection.getLevelType());
            awardRes.setCoverFileUrl(collection.getCoverFileUrl());
            awardRes.setFileType(collection.getFileType());

            if (StringUtils.isNotBlank(collection.getTags())) {
                List<String> result = Arrays.asList(collection.getTags().split(","));
                awardRes.setTagList(result);
            }


            awardRes.setTokenId(ContractToken.getHideTokenId(collectionDetail.getTokenId()));
        }

        res.setAwardRes(awardRes);
        // 物流信息
        ChallengeDetailEntityRes entityRes = detailEntity(res.getId());
        res.setEntityRes(entityRes);

        // 消耗藏品列表
        List<ChallengeOrderExchangeDetailRes> exchangeDetailResList = challengeOrderDetailService.detailByOrderId(res.getId());
        for (ChallengeOrderExchangeDetailRes challengeOrderExchangeDetailRes : exchangeDetailResList) {
            challengeOrderExchangeDetailRes.setTokenId(ContractToken.getHideTokenId(challengeOrderExchangeDetailRes.getTokenId()));
        }
        res.setExchangeDetailResList(exchangeDetailResList);

        res.setExchangeConditionList(challengeConditionService.selectByChallengeId(challenge.getId(), null));
        return res;
    }

    /**
     * 前端分页查询挑战兑换订单
     *
     * @param req 前端分页查询挑战兑换订单入参
     * @return 分页挑战兑换订单对象
     */
    @Override
    public List<ChallengeOrderPageRes> pageFront(ChallengeOrderPageFrontReq req, User operator, Long channelId) {
        ChallengeOrder condition = new ChallengeOrder();
        condition.setUserId(operator.getId());
        if (StringUtils.isNotBlank(req.getType()) && EChallengeHistoryType.CHALLENGE_TYPE_0.getCode().equals(req.getType())) {
            List<String> statusList = new ArrayList<>();
            statusList.add(EChallengeOrderStatus.CHALLENGE_ORDER_STATUS_0.getCode());
            statusList.add(EChallengeOrderStatus.CHALLENGE_ORDER_STATUS_1.getCode());
            condition.setStatusList(statusList);
        } else if (StringUtils.isNotBlank(req.getType()) && EChallengeHistoryType.CHALLENGE_TYPE_1.getCode().equals(req.getType())) {
            condition.setStatus(EChallengeOrderStatus.CHALLENGE_ORDER_STATUS_2.getCode());
        }
        condition.setChallengeType(req.getChallengeType());
        condition.setChannelId(channelId);

        condition.setOrderBy("t.id desc");
        List<ChallengeOrder> orderList = page(condition);

        List<ChallengeOrderPageRes> resList = new ArrayList<>();
        for (ChallengeOrder order : orderList) {
            ChallengeOrderPageRes res = new ChallengeOrderPageRes();
            ChallengeDetailRes detailRes = challengeService.detailFrontSimple(order.getChallengeId());
            BeanUtils.copyProperties(detailRes, res);
            res.setCreateTime(order.getCreateTime());

            res.setChallengeType(res.getType());
            if (EChallengeStartStatus.THREE.getCode().equals(res.getStartStatus())) {
                res.setStartStatus(EChallengeStartStatus.TWO.getCode());
            }

            res.setStatus(order.getStatus());
//            if (EChallengeOrderStatus.CHALLENGE_ORDER_STATUS_2.getCode().equals(order.getStatus())) {
//                res.setStatus(EBoolean.YES.getCode());
//            } else {
//                res.setStatus(EBoolean.NO.getCode());
//            }
            res.setId(order.getId());
            resList.add(res);
        }

        return PageInfoUtil.listToPage(orderList, resList);
    }


    /**
     * 前端列表查询挑战兑换订单
     *
     * @param req 前端列表查询挑战兑换订单入参
     * @return 列表挑战兑换订单对象
     */
    @Override
    public List<ChallengeOrderListRes> listFront(ChallengeOrderListFrontReq req) {
        ChallengeOrder condition = EntityUtils.copyData(req, ChallengeOrder.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), ChallengeOrder.class));

        List<ChallengeOrder> challengeOrderList = challengeOrderMapper.selectByCondition(condition);
        // 转译UserId
        challengeOrderList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        List<ChallengeOrderListRes> resList = challengeOrderList.stream().map((entity) -> {
            ChallengeOrderListRes res = new ChallengeOrderListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

    /**
     * 挑战实物奖励发货
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delivery(ChallengeDeliveryReq request, User operator) {
        ChallengeOrder challengeOrder = detailSimple(request.getId());

        if (EUserKind.BP.getCode().equals(operator.getKind())) {
            Company company = companyService.checkCompany(operator.getCompanyId());
            Challenge challenge = challengeService.detail(challengeOrder.getChallengeId());
            if (!challenge.getCompanyId().equals(company.getId())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "发行方不匹配");
            }
        }

        if (!EChallengeType.CHALLENGE_TYPE_1.getCode().equals(challengeOrder.getChallengeType())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "只有实物奖品需要发货");
        }
        if (!EChallengeOrderStatus.CHALLENGE_ORDER_STATUS_0.getCode().equals(challengeOrder.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "只有待发货的可以发货");
        }

        challengeOrder.setStatus(EChallengeOrderStatus.CHALLENGE_ORDER_STATUS_1.getCode());
        challengeOrder.setDeliverer(request.getDeliverer());
        challengeOrder.setDeliveryDatetime(DateUtil.strToDate(request.getDeliveryDatetime(), DateUtil.DATA_TIME_PATTERN_1));
        challengeOrder.setLogisticsCompany(request.getLogisticsCompany());
        challengeOrder.setLogisticsCode(request.getLogisticsCode());

        challengeOrder.setUpdater(operator.getId());
        challengeOrder.setUpdaterName(operator.getLoginName());
        challengeOrder.setUpdateTime(new Date());

        CompanyEntity companyEntity = companyEntityService.detailForUpdate(challengeOrder.getAwardRefId());
        companyEntity.setSendQuantity(companyEntity.getSendQuantity() + 1);
        companyEntityService.modify(companyEntity);

        challengeOrderMapper.updateByPrimaryKeySelective(challengeOrder);
    }

    /**
     * 批量违约
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDefault(ChallengeOrderBatchDefaultReq request, User operator) {

        Map<Long, CollectionDetail> map = new HashMap<>();
        for (Long id : request.getIdList()) {
            ChallengeOrder challengeOrder = detailForUpdate(id);
            Challenge challenge = challengeService.detail(challengeOrder.getChallengeId());

            if (EUserKind.BP.getCode().equals(operator.getKind())) {
                Company company = companyService.checkCompany(operator.getCompanyId());
                if (!challenge.getCompanyId().equals(company.getId())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "发行方不匹配");
                }
            }

            // 判断订单状态
            if (!EChallengeOrderStatus.CHALLENGE_ORDER_STATUS_0.getCode().equals(challengeOrder.getStatus())
                    || !EChallengeType.CHALLENGE_TYPE_1.getCode().equals(challengeOrder.getChallengeType())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "只有待发货的实物订单可以违约");
            }
            challengeOrder.setStatus(EChallengeOrderStatus.CHALLENGE_ORDER_STATUS_2.getCode());
            challengeOrder.setUpdater(operator.getId());
            challengeOrder.setUpdaterName(operator.getLoginName());
            challengeOrder.setUpdateTime(new Date());

            // 违约说明
            String remark = EmojiParser.removeAllEmojis(request.getRemark());
            challengeOrder.setRemark(remark);

            challengeOrderMapper.updateByPrimaryKeySelective(challengeOrder);

            // 查询用户的兑换订单详情
            ChallengeOrderDetail condition = new ChallengeOrderDetail();
            condition.setUserId(challengeOrder.getUserId());
            condition.setChallengeId(challengeOrder.getChallengeId());
            condition.setOrderId(challengeOrder.getId());

            List<ChallengeOrderDetail> list = challengeOrderDetailService.list(condition);

            for (ChallengeOrderDetail orderDetail : list) {
                if (EChallengeCollectionBackType.CHALLENGE_TYPE_0.getCode().equals(orderDetail.getBackType())) {
                    // 把用户的nft还回去,优先原路退回，如果无法原路退回再找相同作品退回
                    CollectionDetail collectionDetail = collectionDetailService.detailSimple(orderDetail.getCollectionDetailId());
                    if (ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_0.getCode().equals(collectionDetail.getStatus())) {
                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                                "关联藏品状态为0，请联系技术人员");
                    }
                    // 判断藏品当前拥有者是否是该机构
                    if (!EBoolean.YES.getCode().equals(collectionDetail.getOwnerType())
                            || !challenge.getCompanyId().equals(collectionDetail.getOwnerId())
                            || !ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_6.getCode().equals(collectionDetail.getStatus())) {
                        // 不是说明该机构已将该藏品使用掉，需要找个新的相同作品的藏品
                        List<CollectionDetail> collectionDetailList = collectionDetailService
                                .detailCompanyList(collectionDetail.getCollectionId(), challenge.getCompanyId(),
                                        ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_6.getCode());
                        if (CollectionUtils.isEmpty(collectionDetailList) || collectionDetailList.size() < 1) {
                            Company company = companyService.detail(challenge.getCompanyId());
                            Collection collection = collectionService.detailSimple(collectionDetail.getCollectionId());
                            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                                    "机构" + company.getName() + "藏品：" + collection.getName() + "剩余数量不足,无法违约退还");
                        }
                        collectionDetail = collectionDetailList.get(0);
                    }
                    // 退还藏品
                    collectionDetailRecordService
                            .modifyOwnership(null, collectionDetail.getId(), EBoolean.YES.getCode(), collectionDetail.getOwnerId(),
                                    EBoolean.NO.getCode(), challengeOrder.getUserId(),
                                    ECollectionDetailRecordTradeType.COLLECTION_DETAIL_RECORD_TRADE_TYPE_10.getCode()
                                    , challengeOrder.getId(), BigDecimal.ZERO,
                                    ECollectionDetailBuyChannel.CHALLENGE.getCode());
                } else if (EChallengeCollectionBackType.CHALLENGE_TYPE_1.getCode().equals(orderDetail.getBackType())) {
                    CollectionDetail collectionDetail = map.get(orderDetail.getCollectionDetailId());
                    if (null == collectionDetail) {
                        collectionDetail = collectionDetailService.detailForUpdate(orderDetail.getCollectionDetailId());
                    }
                    collectionDetail.setChallengeNumber(collectionDetail.getChallengeNumber() - 1);
                    map.put(collectionDetail.getId(), collectionDetail);
                }

            }
//            for (ChallengeCollection challengeCollection : challengeCollectionService
//                    .detailListByChallengeId(challengeOrder.getChallengeId())) {
//
//                // 把用户的nft还回去
//                List<CollectionDetail> collectionDetailList = collectionDetailService
//                        .detailCompanyList(challengeCollection.getCollectionId(), challenge.getCompanyId(),
//                                ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_0.getCode());
//                if (collectionDetailList.size() < challengeCollection.getCollectionQuantity()) {
//                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "机构退还NFT剩余数量不足");
//                }
//                for (int i = 0; i < challengeCollection.getCollectionQuantity(); i++) {
//                    CollectionDetail collectionDetail = collectionDetailList.get(i);
//                    collectionDetailRecordService
//                            .modifyOwnership(null, collectionDetail.getId(), challengeOrder.getUserId(),
//                                    ECollectionDetailRecordTradeType.COLLECTION_DETAIL_RECORD_TRADE_TYPE_10.getCode()
//                                    , challengeOrder.getId(), BigDecimal.ZERO,
//                                    ECollectionDetailBuyChannel.CHALLENGE.getCode());
//                }
//            }
        }
        for (Long id : map.keySet()) {
            CollectionDetail collectionDetail = map.get(id);

            collectionDetailService.modify(collectionDetail);
        }
    }

    @Override
    public ChallengeOrder detailForUpdate(Long id) {
        ChallengeOrder challengeOrder = challengeOrderMapper.selectForUpdate(id);
        if (null == challengeOrder) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return challengeOrder;
    }

    @Override
    public Integer listCount(ChallengeOrder challengeOrder) {
        return challengeOrderMapper.selectCount(challengeOrder);
    }

}