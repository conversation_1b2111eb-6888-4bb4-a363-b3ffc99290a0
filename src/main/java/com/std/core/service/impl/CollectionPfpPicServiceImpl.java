package com.std.core.service.impl;

import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EErrorCode;
import com.std.core.mapper.CollectionPfpPicMapper;
import com.std.core.pojo.domain.*;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.CollectionPfpPicCreateReq;
import com.std.core.pojo.request.CollectionPfpPicListReq;
import com.std.core.pojo.request.CollectionPfpPicListFrontReq;
import com.std.core.pojo.request.CollectionPfpPicModifyReq;
import com.std.core.pojo.request.CollectionPfpPicPageReq;
import com.std.core.pojo.request.CollectionPfpPicPageFrontReq;
import com.std.core.pojo.request.PfpImageBatchUploadReq;
import com.std.core.pojo.request.PfpImageUrlBatchUploadReq;
import com.std.core.pojo.request.PfpOssZipUploadReq;
import com.std.core.pojo.request.PfpZipUploadReq;
import com.std.core.pojo.response.CollectionPfpPicDetailRes;
import com.std.core.pojo.response.CollectionPfpPicListRes;
import com.std.core.pojo.response.CollectionPfpPicPageRes;
import com.std.core.pojo.response.PfpImageUploadRes;
import com.std.core.service.ICollectionPfpPicService;
import com.std.core.util.ZipFileUtil;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;

import com.std.core.service.ICommonService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * PFP待开图ServiceImpl
 *
 * <AUTHOR> wzh
 * @since : 2023-08-19 10:53
 */
@Service
@Slf4j
public class CollectionPfpPicServiceImpl implements ICollectionPfpPicService {

    @Resource
    private CollectionPfpPicMapper collectionPfpPicMapper;

    @Resource
    private ICommonService commonService;

    /**
     * 新增PFP待开图
     *
     * @param req      新增PFP待开图入参
     * @param operator 操作人
     */
    @Override
    public void create(CollectionPfpPicCreateReq req, User operator) {
        CollectionPfpPic collectionPfpPic = EntityUtils.copyData(req, CollectionPfpPic.class);
        collectionPfpPicMapper.insertSelective(collectionPfpPic);
    }

    @Override
    @Deprecated
    public void create(Long collectionId, Integer number, Integer orderNumber) {
        // 此方法已废弃，建议使用批量上传接口
        // 如果仍需使用，请通过配置文件配置图片路径
        throw new BizException(EErrorCode.E500003.getCode(),
                "此方法已废弃，请使用批量上传接口：/v1/pfp/image/batch_upload_files 或 /v1/pfp/image/batch_upload_urls");
    }

    @Override
    public void create(Long collectionId, Integer orderNumber, String pic, String picIpfs) {
        CollectionPfpPic collectionPfpPic = new CollectionPfpPic();
        collectionPfpPic.setCollectionId(collectionId);

        collectionPfpPic.setPic(pic);
        collectionPfpPic.setIpfsPic(picIpfs);
        collectionPfpPic.setOrderNumber(orderNumber);
        collectionPfpPicMapper.insertSelective(collectionPfpPic);
    }

    public static void main(String[] args) {
        System.out.println(UUID.randomUUID().toString().replace("-", ""));
    }

    /**
     * 删除PFP待开图
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        collectionPfpPicMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改PFP待开图
     *
     * @param req      修改PFP待开图入参
     * @param operator 操作人
     */
    @Override
    public void modify(CollectionPfpPicModifyReq req, User operator) {
        CollectionPfpPic collectionPfpPic = EntityUtils.copyData(req, CollectionPfpPic.class);
        collectionPfpPicMapper.updateByPrimaryKeySelective(collectionPfpPic);
    }

    /**
     * 详情查询PFP待开图
     *
     * @param id 主键ID
     * @return PFP待开图对象
     */
    @Override
    public CollectionPfpPic detail(Long id) {
        CollectionPfpPic collectionPfpPic = collectionPfpPicMapper.selectByPrimaryKey(id);
        if (null == collectionPfpPic) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return collectionPfpPic;
    }

    /**
     * 分页查询PFP待开图
     *
     * @param req 分页查询PFP待开图入参
     * @return 分页PFP待开图对象
     */
    @Override
    public List<CollectionPfpPic> page(CollectionPfpPicPageReq req) {
        CollectionPfpPic condition = EntityUtils.copyData(req, CollectionPfpPic.class);

        List<CollectionPfpPic> collectionPfpPicList = collectionPfpPicMapper.selectByCondition(condition);

        return collectionPfpPicList;
    }

    /**
     * 列表查询PFP待开图
     *
     * @param req 列表查询PFP待开图入参
     * @return 列表PFP待开图对象
     */
    @Override
    public List<CollectionPfpPic> list(CollectionPfpPicListReq req) {
        CollectionPfpPic condition = EntityUtils.copyData(req, CollectionPfpPic.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), CollectionPfpPic.class));

        List<CollectionPfpPic> collectionPfpPicList = collectionPfpPicMapper.selectByCondition(condition);

        return collectionPfpPicList;
    }

    /**
     * 前端详情查询PFP待开图
     *
     * @param id 主键ID
     * @return PFP待开图对象
     */
    @Override
    public CollectionPfpPicDetailRes detailFront(Long id) {
        CollectionPfpPicDetailRes res = new CollectionPfpPicDetailRes();

        CollectionPfpPic collectionPfpPic = collectionPfpPicMapper.selectByPrimaryKey(id);
        if (null == collectionPfpPic) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        BeanUtils.copyProperties(collectionPfpPic, res);

        return res;
    }

    /**
     * 前端分页查询PFP待开图
     *
     * @param req 前端分页查询PFP待开图入参
     * @return 分页PFP待开图对象
     */
    @Override
    public List<CollectionPfpPicPageRes> pageFront(CollectionPfpPicPageFrontReq req) {
        CollectionPfpPic condition = EntityUtils.copyData(req, CollectionPfpPic.class);
        List<CollectionPfpPic> collectionPfpPicList = collectionPfpPicMapper.selectByCondition(condition);

        List<CollectionPfpPicPageRes> resList = collectionPfpPicList.stream().map((entity) -> {
            CollectionPfpPicPageRes res = new CollectionPfpPicPageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(collectionPfpPicList, resList);
    }

    /**
     * 前端列表查询PFP待开图
     *
     * @param req 前端列表查询PFP待开图入参
     * @return 列表PFP待开图对象
     */
    @Override
    public List<CollectionPfpPicListRes> listFront(CollectionPfpPicListFrontReq req) {
        CollectionPfpPic condition = EntityUtils.copyData(req, CollectionPfpPic.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), CollectionPfpPic.class));

        List<CollectionPfpPic> collectionPfpPicList = collectionPfpPicMapper.selectByCondition(condition);

        List<CollectionPfpPicListRes> resList = collectionPfpPicList.stream().map((entity) -> {
            CollectionPfpPicListRes res = new CollectionPfpPicListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

    @Override
    public List<CollectionPfpPic> listByCollectionIdList(List<Long> collectionIdList) {
        CollectionPfpPic condition = new CollectionPfpPic();
        condition.setCollectionIdList(collectionIdList);
        List<CollectionPfpPic> collectionPfpPics = collectionPfpPicMapper.selectByCondition(condition);

        return collectionPfpPics;
    }

    @Override
    public Integer detailMaxOrderNumber() {
        return collectionPfpPicMapper.selectMaxOrderNumber();
    }

    @Override
    public PfpImageUploadRes batchUploadImages(PfpImageBatchUploadReq req, User operator) {
        PfpImageUploadRes response = new PfpImageUploadRes();
        List<PfpImageUploadRes.PfpImageInfo> successList = new ArrayList<>();
        List<PfpImageUploadRes.PfpImageFailInfo> failList = new ArrayList<>();

        // 批量上传文件到OSS
        List<String> ossUrls = commonService.batchUploadFiles(req.getImageFiles(), req.getImagePrefix());

        // 处理上传结果
        for (int i = 0; i < req.getImageFiles().size(); i++) {
            MultipartFile file = req.getImageFiles().get(i);
            String ossUrl = ossUrls.get(i);

            if (ossUrl != null) {
                try {
                    // 计算排序号
                    Integer orderNumber = "AUTO".equals(req.getNamingRule()) ?
                            req.getStartNumber() + i : i + 1;

                    // 保存到数据库
                    create(req.getPeriodId(), orderNumber, ossUrl, ossUrl);

                    // 添加到成功列表
                    PfpImageUploadRes.PfpImageInfo imageInfo = new PfpImageUploadRes.PfpImageInfo();
                    imageInfo.setOrderNumber(orderNumber);
                    imageInfo.setOriginalName(file.getOriginalFilename());
                    imageInfo.setOssUrl(ossUrl);
                    imageInfo.setIpfsUrl(ossUrl); // 暂时使用OSS URL作为IPFS URL
                    successList.add(imageInfo);

                } catch (Exception e) {
                    // 添加到失败列表
                    PfpImageUploadRes.PfpImageFailInfo failInfo = new PfpImageUploadRes.PfpImageFailInfo();
                    failInfo.setOriginalName(file.getOriginalFilename());
                    failInfo.setErrorMessage("数据库保存失败: " + e.getMessage());
                    failList.add(failInfo);
                }
            } else {
                // 添加到失败列表
                PfpImageUploadRes.PfpImageFailInfo failInfo = new PfpImageUploadRes.PfpImageFailInfo();
                failInfo.setOriginalName(file.getOriginalFilename());
                failInfo.setErrorMessage("文件上传失败");
                failList.add(failInfo);
            }
        }

        response.setSuccessCount(successList.size());
        response.setFailCount(failList.size());
        response.setSuccessList(successList);
        response.setFailList(failList);

        return response;
    }

    @Override
    public PfpImageUploadRes batchUploadFromUrls(PfpImageUrlBatchUploadReq req, User operator) {
        PfpImageUploadRes response = new PfpImageUploadRes();
        List<PfpImageUploadRes.PfpImageInfo> successList = new ArrayList<>();
        List<PfpImageUploadRes.PfpImageFailInfo> failList = new ArrayList<>();

        // 批量从URL上传文件到OSS
        List<String> ossUrls = commonService.batchUploadFromUrls(req.getImageUrls(), req.getImagePrefix());

        // 处理上传结果
        for (int i = 0; i < req.getImageUrls().size(); i++) {
            String imageUrl = req.getImageUrls().get(i);
            String ossUrl = ossUrls.get(i);

            if (ossUrl != null) {
                try {
                    // 计算排序号
                    Integer orderNumber = "AUTO".equals(req.getNamingRule()) ?
                            req.getStartNumber() + i : i + 1;

                    // 保存到数据库
                    create(req.getPeriodId(), orderNumber, ossUrl, ossUrl);

                    // 添加到成功列表
                    PfpImageUploadRes.PfpImageInfo imageInfo = new PfpImageUploadRes.PfpImageInfo();
                    imageInfo.setOrderNumber(orderNumber);
                    imageInfo.setOriginalName(getFileNameFromUrl(imageUrl));
                    imageInfo.setOssUrl(ossUrl);
                    imageInfo.setIpfsUrl(ossUrl); // 暂时使用OSS URL作为IPFS URL
                    successList.add(imageInfo);

                } catch (Exception e) {
                    // 添加到失败列表
                    PfpImageUploadRes.PfpImageFailInfo failInfo = new PfpImageUploadRes.PfpImageFailInfo();
                    failInfo.setOriginalName(getFileNameFromUrl(imageUrl));
                    failInfo.setErrorMessage("数据库保存失败: " + e.getMessage());
                    failList.add(failInfo);
                }
            } else {
                // 添加到失败列表
                PfpImageUploadRes.PfpImageFailInfo failInfo = new PfpImageUploadRes.PfpImageFailInfo();
                failInfo.setOriginalName(getFileNameFromUrl(imageUrl));
                failInfo.setErrorMessage("URL图片上传失败");
                failList.add(failInfo);
            }
        }

        response.setSuccessCount(successList.size());
        response.setFailCount(failList.size());
        response.setSuccessList(successList);
        response.setFailList(failList);

        return response;
    }

    /**
     * 从URL中提取文件名
     */
    private String getFileNameFromUrl(String url) {
        if (url == null || url.isEmpty()) {
            return "unknown";
        }

        int lastSlashIndex = url.lastIndexOf('/');
        if (lastSlashIndex >= 0 && lastSlashIndex < url.length() - 1) {
            return url.substring(lastSlashIndex + 1);
        }

        return url;
    }

    @Override
    public PfpImageUploadRes batchUploadFromZip(PfpZipUploadReq req, User operator) {
        PfpImageUploadRes response = new PfpImageUploadRes();
        List<PfpImageUploadRes.PfpImageInfo> successList = new ArrayList<>();
        List<PfpImageUploadRes.PfpImageFailInfo> failList = new ArrayList<>();

        try {
            // 验证ZIP文件
            if (!ZipFileUtil.isValidZipFile(req.getZipFile())) {
                throw new BizException(EErrorCode.E500003.getCode(), "无效的ZIP文件");
            }

            // 解析支持的文件格式
            Set<String> supportedFormats = ZipFileUtil.parseSupportedFormats(req.getSupportedFormats());

            // 设置限制参数
            long maxFileSize = 10 * 1024 * 1024; // 10MB
            int maxTotalFiles = 1000; // 最大1000个文件

            // 从ZIP文件中提取图片
            List<ZipFileUtil.ZipImageInfo> imageInfoList = ZipFileUtil.extractImagesFromZip(
                    req.getZipFile(), supportedFormats, maxFileSize, maxTotalFiles);

            if (imageInfoList.isEmpty()) {
                throw new BizException(EErrorCode.E500003.getCode(), "ZIP文件中没有找到有效的图片文件");
            }

            log.info("从ZIP文件中提取了 {} 个图片文件", imageInfoList.size());

            // 批量上传图片
            for (int i = 0; i < imageInfoList.size(); i++) {
                ZipFileUtil.ZipImageInfo imageInfo = imageInfoList.get(i);

                try {
                    // 生成OSS对象名称
                    String objectName = generateObjectName(req, imageInfo, i);

                    // 上传到OSS
                    String ossUrl = commonService.uploadBytes(imageInfo.getData(), objectName);

                    // 计算排序号
                    Integer orderNumber = "AUTO".equals(req.getNamingRule()) ?
                            req.getStartNumber() + i : i + 1;

                    // 保存到数据库
                    create(req.getPeriodId(), orderNumber, ossUrl, ossUrl);

                    // 添加到成功列表
                    PfpImageUploadRes.PfpImageInfo uploadInfo = new PfpImageUploadRes.PfpImageInfo();
                    uploadInfo.setOrderNumber(orderNumber);
                    uploadInfo.setOriginalName(imageInfo.getFileName());
                    uploadInfo.setOssUrl(ossUrl);
                    uploadInfo.setIpfsUrl(ossUrl); // 暂时使用OSS URL作为IPFS URL
                    successList.add(uploadInfo);

                    log.debug("ZIP图片上传成功: {} -> {}", imageInfo.getFileName(), ossUrl);

                } catch (Exception e) {
                    log.error("ZIP图片上传失败: {}", imageInfo.getFileName(), e);

                    // 添加到失败列表
                    PfpImageUploadRes.PfpImageFailInfo failInfo = new PfpImageUploadRes.PfpImageFailInfo();
                    failInfo.setOriginalName(imageInfo.getFileName());
                    failInfo.setErrorMessage("上传失败: " + e.getMessage());
                    failList.add(failInfo);
                }
            }

        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error("处理ZIP文件失败", e);
            throw new BizException(EErrorCode.E500003.getCode(), "处理ZIP文件失败: " + e.getMessage());
        }

        response.setSuccessCount(successList.size());
        response.setFailCount(failList.size());
        response.setSuccessList(successList);
        response.setFailList(failList);

        log.info("ZIP文件批量上传完成，期数ID: {}, 成功: {}, 失败: {}",
                req.getPeriodId(), response.getSuccessCount(), response.getFailCount());

        return response;
    }

    /**
     * 生成OSS对象名称
     */
    private String generateObjectName(PfpZipUploadReq req, ZipFileUtil.ZipImageInfo imageInfo, int index) {
        String extension = "";
        String fileName = imageInfo.getFileName();

        if (fileName != null && fileName.contains(".")) {
            extension = fileName.substring(fileName.lastIndexOf("."));
        }

        String uuid = UUID.randomUUID().toString().replace("-", "");

        if ("FILENAME".equals(req.getNamingRule())) {
            // 使用原始文件名
            if (req.getKeepDirectoryStructure() && !imageInfo.getRelativePath().isEmpty()) {
                return req.getImagePrefix() + "/" + imageInfo.getRelativePath() + "/" + uuid + "_" + fileName;
            } else {
                return req.getImagePrefix() + "/" + uuid + "_" + fileName;
            }
        } else {
            // 自动编号
            int orderNumber = req.getStartNumber() + index;
            return req.getImagePrefix() + "/" + uuid + "_" + orderNumber + extension;
        }
    }

    @Override
    public PfpImageUploadRes batchUploadFromOssZip(PfpOssZipUploadReq req, User operator) {
        PfpImageUploadRes response = new PfpImageUploadRes();
        List<PfpImageUploadRes.PfpImageInfo> successList = new ArrayList<>();
        List<PfpImageUploadRes.PfpImageFailInfo> failList = new ArrayList<>();

        try {
            log.info("开始处理OSS ZIP文件: {}", req.getZipFileUrl());

            // 从OSS下载ZIP文件
            byte[] zipData = commonService.downloadFromOss(req.getZipFileUrl());
            if (zipData == null || zipData.length == 0) {
                throw new BizException(EErrorCode.E500003.getCode(), "无法从OSS下载ZIP文件");
            }

            log.info("成功下载ZIP文件，大小: {} 字节", zipData.length);

            // 解析支持的文件格式
            Set<String> supportedFormats = ZipFileUtil.parseSupportedFormats(req.getSupportedFormats());

            // 设置限制参数
            long maxFileSize = 10 * 1024 * 1024; // 10MB
            int maxTotalFiles = 1000; // 最大1000个文件

            // 从ZIP数据中提取图片
            List<ZipFileUtil.ZipImageInfo> imageInfoList = extractImagesFromZipData(
                    zipData, supportedFormats, maxFileSize, maxTotalFiles);

            if (imageInfoList.isEmpty()) {
                throw new BizException(EErrorCode.E500003.getCode(), "ZIP文件中没有找到有效的图片文件");
            }

            log.info("从ZIP文件中提取了 {} 个图片文件", imageInfoList.size());

            // 批量上传图片
            for (int i = 0; i < imageInfoList.size(); i++) {
                ZipFileUtil.ZipImageInfo imageInfo = imageInfoList.get(i);

                try {
                    // 生成OSS对象名称
                    String objectName = generateOssObjectName(req, imageInfo, i);

                    // 上传到OSS
                    String ossUrl = commonService.uploadBytes(imageInfo.getData(), objectName);

                    // 计算排序号
                    Integer orderNumber = "AUTO".equals(req.getNamingRule()) ?
                            req.getStartNumber() + i : i + 1;

                    // 保存到数据库
                    create(req.getPeriodId(), orderNumber, ossUrl, ossUrl);

                    // 添加到成功列表
                    PfpImageUploadRes.PfpImageInfo uploadInfo = new PfpImageUploadRes.PfpImageInfo();
                    uploadInfo.setOrderNumber(orderNumber);
                    uploadInfo.setOriginalName(imageInfo.getFileName());
                    uploadInfo.setOssUrl(ossUrl);
                    uploadInfo.setIpfsUrl(ossUrl); // 暂时使用OSS URL作为IPFS URL
                    successList.add(uploadInfo);

                    log.debug("OSS ZIP图片上传成功: {} -> {}", imageInfo.getFileName(), ossUrl);

                } catch (Exception e) {
                    log.error("OSS ZIP图片上传失败: {}", imageInfo.getFileName(), e);

                    // 添加到失败列表
                    PfpImageUploadRes.PfpImageFailInfo failInfo = new PfpImageUploadRes.PfpImageFailInfo();
                    failInfo.setOriginalName(imageInfo.getFileName());
                    failInfo.setErrorMessage("上传失败: " + e.getMessage());
                    failList.add(failInfo);
                }
            }

            // 处理完成后删除原ZIP文件（如果需要）
            if (req.getDeleteAfterProcess() != null && req.getDeleteAfterProcess()) {
                try {
                    boolean deleted = commonService.deleteOssFile(req.getZipFileUrl());
                    if (deleted) {
                        log.info("已删除原ZIP文件: {}", req.getZipFileUrl());
                    } else {
                        log.warn("删除原ZIP文件失败: {}", req.getZipFileUrl());
                    }
                } catch (Exception e) {
                    log.error("删除原ZIP文件时发生错误: {}", req.getZipFileUrl(), e);
                }
            }

        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error("处理OSS ZIP文件失败", e);
            throw new BizException(EErrorCode.E500003.getCode(), "处理OSS ZIP文件失败: " + e.getMessage());
        }

        response.setSuccessCount(successList.size());
        response.setFailCount(failList.size());
        response.setSuccessList(successList);
        response.setFailList(failList);

        log.info("OSS ZIP文件批量上传完成，期数ID: {}, 成功: {}, 失败: {}",
                req.getPeriodId(), response.getSuccessCount(), response.getFailCount());

        return response;
    }

    /**
     * 从ZIP字节数据中提取图片
     */
    private List<ZipFileUtil.ZipImageInfo> extractImagesFromZipData(byte[] zipData,
                                                                   Set<String> supportedFormats,
                                                                   long maxFileSize,
                                                                   int maxTotalFiles) throws IOException {

        List<ZipFileUtil.ZipImageInfo> imageList = new ArrayList<>();

        try (ByteArrayInputStream bais = new ByteArrayInputStream(zipData);
             ZipInputStream zipInputStream = new ZipInputStream(bais)) {

            ZipEntry entry;
            int fileCount = 0;

            while ((entry = zipInputStream.getNextEntry()) != null) {
                // 检查文件数量限制
                if (fileCount >= maxTotalFiles) {
                    log.warn("ZIP文件中的文件数量超过限制: {}", maxTotalFiles);
                    break;
                }

                // 跳过目录
                if (entry.isDirectory()) {
                    continue;
                }

                String fileName = entry.getName();

                // 检查是否为图片文件
                if (!isImageFile(fileName, supportedFormats)) {
                    log.debug("跳过非图片文件: {}", fileName);
                    continue;
                }

                // 检查文件大小
                if (entry.getSize() > maxFileSize) {
                    log.warn("文件大小超过限制，跳过: {} ({}字节)", fileName, entry.getSize());
                    continue;
                }

                // 读取文件数据
                byte[] fileData = readZipEntryData(zipInputStream, maxFileSize);

                if (fileData != null && fileData.length > 0) {
                    // 提取文件名（去除路径）
                    String simpleFileName = getSimpleFileName(fileName);
                    String relativePath = getRelativePath(fileName);

                    ZipFileUtil.ZipImageInfo imageInfo = new ZipFileUtil.ZipImageInfo(
                            simpleFileName, relativePath, fileData, fileData.length);
                    imageList.add(imageInfo);
                    fileCount++;

                    log.debug("提取图片文件: {} ({}字节)", fileName, fileData.length);
                }

                zipInputStream.closeEntry();
            }
        }

        log.info("从ZIP数据中提取了 {} 个图片文件", imageList.size());
        return imageList;
    }

    /**
     * 生成OSS对象名称（用于OSS ZIP处理）
     */
    private String generateOssObjectName(PfpOssZipUploadReq req, ZipFileUtil.ZipImageInfo imageInfo, int index) {
        String extension = "";
        String fileName = imageInfo.getFileName();

        if (fileName != null && fileName.contains(".")) {
            extension = fileName.substring(fileName.lastIndexOf("."));
        }

        String uuid = UUID.randomUUID().toString().replace("-", "");

        if ("FILENAME".equals(req.getNamingRule())) {
            // 使用原始文件名
            if (req.getKeepDirectoryStructure() && !imageInfo.getRelativePath().isEmpty()) {
                return req.getImagePrefix() + "/" + imageInfo.getRelativePath() + "/" + uuid + "_" + fileName;
            } else {
                return req.getImagePrefix() + "/" + uuid + "_" + fileName;
            }
        } else {
            // 自动编号
            int orderNumber = req.getStartNumber() + index;
            return req.getImagePrefix() + "/" + uuid + "_" + orderNumber + extension;
        }
    }

    // 辅助方法（从ZipFileUtil复制过来，避免重复代码）
    private boolean isImageFile(String fileName, Set<String> supportedFormats) {
        if (fileName == null || fileName.isEmpty()) {
            return false;
        }

        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return false;
        }

        String extension = fileName.substring(lastDotIndex + 1).toLowerCase();
        return supportedFormats.contains(extension);
    }

    private byte[] readZipEntryData(ZipInputStream zipInputStream, long maxFileSize) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] buffer = new byte[8192];
        int bytesRead;
        long totalBytesRead = 0;

        while ((bytesRead = zipInputStream.read(buffer)) != -1) {
            totalBytesRead += bytesRead;

            // 检查文件大小限制
            if (totalBytesRead > maxFileSize) {
                log.warn("文件大小超过限制，停止读取");
                return null;
            }

            baos.write(buffer, 0, bytesRead);
        }

        return baos.toByteArray();
    }

    private String getSimpleFileName(String fullPath) {
        if (fullPath == null) {
            return null;
        }

        // 处理不同操作系统的路径分隔符
        String normalizedPath = fullPath.replace('\\', '/');
        int lastSlashIndex = normalizedPath.lastIndexOf('/');

        if (lastSlashIndex >= 0) {
            return normalizedPath.substring(lastSlashIndex + 1);
        }

        return fullPath;
    }

    private String getRelativePath(String fullPath) {
        if (fullPath == null) {
            return "";
        }

        // 处理不同操作系统的路径分隔符
        String normalizedPath = fullPath.replace('\\', '/');
        int lastSlashIndex = normalizedPath.lastIndexOf('/');

        if (lastSlashIndex >= 0) {
            return normalizedPath.substring(0, lastSlashIndex);
        }

        return "";
    }

}