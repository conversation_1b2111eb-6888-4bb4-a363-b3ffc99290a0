package com.std.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.std.common.exception.BizException;
import com.std.core.config.ThirdPartyAuthConfig;
import com.std.core.enums.EBoolean;
import com.std.core.enums.EUserKind;
import com.std.core.util.*;
import com.std.core.mapper.UserMapper;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.H5AuthCallbackReq;
import com.std.core.pojo.request.H5AuthCheckReq;
import com.std.core.pojo.request.ThirdPartyAuthCheckReq;
import com.std.core.pojo.request.ThirdPartyAuthTokenReq;
import com.std.core.pojo.request.ThirdPartyUserInfoReq;
import com.std.core.pojo.request.ThirdPartyUserRegisterReq;
import com.std.core.pojo.response.H5AuthCallbackRes;
import com.std.core.pojo.response.H5AuthCheckRes;
import com.std.core.pojo.response.ThirdPartyAuthCheckRes;
import com.std.core.pojo.response.ThirdPartyAuthTokenRes;
import com.std.core.pojo.response.ThirdPartyUserInfoRes;
import com.std.core.service.ICuserService;
import com.std.core.service.IThirdPartyAuthService;
import com.std.core.util.RedisUtil;
import com.std.core.util.TokenUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 第三方授权Service实现类
 *
 * <AUTHOR> system
 * @since : 2024-07-29
 */
@Slf4j
@Service
public class ThirdPartyAuthServiceImpl implements IThirdPartyAuthService {

    @Resource
    private UserMapper userMapper;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private ThirdPartyAuthConfig thirdPartyAuthConfig;

    @Resource
    private TokenUtil tokenUtil;

    @Resource
    private ICuserService cuserService;

    /**
     * 授权令牌Redis前缀
     */
    private static final String AUTH_TOKEN_PREFIX = "third_party_auth_token:";

    /**
     * 用户授权状态Redis前缀
     */
    private static final String USER_AUTH_PREFIX = "third_party_user_auth:";

    @Override
    public ThirdPartyAuthCheckRes checkAuthStatus(ThirdPartyAuthCheckReq request) {
        // 查找用户
        User user = findUserByUniqueId(request.getUnique_id());

        if (user == null) {
            // 用户不存在，未授权
            return new ThirdPartyAuthCheckRes(false, null, request.getApp_key(), null);
        }

        List<String> scopes = Arrays.asList("user_info");
        return new ThirdPartyAuthCheckRes(true, user.getId(), request.getApp_key(), scopes);
    }

    @Override
    public ThirdPartyAuthTokenRes generateAuthToken(ThirdPartyAuthTokenReq request) {

        Map javaObject = JSON.toJavaObject(JSON.parseObject(JSON.toJSONString(request)), Map.class);

        String res = HttpUtil.postForm(thirdPartyAuthConfig.getDomain() + "/api.php?do=authorize&act=generate_token", javaObject);
        //{
        //    "code": 200,
        //    "data": {
        //        "token": "abc123def456",
        //        "expire_time": "2023-12-01 15:30:00",
        //        "requested_scopes": ["user_info"],
        //        "auth_url": "/api/auth_page.php?token=abc123def456"
        //    },
        //    "message": "令牌生成成功"
        //}

        JSONObject jsonObject = JSONObject.parseObject(res);

        if (jsonObject.getInteger("code") != 200) {
            throw new BizException("500", "授权失败");
        }

        JSONObject data = jsonObject.getJSONObject("data");

        return new ThirdPartyAuthTokenRes(data.getString("token"),
                data.getString("expire_time"), data.getJSONArray("requested_scopes").toJavaList(String.class), data.getString("auth_url"));
    }

    @Override
    public ThirdPartyUserInfoRes getUserBasicInfo(ThirdPartyUserInfoReq request) {

        Map javaObject = JSON.toJavaObject(JSON.parseObject(JSON.toJSONString(request)), Map.class);

        String res = HttpUtil.postForm(thirdPartyAuthConfig.getDomain() + "/api.php?do=authorize&act=get_user_basic_info", javaObject);
        //{
        //    "code": 200,
        //    "data": {
        //        "user_id": 123,
        //        "unique_id": "sadsadas",
        //        "username": "zhangsan",
        //        "avatar": "https://example.com/avatar.jpg",
        //        "mobile": "13800138000"
        //    },
        //    "message": "获取成功"
        //}

        JSONObject jsonObject = JSONObject.parseObject(res);
        if (jsonObject.getInteger("code") != 200) {
            throw new BizException("500", "授权失败");
        }

        return jsonObject.getJSONObject("data").toJavaObject(ThirdPartyUserInfoRes.class);
    }


    @Override
    public User findUserByUniqueId(String uniqueId) {
        if (StringUtils.isBlank(uniqueId)) {
            return null;
        }

        // 使用channelUid字段查询用户
        User condition = new User();
        condition.setChannelUid(uniqueId);
        condition.setKind(EUserKind.C.getCode());

        List<User> userList = userMapper.selectByCondition(condition);

        if (CollectionUtils.isNotEmpty(userList)) {
            return userList.get(0);
        }

        return null;
    }

    /**
     * 确认用户授权（这个方法会在授权页面确认后调用）
     *
     * @param token      授权令牌
     * @param authorized 是否授权
     */
    public void confirmAuthorization(String token, boolean authorized) {
        String tokenKey = AUTH_TOKEN_PREFIX + token;
        String tokenData = redisUtil.getString(tokenKey);

        if (StringUtils.isBlank(tokenData)) {
            throw new BizException("400", "无效的授权令牌");
        }

        String[] parts = tokenData.split(":");
        if (parts.length != 3) {
            throw new BizException("400", "授权令牌格式错误");
        }

        String uniqueId = parts[0];
        String appKey = parts[1];
        String scopes = parts[2];

        if (authorized) {
            // 用户同意授权，保存授权状态
            String authKey = USER_AUTH_PREFIX + appKey + ":" + uniqueId;
            long expireSeconds = thirdPartyAuthConfig.getUserAuthExpireSeconds();
            redisUtil.set(authKey, scopes, (int) expireSeconds);
            log.info("用户授权成功，unique_id: {}, app_key: {}", uniqueId, appKey);
        } else {
            log.info("用户拒绝授权，unique_id: {}, app_key: {}", uniqueId, appKey);
        }

        // 删除授权令牌
        redisUtil.del(tokenKey);
    }

    @Override
    public H5AuthCheckRes checkAuthAndLogin(H5AuthCheckReq request) {
        log.info("H5检查授权状态并处理登录，unique_id: {}", request.getUniqueId());

        // 查找用户
        User user = findUserByUniqueId(request.getUniqueId());

        if (user != null) {
            // 已授权，直接返回token
            JSONObject tokenJson = tokenUtil.generalToken(user.getId(), 5 * 24 * 60 * 60 * 1000L);
            String token = tokenJson.getString("token");

            // 构建用户信息
            JSONObject userInfo = new JSONObject();
            userInfo.put("userId", user.getId());
            userInfo.put("username", user.getUserName() != null ? user.getUserName() : user.getNickname());
            userInfo.put("mobile", user.getMobile());
            userInfo.put("photo", user.getPhoto());

            log.info("用户已授权，直接返回token，unique_id: {}, user_id: {}", request.getUniqueId(), user.getId());
            return H5AuthCheckRes.authorized(token, user.getId(), userInfo);
        }

        // 用户不存在或未授权，需要生成授权URL
        try {
            // 构建第三方授权令牌请求
            ThirdPartyAuthTokenReq tokenReq = new ThirdPartyAuthTokenReq();
            tokenReq.setUnique_id(request.getUniqueId());
            tokenReq.setApp_key(thirdPartyAuthConfig.getAppKey());
            tokenReq.setScopes("user_info");

            // 生成签名参数
            String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
            String nonce = java.util.UUID.randomUUID().toString().replace("-", "").substring(0, 16);
            String appSecret = thirdPartyAuthConfig.getAppSecret();
            String signString = timestamp + nonce + appSecret;
            String signature = com.std.core.util.wechat.MD5Util.md5(signString);

            tokenReq.setTimestamp(timestamp);
            tokenReq.setNonce(nonce);
            tokenReq.setSignature(signature);

            // 生成授权令牌
            ThirdPartyAuthTokenRes tokenRes = generateAuthToken(tokenReq);

            log.info("需要授权，返回授权URL，unique_id: {}, auth_url: {}", request.getUniqueId(), tokenRes.getAuth_url());
            return H5AuthCheckRes.needAuth( tokenRes.getAuth_url());

        } catch (Exception e) {
            log.error("生成授权URL失败", e);
            throw new BizException("500", "生成授权URL失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public H5AuthCallbackRes handleAuthCallback(H5AuthCallbackReq request) {
        log.info("H5授权回调处理，unique_id: {}, isAuthorize: {}", request.getUniqueId(), request.getIsAuthorize());

        // 检查授权状态
        if (!EBoolean.YES.getCode().equals(request.getIsAuthorize())) {
            throw new BizException("403", "用户拒绝授权");
        }

        try {
            // 调用第三方获取用户信息
            ThirdPartyUserInfoReq userInfoReq = new ThirdPartyUserInfoReq();
            userInfoReq.setUnique_id(request.getUniqueId());
            userInfoReq.setApp_key(thirdPartyAuthConfig.getAppKey());

            // 生成签名参数
            String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
            String nonce = java.util.UUID.randomUUID().toString().replace("-", "").substring(0, 16);
            String appSecret = thirdPartyAuthConfig.getAppSecret();
            String signString = timestamp + nonce + appSecret;
            String signature = com.std.core.util.wechat.MD5Util.md5(signString);

            userInfoReq.setTimestamp(timestamp);
            userInfoReq.setNonce(nonce);
            userInfoReq.setSignature(signature);

            // 获取第三方用户信息
            ThirdPartyUserInfoRes userInfoRes = getUserBasicInfo(userInfoReq);

            // 检查用户是否已存在
            User user = findUserByUniqueId(request.getUniqueId());
            boolean isNewUser = false;

            if (user == null) {
                // 用户不存在，自动注册
                ThirdPartyUserRegisterReq registerReq = new ThirdPartyUserRegisterReq();
                registerReq.setUnique_id(request.getUniqueId());
                registerReq.setUsername(userInfoRes.getUsername());
                registerReq.setMobile(userInfoRes.getMobile());
                registerReq.setAvatar(userInfoRes.getAvatar());
                registerReq.setThird_party_source("third_party_h5");

                user = cuserService.registerThirdPartyUser(registerReq);
                isNewUser = true;
                log.info("自动注册第三方用户成功，unique_id: {}, user_id: {}", request.getUniqueId(), user.getId());
            } else {
                log.info("第三方用户已存在，unique_id: {}, user_id: {}", request.getUniqueId(), user.getId());
            }

            // 生成用户token
            JSONObject tokenJson = tokenUtil.generalToken(user.getId(), 5 * 24 * 60 * 60 * 1000L);
            String token = tokenJson.getString("token");

            // 构建用户信息
            JSONObject userInfo = new JSONObject();
            userInfo.put("userId", user.getId());
            userInfo.put("username", user.getUserName() != null ? user.getUserName() : user.getNickname());
            userInfo.put("mobile", user.getMobile());
            userInfo.put("photo", user.getPhoto());
            userInfo.put("unique_id", request.getUniqueId());

            log.info("H5授权回调处理成功，unique_id: {}, user_id: {}, is_new_user: {}",
                    request.getUniqueId(), user.getId(), isNewUser);

            return new H5AuthCallbackRes(token, user.getId(), userInfo, isNewUser);

        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error("H5授权回调处理失败", e);
            throw new BizException("500", "授权回调处理失败");
        }
    }
}
