package com.std.core.service.impl;

import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EErrorCode;
import com.std.core.mapper.PitConditionsDetailMapper;
import com.std.core.pojo.domain.PitConditionsDetail;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.PitConditionsDetailCreateReq;
import com.std.core.pojo.request.PitConditionsDetailListReq;
import com.std.core.pojo.request.PitConditionsDetailListFrontReq;
import com.std.core.pojo.request.PitConditionsDetailModifyReq;
import com.std.core.pojo.request.PitConditionsDetailPageReq;
import com.std.core.pojo.request.PitConditionsDetailPageFrontReq;
import com.std.core.pojo.response.PitConditionsDetailDetailRes;
import com.std.core.pojo.response.PitConditionsDetailListRes;
import com.std.core.pojo.response.PitConditionsDetailPageRes;
import com.std.core.service.IPitConditionsDetailService;

import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.std.core.service.IUserService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 兑换组合ServiceImpl
 *
 * <AUTHOR> ycj
 * @since : 2022-05-06 17:35
 */
@Service
public class PitConditionsDetailServiceImpl implements IPitConditionsDetailService {

    @Resource
    private PitConditionsDetailMapper pitConditionsDetailMapper;

    @Resource
    private IUserService userService;

    /**
     * 新增兑换组合
     *
     * @param req      新增兑换组合入参
     * @param operator 操作人
     */
    @Override
    public void create(PitConditionsDetailCreateReq req, User operator) {
        PitConditionsDetail pitConditionsDetail = EntityUtils.copyData(req, PitConditionsDetail.class);
        pitConditionsDetailMapper.insertSelective(pitConditionsDetail);
    }

    /**
     * 删除兑换组合
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        pitConditionsDetailMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改兑换组合
     *
     * @param req      修改兑换组合入参
     * @param operator 操作人
     */
    @Override
    public void modify(PitConditionsDetailModifyReq req, User operator) {
        PitConditionsDetail pitConditionsDetail = EntityUtils.copyData(req, PitConditionsDetail.class);
        pitConditionsDetailMapper.updateByPrimaryKeySelective(pitConditionsDetail);
    }

    @Override
    public void modify(PitConditionsDetail req) {
        pitConditionsDetailMapper.updateByPrimaryKeySelective(req);
    }

    /**
     * 详情查询兑换组合
     *
     * @param id 主键ID
     * @return 兑换组合对象
     */
    @Override
    public PitConditionsDetail detail(Long id) {
        PitConditionsDetail pitConditionsDetail = pitConditionsDetailMapper.selectByPrimaryKeyOss(id);
        if (null == pitConditionsDetail) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        if (null != pitConditionsDetail.getUserId()) {
            pitConditionsDetail.setUser(userService.selectSummaryInfo(pitConditionsDetail.getUserId()));
        }
        return pitConditionsDetail;
    }

    /**
     * 分页查询兑换组合
     *
     * @param req 分页查询兑换组合入参
     * @return 分页兑换组合对象
     */
    @Override
    public List<PitConditionsDetail> page(PitConditionsDetailPageReq req) {
        PitConditionsDetail condition = EntityUtils.copyData(req, PitConditionsDetail.class);
        condition.setOrderBy("t.id desc");
        List<PitConditionsDetail> pitConditionsDetailList = pitConditionsDetailMapper.selectByConditionOss(condition);

        for (PitConditionsDetail detail : pitConditionsDetailList) {
            if (null != detail.getUserId()) {
                detail.setUser(userService.selectSummaryInfo(detail.getUserId()));
            }
        }
        return pitConditionsDetailList;
    }

    /**
     * 列表查询兑换组合
     *
     * @param req 列表查询兑换组合入参
     * @return 列表兑换组合对象
     */
    @Override
    public List<PitConditionsDetail> list(PitConditionsDetailListReq req) {
        PitConditionsDetail condition = EntityUtils.copyData(req, PitConditionsDetail.class);

        List<PitConditionsDetail> pitConditionsDetailList = pitConditionsDetailMapper.selectByConditionOss(condition);
        for (PitConditionsDetail detail : pitConditionsDetailList) {
            if (null != detail.getUserId()) {
                detail.setUser(userService.selectSummaryInfo(detail.getUserId()));
            }
        }
        return pitConditionsDetailList;
    }

    @Override
    public List<PitConditionsDetail> list(PitConditionsDetail req) {
        return pitConditionsDetailMapper.selectByCondition(req);
    }

    /**
     * 前端详情查询兑换组合
     *
     * @param id 主键ID
     * @return 兑换组合对象
     */
    @Override
    public PitConditionsDetailDetailRes detailFront(Long id) {
        PitConditionsDetailDetailRes res = new PitConditionsDetailDetailRes();

        PitConditionsDetail pitConditionsDetail = pitConditionsDetailMapper.selectByPrimaryKey(id);
        if (null == pitConditionsDetail) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        BeanUtils.copyProperties(pitConditionsDetail, res);

        return res;
    }

    /**
     * 前端分页查询兑换组合
     *
     * @param req 前端分页查询兑换组合入参
     * @return 分页兑换组合对象
     */
    @Override
    public List<PitConditionsDetailPageRes> pageFront(PitConditionsDetailPageFrontReq req) {
        PitConditionsDetail condition = EntityUtils.copyData(req, PitConditionsDetail.class);
        List<PitConditionsDetail> pitConditionsDetailList = pitConditionsDetailMapper.selectByCondition(condition);

        List<PitConditionsDetailPageRes> resList = pitConditionsDetailList.stream().map((entity) -> {
            PitConditionsDetailPageRes res = new PitConditionsDetailPageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(pitConditionsDetailList, resList);
    }

    /**
     * 前端列表查询兑换组合
     *
     * @param req 前端列表查询兑换组合入参
     * @return 列表兑换组合对象
     */
    @Override
    public List<PitConditionsDetailListRes> listFront(PitConditionsDetailListFrontReq req) {
        PitConditionsDetail condition = EntityUtils.copyData(req, PitConditionsDetail.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), PitConditionsDetail.class));

        List<PitConditionsDetail> pitConditionsDetailList = pitConditionsDetailMapper.selectByCondition(condition);

        List<PitConditionsDetailListRes> resList = pitConditionsDetailList.stream().map((entity) -> {
            PitConditionsDetailListRes res = new PitConditionsDetailListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

    @Override
    public void batchCreate(List<PitConditionsDetail> conditionsDetailList) {
        pitConditionsDetailMapper.insertBatch(conditionsDetailList);
    }

    @Override
    public List<PitConditionsDetail> detailByConditionsId(Long conditionsId) {
        PitConditionsDetail condition = new PitConditionsDetail();
        condition.setConditionId(conditionsId);
        List<PitConditionsDetail> pitConditionsDetailList = pitConditionsDetailMapper.selectByCondition(condition);
        return pitConditionsDetailList;
    }

}