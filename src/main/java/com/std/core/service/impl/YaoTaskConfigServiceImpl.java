package com.std.core.service.impl;

import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.EJourBizTypeUser;
import com.std.core.enums.EUserKind;
import com.std.core.enums.EUserYaoFlag;
import com.std.core.enums.EYaoTaskConfigStatus;
import com.std.core.enums.EYaoTaskConfigType;
import com.std.core.mapper.YaoTaskConfigMapper;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.domain.UserEntryRecord;
import com.std.core.pojo.domain.YaoChangeRecord;
import com.std.core.pojo.domain.YaoLoginRule;
import com.std.core.pojo.domain.YaoTaskConfig;
import com.std.core.pojo.request.YaoTaskConfigBatchModifyReq;
import com.std.core.pojo.request.YaoTaskConfigListFrontReq;
import com.std.core.pojo.request.YaoTaskConfigListReq;
import com.std.core.pojo.request.YaoTaskConfigModifyReq;
import com.std.core.pojo.request.YaoTaskConfigPageFrontReq;
import com.std.core.pojo.request.YaoTaskConfigPageReq;
import com.std.core.pojo.response.YaoTaskConfigDetailRes;
import com.std.core.pojo.response.YaoTaskConfigListRes;
import com.std.core.pojo.response.YaoTaskConfigPageRes;
import com.std.core.service.IUserEntryRecordService;
import com.std.core.service.IUserService;
import com.std.core.service.IYaoChangeRecordService;
import com.std.core.service.IYaoLoginRuleService;
import com.std.core.service.IYaoTaskConfigService;
import com.std.core.util.DateUtil;
import com.std.core.util.IdGeneratorUtil;
import com.std.core.util.RedisUtil;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 爻获取任务配置ServiceImpl
 *
 * <AUTHOR> ycj
 * @since : 2022-11-13 09:20
 */
@Slf4j
@Service
public class YaoTaskConfigServiceImpl implements IYaoTaskConfigService {

    @Resource
    private YaoTaskConfigMapper yaoTaskConfigMapper;

    @Resource
    private IYaoChangeRecordService yaoChangeRecordService;

    @Resource
    private IUserEntryRecordService userEntryRecordService;

    @Resource
    private IYaoLoginRuleService yaoLoginRuleService;

    @Resource
    private IUserService userService;

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 新增爻获取任务配置
     *
     * @param operator 操作人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(User operator) {
        if (EUserYaoFlag.E_USER_YAO_FLAG_0.getCode().equals(operator.getYaoFlag())) {
            return;
        }
        YaoTaskConfig yaoTaskConfig = detailByType(EYaoTaskConfigType.YAO_TASK_CONFIG_TYPE_0.getCode());
        if (null == yaoTaskConfig || !EYaoTaskConfigStatus.YAO_TASK_CONFIG_STATUS_1.getCode().equals(yaoTaskConfig.getStatus())) {
            return;
        }

        YaoChangeRecord yaoChangeRecord = yaoChangeRecordService
                .detailDayLoginRecord(operator, new Date(), EJourBizTypeUser.YaoAccount.Task.getCode(),
                        EJourBizTypeUser.YaoAccount.LoginGet.getCode());
        if (null != yaoChangeRecord) {
            return;
        }

        Long seriesNo = null;
        Integer day = 1;
        YaoLoginRule yaoLoginRule = new YaoLoginRule();

        // 查询最近的一条记录
        YaoChangeRecord lastRecord = yaoChangeRecordService
                .selectLastDayLoginRecord(operator, EJourBizTypeUser.YaoAccount.Task.getCode(),
                        EJourBizTypeUser.YaoAccount.LoginGet.getCode());
        if (null != lastRecord) {
            seriesNo = lastRecord.getSeriesNo();

            log.error(
                    "最新登录日期：" + DateUtil.dateToStr(lastRecord.getCreateDatetime(), com.std.common.utils.DateUtil.DATA_TIME_PATTERN_1));
            int i = DateUtil.daysBetweenDate(lastRecord.getCreateDatetime(), new Date());
            log.error("i数值：" + i);
            // 大于1说明断了，没有连续登陆
            if (i > 1) {
                yaoLoginRule = yaoLoginRuleService.detailByDay(day);
                seriesNo = IdGeneratorUtil.generator();
            } else {
                // 连续登录
                log.error("seriesNo数值：" + seriesNo);
                day = yaoChangeRecordService.detailCountBySeriesNo(seriesNo);
                log.error("day数值：" + day);

                day = day + 1;
                yaoLoginRule = yaoLoginRuleService.detailByDay(day);
                if (null == yaoLoginRule) {
                    // 重新开始
                    day = 1;
                    yaoLoginRule = yaoLoginRuleService.detailByDay(day);
                    seriesNo = IdGeneratorUtil.generator();
                }
            }
        } else {
            // 没登录时间重新开始
            seriesNo = IdGeneratorUtil.generator();
            yaoLoginRule = yaoLoginRuleService.detailByDay(day);
        }

        if (null != yaoLoginRule) {
            BigDecimal extraYinYao = BigDecimal.ZERO;
            BigDecimal extraYangYao = BigDecimal.ZERO;
            log.error("yaoLoginDay:" + yaoLoginRule.getDay().intValue());
            log.error("day:" + day);

            if (yaoLoginRule.getDay().intValue() == day) {
                extraYinYao = yaoLoginRule.getExtraYinYao();
                extraYangYao = yaoLoginRule.getExtraYangYao();
            }
            String remark =
                    "连续登陆" + day + "天，固定赠送阴爻：" + yaoLoginRule.getYinYao().stripTrailingZeros().toPlainString() + "，阳爻："
                            + yaoLoginRule.getYangYao().stripTrailingZeros().toPlainString() + "，额外赠送阴爻：" + extraYinYao
                            .stripTrailingZeros().toPlainString() + "，额外赠送阳爻：" + extraYangYao.stripTrailingZeros()
                            .toPlainString();
            log.error("remark:" + remark);
            yaoChangeRecordService.create(operator.getId(), EUserKind.C.getCode(),
                    EJourBizTypeUser.YaoAccount.Task, EJourBizTypeUser.YaoAccount.LoginGet, yaoLoginRule.getId().toString(),
                    yaoLoginRule.getYinYao().add(extraYinYao),
                    yaoLoginRule.getYangYao().add(extraYangYao), new Date(), seriesNo, remark);

        } else {
            String remark =
                    "每日登陆赠送阴爻：" + yaoTaskConfig.getYinYao().stripTrailingZeros().toPlainString() + "，阳爻:" + yaoTaskConfig.getYangYao()
                            .stripTrailingZeros().toPlainString();
            yaoChangeRecordService.create(operator.getId(), EUserKind.C.getCode(),
                    EJourBizTypeUser.YaoAccount.Task, EJourBizTypeUser.YaoAccount.LoginGet, yaoTaskConfig.getId().toString(),
                    yaoTaskConfig.getYinYao(), yaoTaskConfig.getYangYao(), new Date(), seriesNo, remark);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createEntryMetaUniverse(User operator) {
        if (EUserYaoFlag.E_USER_YAO_FLAG_0.getCode().equals(operator.getYaoFlag())) {
            return;
        }
        YaoTaskConfig yaoTaskConfig = detailByType(EYaoTaskConfigType.YAO_TASK_CONFIG_TYPE_1.getCode());
        if (null == yaoTaskConfig || !EYaoTaskConfigStatus.YAO_TASK_CONFIG_STATUS_1.getCode().equals(yaoTaskConfig.getStatus())) {
            return;
        }

        YaoChangeRecord yaoChangeRecord = yaoChangeRecordService
                .detailDayLoginRecord(operator, new Date(), EJourBizTypeUser.YaoAccount.Task.getCode(),
                        EJourBizTypeUser.YaoAccount.EntryMetaUniverse.getCode());

        if (null != yaoChangeRecord) {
            return;
        }

        UserEntryRecord userEntryRecord = userEntryRecordService.detailTodayFirstTime(operator.getId());
        if (null == userEntryRecord || DateUtil.minuteBetween(userEntryRecord.getCreateDatetime(), new Date()) < yaoTaskConfig
                .getOnlineDuration()) {
            return;
        }

        yaoChangeRecordService.create(operator.getId(), EUserKind.C.getCode(),
                EJourBizTypeUser.YaoAccount.Task, EJourBizTypeUser.YaoAccount.EntryMetaUniverse, userEntryRecord.getId().toString(),
                yaoTaskConfig.getYinYao(), yaoTaskConfig.getYangYao(), new Date(), null, null);

    }

    @Override
    public void createPeriodBuy(User operator, Long refId) {
        if (EUserYaoFlag.E_USER_YAO_FLAG_0.getCode().equals(operator.
                getYaoFlag())) {
            return;
        }
        YaoTaskConfig yaoTaskConfig = detailByType(EYaoTaskConfigType.YAO_TASK_CONFIG_TYPE_2.getCode());
        if (null == yaoTaskConfig || !EYaoTaskConfigStatus.YAO_TASK_CONFIG_STATUS_1.getCode().equals(yaoTaskConfig.getStatus())) {
            return;
        }

        yaoChangeRecordService.create(operator.getId(), EUserKind.C.getCode(),
                EJourBizTypeUser.YaoAccount.Task, EJourBizTypeUser.YaoAccount.PeriodBuyGet, refId.toString(), yaoTaskConfig.getYinYao(),
                yaoTaskConfig.getYangYao(), new Date(), null, null);

    }

    @Override
    public YaoTaskConfig detailByType(String type) {

        return yaoTaskConfigMapper.selectType(type);
    }

    /**
     * 删除爻获取任务配置
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        yaoTaskConfigMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改爻获取任务配置
     *
     * @param req 修改爻获取任务配置入参
     * @param operator 操作人
     */
    @Override
    public void modify(YaoTaskConfigModifyReq req, User operator) {
        YaoTaskConfig yaoTaskConfig = EntityUtils.copyData(req, YaoTaskConfig.class);
        yaoTaskConfig.setUpdater(operator.getId());
        yaoTaskConfig.setUpdaterName(operator.getLoginName());
        yaoTaskConfig.setUpdateDatetime(new Date());
        yaoTaskConfigMapper.updateByPrimaryKeySelective(yaoTaskConfig);
    }

    /**
     * 详情查询爻获取任务配置
     *
     * @param id 主键ID
     * @return 爻获取任务配置对象
     */
    @Override
    public YaoTaskConfig detail(Long id) {
        YaoTaskConfig yaoTaskConfig = yaoTaskConfigMapper.selectByPrimaryKey(id);
        if (null == yaoTaskConfig) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return yaoTaskConfig;
    }

    /**
     * 分页查询爻获取任务配置
     *
     * @param req 分页查询爻获取任务配置入参
     * @return 分页爻获取任务配置对象
     */
    @Override
    public List<YaoTaskConfig> page(YaoTaskConfigPageReq req) {
        YaoTaskConfig condition = EntityUtils.copyData(req, YaoTaskConfig.class);

        List<YaoTaskConfig> yaoTaskConfigList = yaoTaskConfigMapper.selectByCondition(condition);

        return yaoTaskConfigList;
    }

    /**
     * 列表查询爻获取任务配置
     *
     * @param req 列表查询爻获取任务配置入参
     * @return 列表爻获取任务配置对象
     */
    @Override
    public List<YaoTaskConfig> list(YaoTaskConfigListReq req) {
        YaoTaskConfig condition = EntityUtils.copyData(req, YaoTaskConfig.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), YaoTaskConfig.class));

        List<YaoTaskConfig> yaoTaskConfigList = yaoTaskConfigMapper.selectByCondition(condition);

        return yaoTaskConfigList;
    }

    /**
     * 前端详情查询爻获取任务配置
     *
     * @param id 主键ID
     * @return 爻获取任务配置对象
     */
    @Override
    public YaoTaskConfigDetailRes detailFront(Long id) {
        YaoTaskConfigDetailRes res = new YaoTaskConfigDetailRes();

        YaoTaskConfig yaoTaskConfig = yaoTaskConfigMapper.selectByPrimaryKey(id);
        if (null == yaoTaskConfig) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        BeanUtils.copyProperties(yaoTaskConfig, res);

        return res;
    }

    /**
     * 前端分页查询爻获取任务配置
     *
     * @param req 前端分页查询爻获取任务配置入参
     * @return 分页爻获取任务配置对象
     */
    @Override
    public List<YaoTaskConfigPageRes> pageFront(YaoTaskConfigPageFrontReq req) {
        YaoTaskConfig condition = EntityUtils.copyData(req, YaoTaskConfig.class);
        List<YaoTaskConfig> yaoTaskConfigList = yaoTaskConfigMapper.selectByCondition(condition);

        List<YaoTaskConfigPageRes> resList = yaoTaskConfigList.stream().map((entity) -> {
            YaoTaskConfigPageRes res = new YaoTaskConfigPageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(yaoTaskConfigList, resList);
    }

    /**
     * 前端列表查询爻获取任务配置
     *
     * @param req 前端列表查询爻获取任务配置入参
     * @return 列表爻获取任务配置对象
     */
    @Override
    public List<YaoTaskConfigListRes> listFront(YaoTaskConfigListFrontReq req) {
        YaoTaskConfig condition = EntityUtils.copyData(req, YaoTaskConfig.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), YaoTaskConfig.class));

        List<YaoTaskConfig> yaoTaskConfigList = yaoTaskConfigMapper.selectByCondition(condition);

        List<YaoTaskConfigListRes> resList = yaoTaskConfigList.stream().map((entity) -> {
            YaoTaskConfigListRes res = new YaoTaskConfigListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpDown(YaoTaskConfigBatchModifyReq request, User operator) {
        for (Long id : request.getIdList()) {
            YaoTaskConfig detail = detail(id);
            detail.setStatus(request.getStatus());
            detail.setUpdater(operator.getId());
            detail.setUpdaterName(operator.getLoginName());
            detail.setUpdateDatetime(new Date());
            yaoTaskConfigMapper.updateByPrimaryKeySelective(detail);
        }
    }

}