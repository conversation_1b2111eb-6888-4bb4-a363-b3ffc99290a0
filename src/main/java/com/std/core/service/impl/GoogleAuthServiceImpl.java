/**
 * @Title GoogleAuthBOImpl.java
 * @Package com.ogc.standard.bo.impl
 * @Description
 * <AUTHOR>
 * @date 2017年12月6日 下午4:55:49
 * @version V1.0
 */
package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.core.service.IGoogleAuthService;
import com.std.core.util.GoogleAuthenticator;
import org.springframework.stereotype.Service;

/**
 * @author: haiqingzheng
 * @since: 2017年12月6日 下午4:55:49
 * @history:
 */
@Service
public class GoogleAuthServiceImpl implements IGoogleAuthService {

    @Override
    public String generateSecretKey() {
        return GoogleAuthenticator.generateSecretKey();
    }

    @Override
    public void checkCode(String secret, String googleCaptcha, Long timeMsec) {
        GoogleAuthenticator ga = new GoogleAuthenticator();
        ga.setWindowSize(1); // should give 1 * 30 seconds of grace...
        boolean result = ga.check_code(secret, new Long(googleCaptcha), timeMsec);
        if (!result) {
            throw new com.std.common.exception.BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                    "google验证码错误");
        }
    }

}
