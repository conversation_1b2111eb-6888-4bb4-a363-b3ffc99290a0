package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.ECollectionStatus;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.ERobotConfigStatus;
import com.std.core.mapper.RobotConfigMapper;
import com.std.core.pojo.domain.Collection;
import com.std.core.pojo.domain.Dict;
import com.std.core.pojo.domain.RobotConfig;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.DictListReq;
import com.std.core.pojo.request.RobotConfigBatchModifyReq;
import com.std.core.pojo.request.RobotConfigCreateReq;
import com.std.core.pojo.request.RobotConfigListFrontReq;
import com.std.core.pojo.request.RobotConfigListReq;
import com.std.core.pojo.request.RobotConfigModifyReq;
import com.std.core.pojo.request.RobotConfigPageFrontReq;
import com.std.core.pojo.request.RobotConfigPageReq;
import com.std.core.pojo.response.RobotConfigDetailRes;
import com.std.core.pojo.response.RobotConfigListRes;
import com.std.core.pojo.response.RobotConfigPageRes;
import com.std.core.service.ICollectionService;
import com.std.core.service.IConfigService;
import com.std.core.service.IDictService;
import com.std.core.service.IRobotConfigService;
import com.std.core.util.SysConstants;
import com.std.core.util.SysConstantsDict;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 机器人等级配置ServiceImpl
 *
 * <AUTHOR> ycj
 * @since : 2022-08-29 10:08
 */
@Service
public class RobotConfigServiceImpl implements IRobotConfigService {

    @Resource
    private RobotConfigMapper robotConfigMapper;

    @Resource
    private ICollectionService collectionService;

    @Resource
    private IDictService dictService;

    @Resource
    private IConfigService configService;

    /**
     * 新增机器人等级配置
     *
     * @param req      新增机器人等级配置入参
     * @param operator 操作人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(RobotConfigCreateReq req, User operator) {
        RobotConfig robotConfig = EntityUtils.copyData(req, RobotConfig.class);
        Collection collection = collectionService.detailSimple(req.getCollectionId());
        if (!ECollectionStatus.COLLECTION_STATUS_1.getCode().equals(collection.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "作品还未上架");
        }

        // 机器人挖宝间隔时间
        Integer unitTime = configService.getIntegerValue(SysConstants.ROBOT_UNIT_TIME_POSITIVEINTEGER);
        if (req.getSendTime() % unitTime > 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "机器人不满足步长" + unitTime);
        }

        DictListReq dictReq = new DictListReq();
        dictReq.setParentKey(SysConstantsDict.META_ROBOT_LEVEL);
        List<Dict> dictList = dictService.listAll(dictReq);
        boolean levelFlag = true;
        for (Dict dict : dictList) {
            if (dict.getKey().equals(req.getLevel())) {
                levelFlag = false;
            }
        }

        if (levelFlag) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "错误等级");
        }
        robotConfig.setCoverFileUrl(collection.getCoverFileUrl());
        robotConfig.setCollectionName(collection.getName());
        robotConfig.setStatus(ERobotConfigStatus.ROBOT_CONFIG_STATUS_0.getCode());
        robotConfig.setCreater(operator.getId());
        robotConfig.setCreaterName(operator.getLoginName());
        robotConfig.setCreateDatetime(new Date());
        robotConfigMapper.insertSelective(robotConfig);
    }

    public static void main(String[] args) {
        System.out.println(3 % 3);
    }

    /**
     * 删除机器人等级配置
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        robotConfigMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改机器人等级配置
     *
     * @param req      修改机器人等级配置入参
     * @param operator 操作人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(RobotConfigModifyReq req, User operator) {
        RobotConfig config = detail(req.getId());
        if (!ERobotConfigStatus.ROBOT_CONFIG_STATUS_0.getCode().equals(config.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "配置使用中，不能修改");
        }
        RobotConfig robotConfig = EntityUtils.copyData(req, RobotConfig.class);
        Collection collection = collectionService.detailSimple(config.getCollectionId());
        if (!ECollectionStatus.COLLECTION_STATUS_1.getCode().equals(collection.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "作品还未上架");
        }

        // 机器人挖宝间隔时间
        Integer unitTime = configService.getIntegerValue(SysConstants.ROBOT_UNIT_TIME_POSITIVEINTEGER);
        if (req.getSendTime() % unitTime > 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), config.getCollectionName() + "机器人不符合步长" + unitTime);
        }

        DictListReq dictReq = new DictListReq();
        dictReq.setParentKey(SysConstantsDict.META_ROBOT_LEVEL);
        List<Dict> dictList = dictService.listAll(dictReq);
        boolean levelFlag = true;
        for (Dict dict : dictList) {
            if (dict.getKey().equals(req.getLevel())) {
                levelFlag = false;
            }
        }

        if (levelFlag) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "错误等级");
        }
        robotConfig.setCoverFileUrl(collection.getCoverFileUrl());
        robotConfig.setCollectionName(collection.getName());
        robotConfig.setUpdater(operator.getId());
        robotConfig.setUpdaterName(operator.getLoginName());
        robotConfig.setUpdateDatetime(new Date());
        robotConfigMapper.updateByPrimaryKeySelective(robotConfig);
    }

    /**
     * 修改机器人等级配置
     *
     * @param req      修改机器人等级配置入参
     * @param operator 操作人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpDown(RobotConfigBatchModifyReq req, User operator) {
        for (Long id : req.getIdList()) {
            RobotConfig robotConfig = detail(id);
            if (robotConfig.getStatus().equals(req.getStatus())) {
                Collection collection = collectionService.detailSimple(robotConfig.getCollectionId());
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "机器人" + collection.getName() + "配置状态错误");
            }

            if (ERobotConfigStatus.ROBOT_CONFIG_STATUS_1.getCode().equals(req.getStatus())){
                // 机器人挖宝间隔时间
                Integer unitTime = configService.getIntegerValue(SysConstants.ROBOT_UNIT_TIME_POSITIVEINTEGER);
                if (robotConfig.getSendTime() % unitTime > 0) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), robotConfig.getCollectionName() + "机器人不符合步长" + unitTime);
                }
            }


            robotConfig.setStatus(req.getStatus());
            robotConfig.setUpdater(operator.getId());
            robotConfig.setUpdaterName(operator.getLoginName());
            robotConfig.setUpdateDatetime(new Date());
            robotConfigMapper.updateByPrimaryKeySelective(robotConfig);
        }
    }

    /**
     * 详情查询机器人等级配置
     *
     * @param id 主键ID
     * @return 机器人等级配置对象
     */
    @Override
    public RobotConfig detail(Long id) {
        RobotConfig robotConfig = robotConfigMapper.selectByPrimaryKey(id);
        if (null == robotConfig) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return robotConfig;
    }

    /**
     * 分页查询机器人等级配置
     *
     * @param req 分页查询机器人等级配置入参
     * @return 分页机器人等级配置对象
     */
    @Override
    public List<RobotConfig> page(RobotConfigPageReq req) {
        RobotConfig condition = EntityUtils.copyData(req, RobotConfig.class);

        List<RobotConfig> robotConfigList = robotConfigMapper.selectByCondition(condition);

        return robotConfigList;
    }

    /**
     * 列表查询机器人等级配置
     *
     * @param req 列表查询机器人等级配置入参
     * @return 列表机器人等级配置对象
     */
    @Override
    public List<RobotConfig> list(RobotConfigListReq req) {
        RobotConfig condition = EntityUtils.copyData(req, RobotConfig.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), RobotConfig.class));

        List<RobotConfig> robotConfigList = robotConfigMapper.selectByCondition(condition);

        return robotConfigList;
    }

    /**
     * 前端详情查询机器人等级配置
     *
     * @param id 主键ID
     * @return 机器人等级配置对象
     */
    @Override
    public RobotConfigDetailRes detailFront(Long id) {
        RobotConfigDetailRes res = new RobotConfigDetailRes();

        RobotConfig robotConfig = robotConfigMapper.selectByPrimaryKey(id);
        if (null == robotConfig) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        BeanUtils.copyProperties(robotConfig, res);

        return res;
    }

    /**
     * 前端分页查询机器人等级配置
     *
     * @param req 前端分页查询机器人等级配置入参
     * @return 分页机器人等级配置对象
     */
    @Override
    public List<RobotConfigPageRes> pageFront(RobotConfigPageFrontReq req) {
        RobotConfig condition = EntityUtils.copyData(req, RobotConfig.class);
        List<RobotConfig> robotConfigList = robotConfigMapper.selectByCondition(condition);

        List<RobotConfigPageRes> resList = robotConfigList.stream().map((entity) -> {
            RobotConfigPageRes res = new RobotConfigPageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(robotConfigList, resList);
    }

    /**
     * 前端列表查询机器人等级配置
     *
     * @param req 前端列表查询机器人等级配置入参
     * @return 列表机器人等级配置对象
     */
    @Override
    public List<RobotConfigListRes> listFront(RobotConfigListFrontReq req) {
        RobotConfig condition = EntityUtils.copyData(req, RobotConfig.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), RobotConfig.class));

        List<RobotConfig> robotConfigList = robotConfigMapper.selectByCondition(condition);

        List<RobotConfigListRes> resList = robotConfigList.stream().map((entity) -> {
            RobotConfigListRes res = new RobotConfigListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

    @Override
    public RobotConfig detailByCollectionId(Long collectionId) {
        RobotConfig robotConfig = robotConfigMapper.selectByCollectionId(collectionId);
        if (null == robotConfig) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), collectionId);
        }

        return robotConfig;
    }

}