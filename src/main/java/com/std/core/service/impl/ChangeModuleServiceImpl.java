package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.core.enums.EBoolean;
import com.std.core.enums.EChangeModuleStatus;
import com.std.core.enums.EErrorCode;
import com.std.core.mapper.ChangeModuleMapper;
import com.std.core.pojo.domain.ChangeModule;
import com.std.core.pojo.domain.ChangeRecommendModule;
import com.std.core.pojo.domain.ChangeType;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.ChangeModuleAmountListRes;
import com.std.core.pojo.response.ChangeModuleDetailRes;
import com.std.core.pojo.response.ChangeModuleListRes;
import com.std.core.pojo.response.ChangeModulePageRes;
import com.std.core.pojo.response.ChangeModuleTotalAmountRes;
import com.std.core.service.IChangeModuleService;
import com.std.core.service.IChangeRecommendService;
import com.std.core.service.IChangeSeriesService;
import com.std.core.service.IChangeTypeService;
import com.std.core.service.IConfigService;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * 百变大咖组件ServiceImpl
 *
 * <AUTHOR> ycj
 * @since : 2021-10-25 17:11
 */
@Service
public class ChangeModuleServiceImpl implements IChangeModuleService {

    @Resource
    private ChangeModuleMapper changeModuleMapper;

    @Resource
    private IChangeTypeService changeTypeService;

    @Resource
    private IConfigService configService;

    @Resource
    private IChangeRecommendService changeRecommendService;

    @Resource
    private IChangeSeriesService changeSeriesService;

    /**
     * 新增百变大咖组件
     *
     * @param req      新增百变大咖组件入参
     * @param operator 操作人
     */
    @Override
    public void create(ChangeModuleCreateReq req, User operator) {
        ChangeModule changeModule = EntityUtils.copyData(req, ChangeModule.class);

        if (changeModule.getDiscountPrice().compareTo(changeModule.getPrice()) > 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "折扣价不能大于原价");
        }
        ChangeType changeType = changeTypeService.detail(req.getTypeId());
        changeModule.setSeriesId(changeType.getSeriesId());
        changeModule.setStatus(EChangeModuleStatus.CHANGE_MODULE_STATUS_0.getCode());
        changeModule.setCreater(operator.getId());
        changeModule.setCreateName(operator.getLoginName());
        changeModule.setCreateDatetime(new Date());

        changeModuleMapper.insertSelective(changeModule);
    }

    /**
     * 删除百变大咖组件
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        changeModuleMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改百变大咖组件
     *
     * @param req      修改百变大咖组件入参
     * @param operator 操作人
     */
    @Override
    public void modify(ChangeModuleModifyReq req, User operator) {
        detail(req.getId());
        ChangeModule changeModule = EntityUtils.copyData(req, ChangeModule.class);
        if (changeModule.getDiscountPrice().compareTo(changeModule.getPrice()) > 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "折扣价不能大于原价");
        }

        changeModule.setUpdater(operator.getId());
        changeModule.setUpdaterName(operator.getLoginName());
        changeModule.setUpdateDatetime(new Date());
        changeModuleMapper.updateByPrimaryKeySelective(changeModule);
    }

    /**
     * 详情查询百变大咖组件
     *
     * @param id 主键ID
     * @return 百变大咖组件对象
     */
    @Override
    public ChangeModule detail(Long id) {
        ChangeModule changeModule = changeModuleMapper.selectByPrimaryKey(id);
        if (null == changeModule) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return changeModule;
    }

    /**
     * 分页查询百变大咖组件
     *
     * @param req 分页查询百变大咖组件入参
     * @return 分页百变大咖组件对象
     */
    @Override
    public List<ChangeModule> page(ChangeModulePageReq req) {
        ChangeModule condition = EntityUtils.copyData(req, ChangeModule.class);

        List<ChangeModule> changeModuleList = changeModuleMapper.selectByCondition(condition);

        return changeModuleList;
    }

    /**
     * 列表查询百变大咖组件
     *
     * @param req 列表查询百变大咖组件入参
     * @return 列表百变大咖组件对象
     */
    @Override
    public List<ChangeModule> list(ChangeModuleListReq req) {
        ChangeModule condition = EntityUtils.copyData(req, ChangeModule.class);
        condition.setOrderBy("t.order_no");
//        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), ChangeModule.class));

        List<ChangeModule> changeModuleList = changeModuleMapper.selectByCondition(condition);

        return changeModuleList;
    }

    /**
     * 前端详情查询百变大咖组件
     *
     * @param id 主键ID
     * @return 百变大咖组件对象
     */
    @Override
    public ChangeModuleDetailRes detailFront(Long id) {
        ChangeModuleDetailRes res = new ChangeModuleDetailRes();

        ChangeModule changeModule = changeModuleMapper.selectByPrimaryKey(id);
        if (null == changeModule) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        BeanUtils.copyProperties(changeModule, res);

        return res;
    }

    /**
     * 前端分页查询百变大咖组件
     *
     * @param req 前端分页查询百变大咖组件入参
     * @return 分页百变大咖组件对象
     */
    @Override
    public List<ChangeModulePageRes> pageFront(ChangeModulePageFrontReq req) {
        ChangeModule condition = EntityUtils.copyData(req, ChangeModule.class);
        condition.setOrderBy("t.order_no");
        condition.setStatus(EChangeModuleStatus.CHANGE_MODULE_STATUS_1.getCode());
        List<ChangeModule> changeModuleList = changeModuleMapper.selectByCondition(condition);

        List<ChangeModulePageRes> resList = changeModuleList.stream().map((entity) -> {
            ChangeModulePageRes res = new ChangeModulePageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(changeModuleList, resList);
    }

    /**
     * 前端列表查询百变大咖组件
     *
     * @param req 前端列表查询百变大咖组件入参
     * @return 列表百变大咖组件对象
     */
    @Override
    public List<ChangeModuleListRes> listFront(ChangeModuleListFrontReq req) {
        ChangeModule condition = EntityUtils.copyData(req, ChangeModule.class);
        condition.setOrderBy("t.order_no");
        condition.setStatus(EChangeModuleStatus.CHANGE_MODULE_STATUS_1.getCode());
        List<ChangeModule> changeModuleList = changeModuleMapper.selectByCondition(condition);

        List<ChangeModuleListRes> resList = changeModuleList.stream().map((entity) -> {
            ChangeModuleListRes res = new ChangeModuleListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

    /**
     * 批量新增
     */
    @Override
    public void batchCreate(ChangeModuleBatchCreateReq request, User operator) {
        List<ChangeModule> changeModuleList = new ArrayList<>();
        for (ChangeModuleCreateReq req : request.getReqList()) {
            ChangeModule changeModule = EntityUtils.copyData(req, ChangeModule.class);
            changeTypeService.detail(req.getTypeId());
            changeModule.setStatus(EChangeModuleStatus.CHANGE_MODULE_STATUS_0.getCode());
            changeModule.setCreater(operator.getId());
            changeModule.setCreateName(operator.getLoginName());
            changeModule.setCreateDatetime(new Date());

            changeModuleList.add(changeModule);
        }

        if (CollectionUtils.isNotEmpty(changeModuleList)) {
            changeModuleMapper.batchAdd(changeModuleList);
        }

    }

    /**
     * 批量上下架
     */
    @Override
    public void batchUpAndDown(ChangeModuleUpAndDownReq request, User operator) {
        List<ChangeModule> changeModuleList = new ArrayList<>();
        for (Long id : request.getIdList()) {
            ChangeModule module = detail(id);

            //
            if (EChangeModuleStatus.CHANGE_MODULE_STATUS_2.getCode().equals(request.getStatus()) && module.getStatus()
                    .equals(EChangeModuleStatus.CHANGE_MODULE_STATUS_0.getCode())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "待上架的类型不能下架");
            }

            module.setStatus(request.getStatus());
            module.setUpdater(operator.getId());
            module.setUpdaterName(operator.getLoginName());
            module.setUpdateDatetime(new Date());
            changeModuleList.add(module);
        }

        changeModuleMapper.batchUpdate(changeModuleList);

        // 把下架的组件的关联推荐也下架
        if (request.getStatus().equals(EChangeModuleStatus.CHANGE_MODULE_STATUS_2.getCode())) {
            ChangeRecommendModule module = new ChangeRecommendModule();
            module.setModuleIdList(request.getIdList());
            changeRecommendService.batchDown(module);
        }
    }

    @Override
    public List<ChangeModule> detailByRecommendId(Long recommendId) {
        return changeModuleMapper.detailByRecommendId(recommendId);
    }

    @Override
    public List<ChangeModule> detailDefaultByRecommendId(Long seriesId) {
        ChangeModule condition = new ChangeModule();
        condition.setSeriesId(seriesId);
        condition.setDefaultFlag(EBoolean.YES.getCode());
        return changeModuleMapper.selectByCondition(condition);
    }

    /**
     * 根据推荐获取组件总价
     */
    @Override
    public BigDecimal getTotalPriceByRecommendId(Long recommendId) {
        return changeModuleMapper.getTotalPriceByRecommendId(recommendId);
    }

    /**
     * 查询NFT组件总价
     */
    @Override
    public ChangeModuleTotalAmountRes detailFrontTotalPrice(ChangeModuleTotalAmountReq request) {
        ChangeModule module = new ChangeModule();
        module.setIdList(request.getIdList());
        ChangeModuleTotalAmountRes res = changeModuleMapper.detailFrontTotalPrice(module);

        BigDecimal couponAmount = res.getOldAmount().subtract(res.getAmount());
        res.setCouponAmount(couponAmount);

        BigDecimal changeFee = configService.getBigDecimalValue("change_fee");
        res.setChainAmount(changeFee);

        BigDecimal totalAmount = res.getAmount().add(changeFee);
        res.setTotalAmount(totalAmount);

        return res;
    }

    @Override
    public List<ChangeModuleAmountListRes> listFrontAmount(ChangeModuleTotalAmountReq request) {
        ChangeModule module = new ChangeModule();
        module.setIdList(request.getIdList());
        List<ChangeModuleAmountListRes> resList = changeModuleMapper.listFrontAmount(module);

        return resList;
    }

    /**
     * 设置默认组件
     *
     * @param request
     * @param operator
     */
    @Override
    public void setDefault(ChangeModuleSetDefaultReq request, User operator) {
        Long seriesId = null;
        List<ChangeModule> changeModuleList = new ArrayList<>();

        if (EBoolean.YES.getCode().equals(request.getType())) {

            Map<Long, ChangeModule> moduleMap = new HashMap<>();
            for (Long id : request.getIdList()) {
                ChangeModule changeModule = detail(id);

                // 判断是否是同一系列
                if (null == seriesId) {
                    seriesId = changeModule.getSeriesId();
                }

                if (!seriesId.equals(changeModule.getSeriesId())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请选择同一系列的组件");
                }

                ChangeModule module = moduleMap.get(changeModule.getTypeId());

                if (null == module) {
                    moduleMap.put(changeModule.getTypeId(), changeModule);
                } else {
                    ChangeType changeType = changeTypeService.detail(changeModule.getTypeId());
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "本次选择的默认组件里" + changeType.getName() + "类型重复");
                }

                changeModule.setDefaultFlag(EBoolean.YES.getCode());
                changeModuleList.add(changeModule);
            }
            List<ChangeModule> defaultModuleList = detailDefaultByRecommendId(seriesId);

            for (ChangeModule changeModule : defaultModuleList) {
                ChangeModule module = moduleMap.get(changeModule.getTypeId());

                if (null == module) {
                    moduleMap.put(changeModule.getTypeId(), changeModule);
                } else {
                    ChangeType changeType = changeTypeService.detail(changeModule.getTypeId());
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "本次选择的默认组件与原默认组件" + changeType.getName() + "类型重复");
                }
            }
        } else if (EBoolean.NO.getCode().equals(request.getType())) {
            for (Long id : request.getIdList()) {
                ChangeModule changeModule = detail(id);

                // 判断是否是同一系列
                if (null == seriesId) {
                    seriesId = changeModule.getSeriesId();
                }

                if (!seriesId.equals(changeModule.getSeriesId())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请选择同一系列的组件");
                }
                changeModule.setDefaultFlag(EBoolean.NO.getCode());
                changeModuleList.add(changeModule);
            }
        } else {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "类型错误");
        }

        changeModuleMapper.batchUpdate(changeModuleList);
    }

}