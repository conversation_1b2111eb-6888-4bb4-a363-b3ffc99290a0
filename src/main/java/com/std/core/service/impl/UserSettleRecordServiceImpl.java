package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.DateUtil;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.config.YeePayBankConfig;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.ELockBizType;
import com.std.core.enums.EUserSettleRecordStatus;
import com.std.core.mapper.UserSettleRecordMapper;
import com.std.core.pojo.domain.*;
import com.std.core.pojo.request.UserSettleRecordCreateReq;
import com.std.core.pojo.request.UserSettleRecordListFrontReq;
import com.std.core.pojo.request.UserSettleRecordListReq;
import com.std.core.pojo.request.UserSettleRecordModifyReq;
import com.std.core.pojo.request.UserSettleRecordPageReq;
import com.std.core.pojo.response.UserSettleRecordDetailRes;
import com.std.core.pojo.response.UserSettleRecordListRes;
import com.std.core.pojo.response.UserSettleRecordPageRes;
import com.std.core.service.ILockService;
import com.std.core.service.IUserPaymentAccountInfoService;
import com.std.core.service.IUserService;
import com.std.core.service.IUserSettleAccountService;
import com.std.core.service.IUserSettleRecordService;
import com.std.core.service.*;
import com.yeepay.yop.sdk.service.settle.model.RecordsQuerySettleRecordQueryDtoResult;
import com.yeepay.yop.sdk.service.settle.response.RecordsQueryResponse;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户结算记录ServiceImpl
 *
 * <AUTHOR> ycj
 * @since : 2022-04-21 15:32
 */
@Service
@Slf4j
public class UserSettleRecordServiceImpl implements IUserSettleRecordService {

    @Resource
    private UserSettleRecordMapper userSettleRecordMapper;

    @Resource
    private IUserService userService;

    @Resource
    private IUserPaymentAccountInfoService userPaymentAccountInfoService;

    @Resource
    private ILockService lockService;

    @Resource
    private IYeePayService yeePayService;

    @Resource
    private IUserSettleAccountService userSettleAccountService;

    @Resource
    private YeePayBankConfig yeePayBankConfig;

    /**
     * 新增用户结算记录
     *
     * @param req 新增用户结算记录入参
     * @param operator 操作人
     */
    @Override
    public void create(UserSettleRecordCreateReq req, User operator) {
        UserSettleRecord userSettleRecord = EntityUtils.copyData(req, UserSettleRecord.class);
        userSettleRecordMapper.insertSelective(userSettleRecord);
    }

    /**
     * 删除用户结算记录
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        userSettleRecordMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改用户结算记录
     *
     * @param req 修改用户结算记录入参
     * @param operator 操作人
     */
    @Override
    public void modify(UserSettleRecordModifyReq req, User operator) {
        UserSettleRecord userSettleRecord = EntityUtils.copyData(req, UserSettleRecord.class);
        userSettleRecordMapper.updateByPrimaryKeySelective(userSettleRecord);
    }

    /**
     * 详情查询用户结算记录
     *
     * @param id 主键ID
     * @return 用户结算记录对象
     */
    @Override
    public UserSettleRecord detail(Long id) {
        UserSettleRecord userSettleRecord = userSettleRecordMapper.selectByPrimaryKey(id);
        if (null == userSettleRecord) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        // 转译UserId
        userSettleRecord.setUser(userService.selectSummaryInfo(userSettleRecord.getUserId()));

        return userSettleRecord;
    }

    /**
     * 分页查询用户结算记录
     *
     * @param req 分页查询用户结算记录入参
     * @return 分页用户结算记录对象
     */
    @Override
    public List<UserSettleRecord> page(UserSettleRecordPageReq req) {
        UserSettleRecord condition = EntityUtils.copyData(req, UserSettleRecord.class);

        List<UserSettleRecord> userSettleRecordList = userSettleRecordMapper.selectByCondition(condition);
        // 转译UserId
        userSettleRecordList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        return userSettleRecordList;
    }

    /**
     * 列表查询用户结算记录
     *
     * @param req 列表查询用户结算记录入参
     * @return 列表用户结算记录对象
     */
    @Override
    public List<UserSettleRecord> list(UserSettleRecordListReq req) {
        UserSettleRecord condition = EntityUtils.copyData(req, UserSettleRecord.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), UserSettleRecord.class));

        List<UserSettleRecord> userSettleRecordList = userSettleRecordMapper.selectByCondition(condition);
        // 转译UserId
        userSettleRecordList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        return userSettleRecordList;
    }

    /**
     * 前端详情查询用户结算记录
     *
     * @param id 主键ID
     * @return 用户结算记录对象
     */
    @Override
    public UserSettleRecordDetailRes detailFront(Long id) {
        UserSettleRecordDetailRes res = new UserSettleRecordDetailRes();

        UserSettleRecord userSettleRecord = userSettleRecordMapper.selectByPrimaryKey(id);
        if (null == userSettleRecord) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        // 转译UserId
        userSettleRecord.setUser(userService.selectSummaryInfo(userSettleRecord.getUserId()));

        BeanUtils.copyProperties(userSettleRecord, res);

        return res;
    }

    /**
     * 前端分页查询用户结算记录
     *
     * @return 分页用户结算记录对象
     */
    @Override
    public List<UserSettleRecordPageRes> pageFront(User operator) {
        UserSettleRecord condition = new UserSettleRecord();
        condition.setUserId(operator.getId());
        condition.setOrderBy("t.id desc");
        List<UserSettleRecord> userSettleRecordList = userSettleRecordMapper.selectByCondition(condition);
        // 转译UserId
        userSettleRecordList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        List<UserSettleRecordPageRes> resList = userSettleRecordList.stream().map((entity) -> {
            UserSettleRecordPageRes res = new UserSettleRecordPageRes();
            BeanUtils.copyProperties(entity, res);

            return res;
        }).collect(Collectors.toList());

        UserPaymentAccountInfo userInfo = new UserPaymentAccountInfo();
        if (CollectionUtils.isNotEmpty(resList)) {
            userInfo = userPaymentAccountInfoService.detailByUser(operator.getId());
        }
        for (UserSettleRecordPageRes res : resList) {
            String cardNo = userInfo.getSettleCardNo();
            res.setSettleBankInfo(userInfo.getSettleBankName().concat(cardNo.substring(cardNo.length() - 4, cardNo.length())));
        }
        return PageInfoUtil.listToPage(userSettleRecordList, resList);
    }

    /**
     * 前端列表查询用户结算记录
     *
     * @param req 前端列表查询用户结算记录入参
     * @return 列表用户结算记录对象
     */
    @Override
    public List<UserSettleRecordListRes> listFront(UserSettleRecordListFrontReq req) {
        UserSettleRecord condition = EntityUtils.copyData(req, UserSettleRecord.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), UserSettleRecord.class));

        List<UserSettleRecord> userSettleRecordList = userSettleRecordMapper.selectByCondition(condition);
        // 转译UserId
        userSettleRecordList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        List<UserSettleRecordListRes> resList = userSettleRecordList.stream().map((entity) -> {
            UserSettleRecordListRes res = new UserSettleRecordListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void synchronousSettleRecord(UserPaymentAccountInfo userInfo, Date newDate) {
        String date = DateUtil.dateToStr(newDate, DateUtil.DATA_TIME_PATTERN_9);
        String refCode = date + userInfo.getUserId();

        try {
            lockService.create(ELockBizType.YEEPAY_SETTLE_RECORD.getCode(), refCode);
        } catch (Exception e) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "用户每日结算重复");
        }

        String startDate = DateUtil.dateToStr(DateUtil.getStartDatetime(date), DateUtil.DATA_TIME_PATTERN_1);

        String endDate = DateUtil.dateToStr(newDate, DateUtil.DATA_TIME_PATTERN_1);

        RecordsQueryResponse response = new RecordsQueryResponse();

        int i = 0;
        while (true) {
            i++;
            if (i>=20){
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),"用户易宝每日结算,访问超时，结算编号:"+ userInfo.getId() );
            }
            try {
                response = yeePayService.recordsQueryResult(userInfo.getMerchantNo(), startDate, endDate);
                break;
            } catch (Exception e) {
                log.error("用户易宝每日结算，三方调用失败,结算编号：" + userInfo.getId() + "当前次数:" + i + "原因：" + e.getMessage());
            }
        }

        if ("000000".equals(response.getResult().getCode())) {
            List<RecordsQuerySettleRecordQueryDtoResult> recordList = response.getResult().getSettleRecordQueryDtos();
            RecordsQuerySettleRecordQueryDtoResult record = recordList.get(0);

            UserSettleAccount account = userSettleAccountService.getAccount(userInfo);
            UserSettleRecord settleRecord = new UserSettleRecord();
            settleRecord.setUserId(userInfo.getUserId());
            settleRecord.setSettleAccountId(account.getId());
            settleRecord.setAmount(record.getRealAmount());
            settleRecord.setStatus(EUserSettleRecordStatus.USER_SETTLE_RECORD_STATUS_1.getCode());
            settleRecord.setSettleDatetime(DateUtil.strToDate(record.getCreateTime(), DateUtil.DATA_TIME_PATTERN_1));
            settleRecord.setCreateDatetime(new Date());
            userSettleRecordMapper.insertSelective(settleRecord);

            userSettleAccountService.addSettleAmount(account, settleRecord.getAmount());
        }

    }


}