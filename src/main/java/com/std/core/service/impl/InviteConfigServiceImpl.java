package com.std.core.service.impl;

import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EErrorCode;
import com.std.core.mapper.InviteConfigMapper;
import com.std.core.pojo.domain.InviteConfig;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.InviteConfigCreateReq;
import com.std.core.pojo.request.InviteConfigListReq;
import com.std.core.pojo.request.InviteConfigModifyReq;
import com.std.core.pojo.request.InviteConfigPageReq;
import com.std.core.service.IInviteConfigService;

import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * 邀请配置ServiceImpl
 *
 * <AUTHOR> Leo
 * @since : 2020-06-07 03:54
 */
@Service
public class InviteConfigServiceImpl implements IInviteConfigService {

    @Resource
    private InviteConfigMapper inviteConfigMapper;

    /**
     * 新增邀请配置
     *
     * @param req 新增邀请配置入参
     * @param operator 操作人
     */
    @Override
    public void create(InviteConfigCreateReq req, User operator) {
        InviteConfig inviteConfig = EntityUtils.copyData(req, InviteConfig.class);
        inviteConfigMapper.insertSelective(inviteConfig);
    }

    /**
     * 删除邀请配置
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        inviteConfigMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改邀请配置
     *
     * @param req 修改邀请配置入参
     * @param operator 操作人
     */
    @Override
    public void modify(InviteConfigModifyReq req, User operator) {
        InviteConfig inviteConfig = EntityUtils.copyData(req, InviteConfig.class);
        inviteConfig.setUpdater(operator.getId());
        inviteConfig.setUpdaterName(operator.getLoginName());
        inviteConfig.setUpdateTime(new Date());
        inviteConfigMapper.updateByPrimaryKeySelective(inviteConfig);
    }

    /**
     * 详情查询邀请配置
     *
     * @param id 主键ID
     * @return 邀请配置对象
     */
    @Override
    public InviteConfig detail(Long id) {
        InviteConfig inviteConfig = inviteConfigMapper.selectByPrimaryKey(id);
        if (null == inviteConfig) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return inviteConfig;
    }

    /**
     * 分页查询邀请配置
     *
     * @param req 分页查询邀请配置入参
     * @return 分页邀请配置对象
     */
    @Override
    public List<InviteConfig> page(InviteConfigPageReq req) {
        InviteConfig condition = EntityUtils.copyData(req, InviteConfig.class);

        return inviteConfigMapper.selectByCondition(condition);
    }

    /**
     * 列表查询邀请配置
     *
     * @param req 列表查询邀请配置入参
     * @return 列表邀请配置对象
     */
    @Override
    public List<InviteConfig> list(InviteConfigListReq req) {
        InviteConfig condition = EntityUtils.copyData(req, InviteConfig.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), InviteConfig.class));

        return inviteConfigMapper.selectByCondition(condition);
    }

    @Override
    public List<InviteConfig> selectConfigList() {
        InviteConfig condition = new InviteConfig();
        condition.setOrderBy("t.level asc");
        return inviteConfigMapper.selectByCondition(condition);
    }

    @Override
    public InviteConfig selectConfig(String userLevel, Integer level) {
        InviteConfig condition = new InviteConfig();
        condition.setUserLevel(userLevel);
        condition.setLevel(level);
        List<InviteConfig> inviteConfigs = inviteConfigMapper.selectByCondition(condition);
        if (CollectionUtils.isNotEmpty(inviteConfigs)) {
            return inviteConfigs.get(0);
        }
        return null;
    }

}