package com.std.core.service;

import com.std.core.pojo.domain.HelpCategory;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.HelpCategoryCreateReq;
import com.std.core.pojo.request.HelpCategoryListReq;
import com.std.core.pojo.request.HelpCategoryModifyReq;
import com.std.core.pojo.request.HelpCategoryPageReq;

import java.util.List;

/**
 * 帮助中心文章类型Service
 *
 * <AUTHOR> ycj
 * @since : 2020-11-09 13:28
 */
public interface IHelpCategoryService {

    /**
     * 新增帮助中心文章类型
     *
     * @param req      新增帮助中心文章类型入参
     * @param operator 操作人
     */
    void create(HelpCategoryCreateReq req, User operator);

    /**
     * 删除帮助中心文章类型
     *
     * @param id 主键ID
     */
    void remove(Long id);

    /**
     * 修改帮助中心文章类型
     *
     * @param req      修改帮助中心文章类型入参
     * @param operator 操作人
     */
    void modify(HelpCategoryModifyReq req, User operator);

    /**
     * 详情查询帮助中心文章类型
     *
     * @param id 主键ID
     * @return 帮助中心文章类型详情数据
     */
    HelpCategory detail(Long id);

    /**
     * 分页查询帮助中心文章类型
     *
     * @param req 分页查询帮助中心文章类型入参
     * @return 帮助中心文章类型分页数据
     */
    List<HelpCategory> page(HelpCategoryPageReq req);

    /**
     * 列表查询帮助中心文章类型
     *
     * @param req 列表查询帮助中心文章类型入参
     * @return 帮助中心文章类型列表数据
     */
    List<HelpCategory> list(HelpCategoryListReq req);

//    appPage();
}