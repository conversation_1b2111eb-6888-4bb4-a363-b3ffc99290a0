package com.std.core.service;

import com.std.core.enums.EJourBizTypeSettle;
import com.std.core.pojo.domain.UserPaymentAccountInfo;
import com.std.core.pojo.domain.UserSettleAccount;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.UserSettleAccountCreateReq;
import com.std.core.pojo.request.UserSettleAccountListReq;
import com.std.core.pojo.request.UserSettleAccountListFrontReq;
import com.std.core.pojo.request.UserSettleAccountModifyReq;
import com.std.core.pojo.request.UserSettleAccountPageReq;
import com.std.core.pojo.request.UserSettleAccountPageFrontReq;
import com.std.core.pojo.response.UserSettleAccountDetailRes;
import com.std.core.pojo.response.UserSettleAccountListRes;
import com.std.core.pojo.response.UserSettleAccountPageRes;

import java.math.BigDecimal;
import java.util.List;

/**
 * 用户结算账户Service
 *
 * <AUTHOR> ycj
 * @since : 2022-04-21 15:49
 */
public interface IUserSettleAccountService {

    /**
     * 新增用户结算账户
     *
     * @param req      新增用户结算账户入参
     * @param operator 操作人
     */
    void create(UserSettleAccountCreateReq req, User operator);

    /**
     * 删除用户结算账户
     *
     * @param id 主键ID
     */
    void remove(Long id);

    /**
     * 修改用户结算账户
     *
     * @param req      修改用户结算账户入参
     * @param operator 操作人
     */
    void modify(UserSettleAccountModifyReq req, User operator);

    /**
     * 详情查询用户结算账户
     *
     * @param id 主键ID
     * @return 用户结算账户详情数据
     */
    UserSettleAccount detail(Long id);

    UserSettleAccount detailForUpdate(Long id);

    /**
     * 分页查询用户结算账户
     *
     * @param req 分页查询用户结算账户入参
     * @return 用户结算账户分页数据
     */
    List<UserSettleAccount> page(UserSettleAccountPageReq req);
    List<UserSettleAccount> pageCompany(UserSettleAccountPageReq request);
    /**
     * 列表查询用户结算账户
     *
     * @param req 列表查询用户结算账户入参
     * @return 用户结算账户列表数据
     */
    List<UserSettleAccount> list(UserSettleAccountListReq req);

    /**
     * 前端详情查询用户结算账户
     *
     * @param id 主键ID
     * @return 用户结算账户详情数据
     */
    UserSettleAccountDetailRes detailFront(Long id);

    /**
     * 前端分页查询用户结算账户
     *
     * @param req 分页查询用户结算账户入参
     * @return 用户结算账户分页数据
     */
    List<UserSettleAccountPageRes> pageFront(UserSettleAccountPageFrontReq req);

    /**
     * 前端列表查询用户结算账户
     *
     * @param req 列表查询用户结算账户入参
     * @return 用户结算账户列表数据
     */
    List<UserSettleAccountListRes> listFront(UserSettleAccountListFrontReq req);

    /**
     * 增加用户结算账户代结算金额
     *
     * @param
     * @param amount
     */
    void addToSettleAmount(UserSettleAccount settleAccount, BigDecimal amount, String refId, String bizType, String bizNote, String remark);

    void addToSettleAmount(UserSettleAccount settleAccount, BigDecimal amount);

    void addOnSettleAmount(UserSettleAccount settleAccount, BigDecimal amount, String refId, String bizType, String bizNote, String remark);

    void subtractOnSettleAmount(UserSettleAccount settleAccount, BigDecimal amount, String refId, String bizType, String bizNote, String remark);

    void subtractOnToSettleAmount(UserSettleAccount settleAccount, BigDecimal amount, String refId, String bizType, String bizNote, String remark);

    UserSettleAccount getAccount(UserPaymentAccountInfo userInfo);

    UserSettleAccount getAccount(Long companyId);

    void addSettleAmount(UserSettleAccount settleAccount, BigDecimal amount);

    UserSettleAccount getPlatAccount();


}