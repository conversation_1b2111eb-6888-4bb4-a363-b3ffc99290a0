package com.std.core.service;

import com.std.core.pojo.domain.User;
import com.std.core.pojo.domain.UserAuthChannel;
import com.std.core.pojo.request.UserAuthChannelCreateReq;
import com.std.core.pojo.request.UserAuthChannelListFrontReq;
import com.std.core.pojo.request.UserAuthChannelListReq;
import com.std.core.pojo.request.UserAuthChannelModifyReq;
import com.std.core.pojo.request.UserAuthChannelPageFrontReq;
import com.std.core.pojo.request.UserAuthChannelPageReq;
import com.std.core.pojo.response.UserAuthChannelDetailRes;
import com.std.core.pojo.response.UserAuthChannelListRes;
import com.std.core.pojo.response.UserAuthChannelPageRes;
import java.util.List;

/**
 * 用户三方渠道关联表Service
 *
 * <AUTHOR> xieyj
 * @since : 2022-06-20 20:41
 */
public interface IUserAuthChannelService {

    /**
     * 新增用户三方渠道关联表
     *
     * @param req 新增用户三方渠道关联表入参
     * @param operator 操作人
     */
    void create(UserAuthChannelCreateReq req, User operator);

    void create(UserAuthChannel req);

    /**
     * 删除用户三方渠道关联表
     *
     * @param id 主键ID
     */
    void remove(Long id);

    /**
     * 修改用户三方渠道关联表
     *
     * @param req 修改用户三方渠道关联表入参
     * @param operator 操作人
     */
    void modify(UserAuthChannelModifyReq req, User operator);

    void modify(UserAuthChannel req);

    /**
     * 详情查询用户三方渠道关联表
     *
     * @param id 主键ID
     * @return 用户三方渠道关联表详情数据
     */
    UserAuthChannel detail(Long id);

    /**
     * 分页查询用户三方渠道关联表
     *
     * @param req 分页查询用户三方渠道关联表入参
     * @return 用户三方渠道关联表分页数据
     */
    List<UserAuthChannel> page(UserAuthChannelPageReq req);

    /**
     * 列表查询用户三方渠道关联表
     *
     * @param req 列表查询用户三方渠道关联表入参
     * @return 用户三方渠道关联表列表数据
     */
    List<UserAuthChannel> list(UserAuthChannelListReq req);

    List<UserAuthChannel> list(UserAuthChannel req);

    /**
     * 前端详情查询用户三方渠道关联表
     *
     * @param id 主键ID
     * @return 用户三方渠道关联表详情数据
     */
    UserAuthChannelDetailRes detailFront(Long id);

    /**
     * 前端分页查询用户三方渠道关联表
     *
     * @param req 分页查询用户三方渠道关联表入参
     * @return 用户三方渠道关联表分页数据
     */
    List<UserAuthChannelPageRes> pageFront(UserAuthChannelPageFrontReq req);

    /**
     * 前端列表查询用户三方渠道关联表
     *
     * @param req 列表查询用户三方渠道关联表入参
     * @return 用户三方渠道关联表列表数据
     */
    List<UserAuthChannelListRes> listFront(UserAuthChannelListFrontReq req);

    UserAuthChannel detailByChannel(Long userId, Long channelId);

    /**
     * 查询当前渠道下uid是否授权过
     */
    UserAuthChannel detailByChannelUid(Long channelId, String uid);
}