package com.std.core.service;

import com.alibaba.fastjson.JSONObject;
import com.std.common.base.BaseIdReq;
import com.std.common.base.BasePageReq;
import com.std.core.pojo.domain.Config;
import com.std.core.pojo.domain.Cuser;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.*;

import java.util.List;

/**
 * C端用户Service
 *
 * <AUTHOR> Leo
 * @since : 2020-05-18 20:09
 */
public interface ICuserService {

    /**
     * 新增C端用户
     *
     * @param req 新增C端用户入参
     * @param operator 操作人
     */
    void create(CuserCreateReq req, User operator);

    /**
     * 怀南会产品服务条款
     */
    Config detailRegisterServiceConfig();

    /**
     * 删除C端用户
     *
     * @param id 主键ID
     */
    void remove(Long id);

    /**
     * 修改C端用户
     *
     * @param req 修改C端用户入参
     * @param operator 操作人
     */
    void modify(CuserModifyReq req, User operator);

    /**
     * 修改C端用户
     */
    void modify(Cuser cuser);

    /**
     * 设置状态
     */
    void modifyStatus(UserStatusReq req, User operator, String ip);

    /**
     * 详情查询C端用户
     *
     * @param id 主键ID
     * @return C端用户详情数据
     */
    Cuser detail(Long id);

    /**
     * 分页查询C端用户
     *
     * @param req 分页查询C端用户入参
     * @return C端用户分页数据
     */
    List<Cuser> page(CuserPageReq req);

    /**
     * 模糊查询
     */
    List<Cuser> vagueDeatil(CuserPageReq req);

    /**
     * 列表查询C端用户
     *
     * @param req 列表查询C端用户入参
     * @return C端用户列表数据
     */
    List<Cuser> list(CuserListReq req);

    /**
     * C端用户注册
     */
    JSONObject register(UserRegisterReq request, String ip);

    /**
     * 用户名加入布隆过滤器
     * @param loginName
     */
    void addNewUserToRedissonBloomFilter(String loginName);

    /**
     * 发行方入住
     */
    JSONObject companyRegister(UserCompanyRegisterReq request,String userKind, String ip);

    /**
     * 验证uid是否绑定手机号
     */
    JSONObject doCheckBindMobile(XmlyCheckBindMobileReq request);

    /**
     * 绑定手机号
     */
    JSONObject doBindMobile(XmlyBindMobileReq request, String ip);

    /**
     * oss端代用户注册
     */
    User ossRegister(UserRegisterByOssReq request);


    /**
     * C端用户登录
     */
    UserLoginRes login(UserLoginReq request, Long channelId, String ip);

    JSONObject loginChannel(UserLoginChannelReq request, Long channelId, String ip);

    Cuser detailByUserId(Long userId);

    /**
     * 根据用户序号查询C端用户信息
     */
    Cuser detailByUser(Long userId);

    /**
     * 会员查询
     */
    List<UserDeatilRes> userDeatilRes();

    /**
     * 社区查询
     */
    List<TeamUserRes> teamPage(CuserPageReq request);

    /**
     * 重置推荐人
     */
    void changeReferUser(UserChangeRefereeReq request);

    /**
     * 用户等级说明
     */
    UserNodeLevelSummaryRes nodeLevelSummary(User operator);

    /**
     * 我的团队
     */
    UserMyTeamRes myTeam(User operator);

    /**
     * 设置分佣比例
     */
    void setRate(CuserSetRateReq request);

    void userGroupingSetting(UserGroupingSettingReq req, User operator);

    /**
     * 获取凯撒特殊用户的C端用户
     */
    Cuser getSpecificUser();

    /**
     * 设置虚拟收益
     */
    void setVirtualIncome(CuserSetVirtualIncomeReq request);


    void userChannelSetting(UserChannelSettingReq req, User operator);

    void cancelChannelSetting(UserChannelSettingCancelReq req, User operator);

    void modifyMobile(ModifyUserMobileReq req, User operator);


    void resetRefUser(ResetRefUserReq req, User operator);

    UserMyCommunityHeadRes myCommunityHead(User operator);

    /**
     * 我的社区分页查询
     */
    List<SubUserRes> myCommunity(User operator, BasePageReq request);

    /**
     * 社区成员查询
     */
    List<CommunityUserRes> communityPage(BaseIdReq request);

    /**
     * 社区用户信息
     */
    CommunityUserRes communityRef(BaseIdReq req);

    List<CommunityUserRes> communityRef2(BaseIdReq req);

    UserGetPhotoRes getUserPhotoByMobile(String mobile);

    UserGetPhotoRes checkUserByMobile(UserGetPhotoReq req);

    /**
     * 用户注销
     */
    void logoff(User operator, CuserLogoffReq request);

    /**
     * 系统注销用户
     */
    void doSysClose(Long userId, String remark, User operator, String ip);

    void batchReset(User operator, CuserBatchResetReq request);

    void batchModifyIdentifyStyle(User operator, CuserModifyIdentifyStyleReq request);

    /**
     * 批量清退用户
     */
    void batchRecovery(User operator, User user);

    UserHomeRes getUserUnRead(User operator);

    AgoraTokenRes getAgoraToken(AgoraTokenReq request,User operator);

    /**
     * 第三方用户注册
     *
     * @param request 第三方用户注册请求
     * @return 注册的用户
     */
    User registerThirdPartyUser(ThirdPartyUserRegisterReq request);

}