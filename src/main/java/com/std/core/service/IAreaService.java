package com.std.core.service;

import com.std.core.pojo.domain.Area;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.AreaCreateReq;
import com.std.core.pojo.request.AreaListReq;
import com.std.core.pojo.request.AreaModifyReq;
import com.std.core.pojo.request.AreaPageReq;

import java.util.List;

/**
 * 地区表Service
 *
 * <AUTHOR> xieyj
 * @since : 2020-08-10 17:06
 */
public interface IAreaService {

    /**
     * 新增地区表
     *
     * @param req      新增地区表入参
     * @param operator 操作人
     */
    void create(AreaCreateReq req, User operator);

    /**
     * 删除地区表
     *
     * @param id 主键ID
     */
    void remove(Long id);

    /**
     * 修改地区表
     *
     * @param req      修改地区表入参
     * @param operator 操作人
     */
    void modify(AreaModifyReq req, User operator);

    /**
     * 详情查询地区表
     *
     * @param id 主键ID
     * @return 地区表详情数据
     */
    Area detail(Long id);

    /**
     * 分页查询地区表
     *
     * @param req 分页查询地区表入参
     * @return 地区表分页数据
     */
    List<Area> page(AreaPageReq req);

    /**
     * 列表查询地区表
     *
     * @param req 列表查询地区表入参
     * @return 地区表列表数据
     */
    List<Area> list(AreaListReq req);

    //获取所有省份
    List<Area> getAllProvince();
    List<Area> getAllProvinceCompany();
    /**
     * 根据父id获取所有的市或者区
     *
     * @param id
     * @return
     */
    List<Area> allCityOrCounty(Long id);
    List<Area> allCityOrCountyCompany(Long id);

    List<Area> listCompany(AreaListReq request);


    String detailByCompanyCode(String province);
}