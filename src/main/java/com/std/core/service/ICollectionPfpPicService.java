package com.std.core.service;

import com.std.core.pojo.domain.*;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.CollectionPfpPicCreateReq;
import com.std.core.pojo.request.CollectionPfpPicListReq;
import com.std.core.pojo.request.CollectionPfpPicListFrontReq;
import com.std.core.pojo.request.CollectionPfpPicModifyReq;
import com.std.core.pojo.request.CollectionPfpPicPageReq;
import com.std.core.pojo.request.CollectionPfpPicPageFrontReq;
import com.std.core.pojo.request.PfpImageBatchUploadReq;
import com.std.core.pojo.request.PfpImageUrlBatchUploadReq;
import com.std.core.pojo.request.PfpOssZipUploadReq;
import com.std.core.pojo.request.PfpZipUploadReq;
import com.std.core.pojo.response.CollectionPfpPicDetailRes;
import com.std.core.pojo.response.CollectionPfpPicListRes;
import com.std.core.pojo.response.CollectionPfpPicPageRes;
import com.std.core.pojo.response.PfpImageUploadRes;
import java.util.List;

/**
 * PFP待开图Service
 *
 * <AUTHOR> wzh
 * @since : 2023-08-19 10:53
 */
public interface ICollectionPfpPicService {

    /**
     * 新增PFP待开图
     *
     * @param req 新增PFP待开图入参
     * @param operator 操作人
     */
    void create(CollectionPfpPicCreateReq req, User operator);
    void create(Long collectionId, Integer number,Integer orderNumber);
    void create(Long collectionId, Integer orderNumber, String pic, String picIpfs);
    /**
     * 删除PFP待开图
     *
     * @param id 主键ID
     */
     void remove(Long id);

    /**
     * 修改PFP待开图
     *
     * @param req 修改PFP待开图入参
     * @param operator 操作人
     */
    void modify(CollectionPfpPicModifyReq req, User operator);

    /**
     * 详情查询PFP待开图
     *
     * @param id 主键ID
     * @return PFP待开图详情数据
     */
     CollectionPfpPic detail(Long id);

    /**
     * 分页查询PFP待开图
     *
     * @param req 分页查询PFP待开图入参
     * @return PFP待开图分页数据
     */
     List<CollectionPfpPic> page(CollectionPfpPicPageReq req);

    /**
     * 列表查询PFP待开图
     *
     * @param req 列表查询PFP待开图入参
     * @return PFP待开图列表数据
     */
     List<CollectionPfpPic> list(CollectionPfpPicListReq req);

    /**
     * 前端详情查询PFP待开图
     *
     * @param id 主键ID
     * @return PFP待开图详情数据
     */
    CollectionPfpPicDetailRes detailFront(Long id);

    /**
     * 前端分页查询PFP待开图
     *
     * @param req 分页查询PFP待开图入参
     * @return PFP待开图分页数据
     */
     List<CollectionPfpPicPageRes> pageFront(CollectionPfpPicPageFrontReq req);

    /**
     * 前端列表查询PFP待开图
     *
     * @param req 列表查询PFP待开图入参
     * @return PFP待开图列表数据
     */
     List<CollectionPfpPicListRes> listFront(CollectionPfpPicListFrontReq req);

    List<CollectionPfpPic> listByCollectionIdList(List<Long> collectionIdList);

    Integer detailMaxOrderNumber();

    /**
     * 批量上传PFP图片文件
     *
     * @param req 批量上传请求
     * @param operator 操作人
     * @return 上传结果
     */
    PfpImageUploadRes batchUploadImages(PfpImageBatchUploadReq req, User operator);

    /**
     * 批量从URL上传PFP图片
     *
     * @param req 批量URL上传请求
     * @param operator 操作人
     * @return 上传结果
     */
    PfpImageUploadRes batchUploadFromUrls(PfpImageUrlBatchUploadReq req, User operator);

    /**
     * 从ZIP文件批量上传PFP图片
     *
     * @param req ZIP文件上传请求
     * @param operator 操作人
     * @return 上传结果
     */
    PfpImageUploadRes batchUploadFromZip(PfpZipUploadReq req, User operator);

    /**
     * 从OSS上的ZIP文件批量上传PFP图片
     *
     * @param req OSS ZIP文件处理请求
     * @param operator 操作人
     * @return 上传结果
     */
    PfpImageUploadRes batchUploadFromOssZip(PfpOssZipUploadReq req, User operator);
}