package com.std.core.service;

import com.std.core.enums.EUserRelationType;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.domain.UserRelation;
import com.std.core.pojo.request.UserRelationCreateReq;
import com.std.core.pojo.request.UserRelationListFrontReq;
import com.std.core.pojo.request.UserRelationListReq;
import com.std.core.pojo.request.UserRelationModifyReq;
import com.std.core.pojo.request.UserRelationPageFrontReq;
import com.std.core.pojo.request.UserRelationPageReq;
import com.std.core.pojo.response.UserRelationDetailRes;
import com.std.core.pojo.response.UserRelationListRes;
import com.std.core.pojo.response.UserRelationPageRes;
import okhttp3.internal.Internal;

import java.util.List;

/**
 * 用户关注信息Service
 *
 * <AUTHOR> xieyj
 * @since : 2021-07-07 09:55
 */
public interface IUserRelationService {

    /**
     * 新增用户关注信息
     *
     * @param req 新增用户关注信息入参
     * @param operator 操作人
     */
    void create(UserRelationCreateReq req, User operator);

    /**
     * 删除用户关注信息
     *
     * @param id 主键ID
     */
    void remove(Long id);

    /**
     * 修改用户关注信息
     *
     * @param req 修改用户关注信息入参
     * @param operator 操作人
     */
    void modify(UserRelationModifyReq req, User operator);

    /**
     * 详情查询用户关注信息
     *
     * @param id 主键ID
     * @return 用户关注信息详情数据
     */
    UserRelation detail(Long id);

    /**
     * 分页查询用户关注信息
     *
     * @param req 分页查询用户关注信息入参
     * @return 用户关注信息分页数据
     */
    List<UserRelation> page(UserRelationPageReq req);

    /**
     * 列表查询用户关注信息
     *
     * @param req 列表查询用户关注信息入参
     * @return 用户关注信息列表数据
     */
    List<UserRelation> list(UserRelationListReq req);

    List<Long> attentionUserList(Long userId);

    /**
     * 前端详情查询用户关注信息
     *
     * @param id 主键ID
     * @return 用户关注信息详情数据
     */
    UserRelationDetailRes detailFront(Long id);

    /**
     * 前端分页查询用户关注信息
     *
     * @param req 分页查询用户关注信息入参
     * @return 用户关注信息分页数据
     */
    List<UserRelationPageRes> pageFront(UserRelationPageFrontReq req, User operator);

    /**
     * 前端列表查询用户关注信息
     *
     * @param req 列表查询用户关注信息入参
     * @return 用户关注信息列表数据
     */
    List<UserRelationListRes> listFront(UserRelationListFrontReq req);

    long countByCondition(String type, Long userId, Long toUser);

    long countByCondition(UserRelation condition);

    long userFansCount(Long userId, String type);

    long userAttentionCount(Long userId, String type);

    long deleteByCondition(UserRelation condition);

    void followForumUser(User operator, Long toUserId);

    void attentionUser(User operator, Long toUserId, EUserRelationType eUserRelationType);

    boolean isExistUserRelation(long userId, long toUserId, String type);

    Long detailCompanyAttentionCount(Long companyId);

    List<Long> detailListUser(Long toUserId,String type);
}