package com.std.core.service;

import com.std.common.base.BaseIdReq;
import com.std.core.pojo.domain.PayRecord;
import com.std.core.pojo.domain.TransferOrder;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.*;

import java.util.List;

/**
 * 渠道系统藏品流转订单Service
 *
 * <AUTHOR> ycj
 * @since : 2022-03-21 10:50
 */
public interface ITransferOrderService {

    /**
     * 新增渠道系统藏品流转订单
     *
     * @param req 新增渠道系统藏品流转订单入参
     * @param operator 操作人
     */
    OrderPayRes create(TransferOrderCreateReq req, User operator);

    /**
     * 删除渠道系统藏品流转订单
     *
     * @param id 主键ID
     */
     void remove(Long id);

    /**
     * 修改渠道系统藏品流转订单
     *
     * @param req 修改渠道系统藏品流转订单入参
     * @param operator 操作人
     */
    void modify(TransferOrderModifyReq req, User operator);

    void modify(TransferOrder req);
    /**
     * 详情查询渠道系统藏品流转订单
     *
     * @param id 主键ID
     * @return 渠道系统藏品流转订单详情数据
     */
     TransferOrder detail(Long id);
    TransferOrder detailForUpdate(Long id);

    /**
     * 分页查询渠道系统藏品流转订单
     *
     * @param req 分页查询渠道系统藏品流转订单入参
     * @return 渠道系统藏品流转订单分页数据
     */
     List<TransferOrder> page(TransferOrderPageReq req);
    List<TransferOrderRes> pageTransferDetail(TransferOrderPageReq request);

    /**
     * 列表查询渠道系统藏品流转订单
     *
     * @param req 列表查询渠道系统藏品流转订单入参
     * @return 渠道系统藏品流转订单列表数据
     */
     List<TransferOrder> list(TransferOrderListReq req);
    List<TransferOrder> list(TransferOrder req);

    /**
     * 前端详情查询渠道系统藏品流转订单
     *
     * @param id 主键ID
     * @return 渠道系统藏品流转订单详情数据
     */
    TransferOrderDetailRes detailFront(Long id);

    /**
     * 前端分页查询渠道系统藏品流转订单
     *
     * @param req 分页查询渠道系统藏品流转订单入参
     * @return 渠道系统藏品流转订单分页数据
     */
     List<TransferOrderPageRes> pageFront(TransferOrderPageFrontReq req);

    /**
     * 前端列表查询渠道系统藏品流转订单
     *
     * @param req 列表查询渠道系统藏品流转订单入参
     * @return 渠道系统藏品流转订单列表数据
     */
     List<TransferOrderListRes> listFront(TransferOrderListFrontReq req);

    void create(TransferOrder transferOrder);

    void passOn(TransferOrder transferOrder);

    /**
     * 转出回调
     * @param transferOrder
     */
    void passOnCallBack(TransferOrder transferOrder);

    Integer detailTransferNotFinish(Long collectionId, Long userId);

}