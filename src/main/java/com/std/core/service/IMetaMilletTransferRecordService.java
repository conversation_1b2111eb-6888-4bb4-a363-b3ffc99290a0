package com.std.core.service;

import com.std.core.pojo.domain.MetaMilletTransferRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.MetaMilletTransferRecordCreateReq;
import com.std.core.pojo.request.MetaMilletTransferRecordListReq;
import com.std.core.pojo.request.MetaMilletTransferRecordListFrontReq;
import com.std.core.pojo.request.MetaMilletTransferRecordModifyReq;
import com.std.core.pojo.request.MetaMilletTransferRecordPageReq;
import com.std.core.pojo.request.MetaMilletTransferRecordPageFrontReq;
import com.std.core.pojo.response.MetaMilletTransferRecordDetailRes;
import com.std.core.pojo.response.MetaMilletTransferRecordListRes;
import com.std.core.pojo.response.MetaMilletTransferRecordPageRes;
import java.util.List;

/**
 * 元粟转赠记录Service
 *
 * <AUTHOR> ycj
 * @since : 2022-11-11 14:42
 */
public interface IMetaMilletTransferRecordService {

    /**
     * 新增元粟转赠记录
     *
     * @param req 新增元粟转赠记录入参
     * @param operator 操作人
     */
    void create(MetaMilletTransferRecordCreateReq req, User operator);

    /**
     * 删除元粟转赠记录
     *
     * @param id 主键ID
     */
     void remove(Long id);

    /**
     * 修改元粟转赠记录
     *
     * @param req 修改元粟转赠记录入参
     * @param operator 操作人
     */
    void modify(MetaMilletTransferRecordModifyReq req, User operator);

    /**
     * 详情查询元粟转赠记录
     *
     * @param id 主键ID
     * @return 元粟转赠记录详情数据
     */
     MetaMilletTransferRecord detail(Long id);

    /**
     * 分页查询元粟转赠记录
     *
     * @param req 分页查询元粟转赠记录入参
     * @return 元粟转赠记录分页数据
     */
     List<MetaMilletTransferRecord> page(MetaMilletTransferRecordPageReq req);

    /**
     * 列表查询元粟转赠记录
     *
     * @param req 列表查询元粟转赠记录入参
     * @return 元粟转赠记录列表数据
     */
     List<MetaMilletTransferRecord> list(MetaMilletTransferRecordListReq req);

    /**
     * 前端详情查询元粟转赠记录
     *
     * @param id 主键ID
     * @return 元粟转赠记录详情数据
     */
    MetaMilletTransferRecordDetailRes detailFront(Long id);

    /**
     * 前端分页查询元粟转赠记录
     *
     * @param req 分页查询元粟转赠记录入参
     * @return 元粟转赠记录分页数据
     */
     List<MetaMilletTransferRecordPageRes> pageFront(MetaMilletTransferRecordPageFrontReq req);

    /**
     * 前端列表查询元粟转赠记录
     *
     * @param req 列表查询元粟转赠记录入参
     * @return 元粟转赠记录列表数据
     */
     List<MetaMilletTransferRecordListRes> listFront(MetaMilletTransferRecordListFrontReq req);

}