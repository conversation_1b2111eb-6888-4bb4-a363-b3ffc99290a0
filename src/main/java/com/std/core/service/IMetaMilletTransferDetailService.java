package com.std.core.service;

import com.std.core.pojo.domain.MetaMilletTransferDetail;
import com.std.core.pojo.domain.MetaMilletTransferRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.MetaMilletTransferDetailCreateReq;
import com.std.core.pojo.request.MetaMilletTransferDetailListReq;
import com.std.core.pojo.request.MetaMilletTransferDetailListFrontReq;
import com.std.core.pojo.request.MetaMilletTransferDetailModifyReq;
import com.std.core.pojo.request.MetaMilletTransferDetailPageReq;
import com.std.core.pojo.request.MetaMilletTransferDetailPageFrontReq;
import com.std.core.pojo.response.MetaMilletTransferDetailDetailRes;
import com.std.core.pojo.response.MetaMilletTransferDetailListRes;
import com.std.core.pojo.response.MetaMilletTransferDetailPageRes;
import java.util.List;

/**
 * 元粟转赠明细Service
 *
 * <AUTHOR> ycj
 * @since : 2022-11-11 14:41
 */
public interface IMetaMilletTransferDetailService {

    /**
     * 新增元粟转赠明细
     *
     * @param req 新增元粟转赠明细入参
     * @param operator 操作人
     */
    void create(MetaMilletTransferDetailCreateReq req, User operator);

    /**
     * 删除元粟转赠明细
     *
     * @param id 主键ID
     */
     void remove(Long id);

    /**
     * 修改元粟转赠明细
     *
     * @param req 修改元粟转赠明细入参
     * @param operator 操作人
     */
    void modify(MetaMilletTransferDetailModifyReq req, User operator);

    /**
     * 详情查询元粟转赠明细
     *
     * @param id 主键ID
     * @return 元粟转赠明细详情数据
     */
     MetaMilletTransferDetail detail(Long id);

    /**
     * 分页查询元粟转赠明细
     *
     * @param req 分页查询元粟转赠明细入参
     * @return 元粟转赠明细分页数据
     */
     List<MetaMilletTransferDetail> page(MetaMilletTransferDetailPageReq req);

    /**
     * 列表查询元粟转赠明细
     *
     * @param req 列表查询元粟转赠明细入参
     * @return 元粟转赠明细列表数据
     */
     List<MetaMilletTransferDetail> list(MetaMilletTransferDetailListReq req);

    /**
     * 前端详情查询元粟转赠明细
     *
     * @param id 主键ID
     * @return 元粟转赠明细详情数据
     */
    MetaMilletTransferDetailDetailRes detailFront(Long id);

    /**
     * 前端分页查询元粟转赠明细
     *
     * @param req 分页查询元粟转赠明细入参
     * @return 元粟转赠明细分页数据
     */
     List<MetaMilletTransferDetailPageRes> pageFront(MetaMilletTransferDetailPageFrontReq req);

    /**
     * 前端列表查询元粟转赠明细
     *
     * @param req 列表查询元粟转赠明细入参
     * @return 元粟转赠明细列表数据
     */
     List<MetaMilletTransferDetailListRes> listFront(MetaMilletTransferDetailListFrontReq req);

    void batchCreate(List<MetaMilletTransferDetail> detailList);

    List<MetaMilletTransferDetail> detailByRecord(Long recordId);
}