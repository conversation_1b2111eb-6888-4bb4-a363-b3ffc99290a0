package com.std.core.service;

import com.std.core.pojo.domain.AwardEntity;
import com.std.core.pojo.domain.CollectionPeriodRelation;
import com.std.core.pojo.domain.PeriodCollectionStatistics;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.CollectionPeriodCollectionPageRes;
import com.std.core.pojo.response.CollectionPeriodRelationDetailRes;
import com.std.core.pojo.response.CollectionPeriodRelationListRes;
import com.std.core.pojo.response.CollectionPeriodRelationPageRes;
import java.util.List;

/**
 * 藏品作品期数Service
 *
 * <AUTHOR> ycj
 * @since : 2021-12-14 15:45
 */
public interface ICollectionPeriodRelationService {

    /**
     * 新增藏品作品期数
     *
     * @param req 新增藏品作品期数入参
     * @param operator 操作人
     */
    void create(CollectionPeriodRelationCreateReq req, User operator);

    /**
     * 删除藏品作品期数
     *
     * @param id 主键ID
     */
    void remove(Long id);

    /**
     * 修改藏品作品期数
     *
     * @param req 修改藏品作品期数入参
     * @param operator 操作人
     */
    void modify(CollectionPeriodRelationModifyReq req, User operator);
    void modify(CollectionPeriodRelation req);

    /**
     * 详情查询藏品作品期数
     *
     * @param id 主键ID
     * @return 藏品作品期数详情数据
     */
    CollectionPeriodRelation detail(Long id);

    CollectionPeriodRelation detailRelation(Long periodId, Long collectionId);

    /**
     * 分页查询藏品作品期数
     *
     * @param req 分页查询藏品作品期数入参
     * @return 藏品作品期数分页数据
     */
    List<CollectionPeriodRelation> page(CollectionPeriodRelationPageReq req);

    /**
     * 列表查询藏品作品期数
     *
     * @param req 列表查询藏品作品期数入参
     * @return 藏品作品期数列表数据
     */
    List<CollectionPeriodRelation> list(CollectionPeriodRelationListReq req);

    List<CollectionPeriodRelation> list(CollectionPeriodRelation relation);

    List<CollectionPeriodRelation> list(Long periodId);

    /**
     * 前端详情查询藏品作品期数
     *
     * @param id 主键ID
     * @return 藏品作品期数详情数据
     */
    CollectionPeriodRelationDetailRes detailFront(Long id);
    CollectionPeriodRelation detailForUpdate(Long id);

    /**
     * 前端分页查询藏品作品期数
     *
     * @param req 分页查询藏品作品期数入参
     * @return 藏品作品期数分页数据
     */
    List<CollectionPeriodRelationPageRes> pageFront(CollectionPeriodRelationPageFrontReq req);

    /**
     * 前端列表查询藏品作品期数
     *
     * @param req 列表查询藏品作品期数入参
     * @return 藏品作品期数列表数据
     */
    List<CollectionPeriodRelationListRes> listFront(CollectionPeriodRelationListFrontReq req);


    Integer getPeriodLockTime(Long id);

    Integer getPeriodTransformLimitTime(Long id);

    /**
     * 根据期数序号查询权重
     */
    List<AwardEntity> ListByPeriodId(Long periodId, String config, List<Long> idList);

    Integer selectSellCollectionCount(Long companyId);

    List<PeriodCollectionStatistics> periodCollectionStatistics(CollectionPagePeriodCollectionStatisticsReq request);

    List<CollectionPeriodCollectionPageRes> detailListByPeriodId(Long periodId, Integer collectionNumber);

    List<Long> listRank(Long periodId, int limit);

    CollectionPeriodRelation listRankAndHaveRemain(Long periodId, int limit);

    CollectionPeriodRelation listRankAndHaveRemainByToken(Long periodId, int limit);

    List<CollectionPeriodRelation> listPeriodId(Long periodId, String periodStatus);

    List<CollectionPeriodRelation> listByCollectionId(Long collectionId);
}