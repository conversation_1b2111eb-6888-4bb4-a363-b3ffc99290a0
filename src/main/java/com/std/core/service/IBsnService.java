package com.std.core.service;

import com.alibaba.fastjson.JSONObject;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;

/**
 * bsn交易任务Service
 *
 * <AUTHOR> xieyj
 * @since : 2022-10-12 14:49
 */
public interface IBsnService {

    /**
     * 创建账户地址
     */
    List<String> createAccount(Integer count);

    /**
     * 新建分类
     */
    String addClasses(String className, String owner);

    String issueNFT(String classId, String name, MultipartFile multipartFile, String coverUrl, String fileUrl, String recipient);

    /**
     * 批量发行nft
     *
     * <AUTHOR>
     * @date: 2022/10/12 15:08
     */
    String issueNFTBatch(String classId, String name, MultipartFile multipartFile, String uri, String data, String recipient,
            Integer count);

    /**
     * 转赠
     */
    String transNFT(String owerAddress, String recipient, String classId, String nftId);

    JSONObject queryTaskInfo(String taskId);

    JSONObject queryNftInfo(String nftId, String classId);


}