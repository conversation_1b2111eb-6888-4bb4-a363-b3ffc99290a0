package com.std.core.service;

import com.std.common.base.BaseIdReq;
import com.std.core.pojo.domain.UserBindCard;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.*;

import java.util.List;

/**
 * 用户绑定快捷支付Service
 *
 * <AUTHOR> ycj
 * @since : 2022-03-09 17:36
 */
public interface IUserBindCardService {

    /**
     * 用户预约绑卡
     *
     * @param req      新增用户绑定快捷支付入参
     * @param operator 操作人
     */
    UserBindCardCeateRes create(UserBindCardCreateReq req, User operator);

    /**
     * 易宝绑卡
     * @param request
     * @param operator
     */
    UserBindCardCeateRes yeepayCreate(UserBindCardCreateReq request, User operator);
    /**
     * 用户确认绑卡
     *
     * @param request
     * @param operator
     */
    void confirmSign(UserBindCardConfirmSignReq request, User operator);

    /**
     * 易宝确认绑卡
     *
     * @param request
     * @param operator
     */
    void yeePayconfirmSign(UserBindCardConfirmSignReq request, User operator);


    void modify(UserBindCard req);

    /**
     * 删除用户绑定快捷支付
     *
     * @param id 主键ID
     */
    void remove(Long id);

    /**
     * 修改用户绑定快捷支付
     *
     * @param req      修改用户绑定快捷支付入参
     * @param operator 操作人
     */
    void modify(UserBindCardModifyReq req, User operator);

    /**
     * 详情查询用户绑定快捷支付
     *
     * @param id 主键ID
     * @return 用户绑定快捷支付详情数据
     */
    UserBindCard detail(Long id);

    /**
     * 分页查询用户绑定快捷支付
     *
     * @param req 分页查询用户绑定快捷支付入参
     * @return 用户绑定快捷支付分页数据
     */
    List<UserBindCard> page(UserBindCardPageReq req);

    /**
     * 列表查询用户绑定快捷支付
     *
     * @param req 列表查询用户绑定快捷支付入参
     * @return 用户绑定快捷支付列表数据
     */
    List<UserBindCard> list(UserBindCardListReq req);

    /**
     * 前端详情查询用户绑定快捷支付
     *
     * @param id 主键ID
     * @return 用户绑定快捷支付详情数据
     */
    UserBindCardDetailRes detailFront(Long id);

    /**
     * 前端分页查询用户绑定快捷支付
     *
     * @param req 分页查询用户绑定快捷支付入参
     * @return 用户绑定快捷支付分页数据
     */
    List<UserBindCardPageRes> pageFront(UserBindCardPageFrontReq req);

    /**
     * 前端列表查询用户绑定快捷支付
     *
     * @param req 列表查询用户绑定快捷支付入参
     * @return 用户绑定快捷支付列表数据
     */
    List<UserBindCardListRes> listFront(UserBindCardListFrontReq req, User operator);

    /**
     * @param cardNo
     * @return
     */
    UserBindCardDetailBinInfoRes detailBinInfo(String cardNo, String channelType);

    UserBindCardDetailBinInfoRes getUserBindCardDetailBinInfoRes(String cardNo, String channelType);

    List<Long> smsCodeFailure();

    /**
     * 获取用户卡宾信息
     * @param id
     * @param operator
     * @return
     */
    UserBindCard detail(Long id, User operator);

    /**
     * 用户解除绑定
     * @param request
     * @param operator
     */
    void abolishBind(BaseIdReq request, User operator);


}