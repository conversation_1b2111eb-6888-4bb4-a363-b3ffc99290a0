package com.std.core.service;

import com.std.core.pojo.domain.ChannelMetaSystem;
import com.std.core.pojo.domain.U3dProps;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ChannelMetaSystemCreateReq;
import com.std.core.pojo.request.ChannelMetaSystemListReq;
import com.std.core.pojo.request.ChannelMetaSystemListFrontReq;
import com.std.core.pojo.request.ChannelMetaSystemModifyReq;
import com.std.core.pojo.request.ChannelMetaSystemPageReq;
import com.std.core.pojo.request.ChannelMetaSystemPageFrontReq;
import com.std.core.pojo.response.*;

import java.util.List;

/**
 * 元宇宙系统Service
 *
 * <AUTHOR> ycj
 * @since : 2023-04-27 16:27
 */
public interface IChannelMetaSystemService {

    /**
     * 新增元宇宙系统
     *
     * @param req      新增元宇宙系统入参
     * @param operator 操作人
     */
    void create(ChannelMetaSystemCreateReq req, User operator);

    ChannelMetaSystemCreateRes enterTheMeta(ChannelMetaSystemCreateReq request, User operator);

    /**
     * 删除元宇宙系统
     *
     * @param id 主键ID
     */
    void remove(Long id);

    /**
     * 修改元宇宙系统
     *
     * @param req      修改元宇宙系统入参
     * @param operator 操作人
     */
    void modify(ChannelMetaSystemModifyReq req, User operator);

    /**
     * 详情查询元宇宙系统
     *
     * @param id 主键ID
     * @return 元宇宙系统详情数据
     */
    ChannelMetaSystem detail(Long id);

    U3dProps detailRole(Long id);

    /**
     * 分页查询元宇宙系统
     *
     * @param req 分页查询元宇宙系统入参
     * @return 元宇宙系统分页数据
     */
    List<ChannelMetaSystem> page(ChannelMetaSystemPageReq req);

    /**
     * 列表查询元宇宙系统
     *
     * @param req 列表查询元宇宙系统入参
     * @return 元宇宙系统列表数据
     */
    List<ChannelMetaSystem> list(ChannelMetaSystemListReq req);

    /**
     * 前端详情查询元宇宙系统
     *
     * @param id 主键ID
     * @return 元宇宙系统详情数据
     */
    ChannelMetaSystemDetailRes detailFront(Long id);
    /**
     * 前端分页查询元宇宙系统
     *
     * @param req 分页查询元宇宙系统入参
     * @return 元宇宙系统分页数据
     */
    List<ChannelMetaSystemPageRes> pageFront(ChannelMetaSystemPageFrontReq req);

    /**
     * 前端列表查询元宇宙系统
     *
     * @param req      列表查询元宇宙系统入参
     * @param operator
     * @return 元宇宙系统列表数据
     */
    List<ChannelMetaSystemListRes> listFront(ChannelMetaSystemListFrontReq req, User operator);

    U3dMyRoleDetailRes metaRoleRand();


}