<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.MeetingTicketMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.MeetingTicket">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="meeting_id" jdbcType="BIGINT" property="meetingId"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="collection_name" jdbcType="VARCHAR" property="collectionName"/>
        <result column="collection_pic" jdbcType="VARCHAR" property="collectionPic"/>
        <result column="pwd" jdbcType="VARCHAR" property="pwd"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.type
        , t.meeting_id
        , t.collection_id
        , t.collection_name
        , t.collection_pic
        , t.pwd
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="meetingId != null">
                AND t.meeting_id = #{meetingId, jdbcType=BIGINT}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="collectionName != null and collectionName != '' ">
                AND t.collection_name = #{collectionName, jdbcType=VARCHAR}
            </if>
            <if test="collectionPic != null and collectionPic != '' ">
                AND t.collection_pic = #{collectionPic, jdbcType=VARCHAR}
            </if>
            <if test="pwd != null and pwd != '' ">
                AND t.pwd = #{pwd, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.MeetingTicket">
        insert into live_meeting_ticket
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="meetingId != null ">
                meeting_id,
            </if>
            <if test="collectionId != null ">
                collection_id,
            </if>
            <if test="collectionName != null and collectionName != '' ">
                collection_name ,
            </if>
            <if test="collectionPic != null and collectionPic != '' ">
                collection_pic ,
            </if>
            <if test="pwd != null and pwd != '' ">
                pwd,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="meetingId != null">
                #{meetingId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="collectionName != null and collectionName != '' ">
                #{collectionName, jdbcType=VARCHAR},
            </if>
            <if test="collectionPic != null and collectionPic != '' ">
                #{collectionPic, jdbcType=VARCHAR},
            </if>
            <if test="pwd != null and pwd != '' ">
                #{pwd,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <insert id="insertBatch">
        <foreach collection="list" item="item" index="index" separator=";">
            insert into live_meeting_ticket
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.type != null and item.type != '' ">
                    type,
                </if>
                <if test="item.meetingId != null ">
                    meeting_id,
                </if>
                <if test="item.collectionId != null ">
                    collection_id,
                </if>
                <if test="item.collectionName != null and item.collectionName != '' ">
                    collection_name,
                </if>
                <if test="item.collectionPic != null and item.collectionPic != '' ">
                    collection_pic,
                </if>
                <if test="item.pwd != null and item.pwd != '' ">
                    pwd,
                </if>
                <if test="item.createDatetime != null ">
                    create_datetime,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.type != null and item.type != '' ">
                    #{item.type,jdbcType=VARCHAR},
                </if>
                <if test="item.meetingId != null">
                    #{item.meetingId,jdbcType=BIGINT},
                </if>
                <if test="item.collectionId != null">
                    #{item.collectionId,jdbcType=BIGINT},
                </if>
                <if test="item.collectionName != null and item.collectionName != '' ">
                    #{item.collectionName, jdbcType=VARCHAR},
                </if>
                <if test="item.collectionPic != null and item.collectionPic != '' ">
                    #{item.collectionPic, jdbcType=VARCHAR},
                </if>
                <if test="item.pwd != null and item.pwd != '' ">
                    #{item.pwd,jdbcType=VARCHAR},
                </if>
                <if test="item.createDatetime != null">
                    #{item.createDatetime,jdbcType=TIMESTAMP},
                </if>
            </trim>
        </foreach>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from live_meeting_ticket
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByMeeting">
        delete
        from live_meeting_ticket
        where meeting_id = #{meetingId,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.MeetingTicket">
        update live_meeting_ticket
        <set>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="meetingId != null">
                meeting_id = #{meetingId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="collectionName != null and collectionName != '' ">
                collection_name = #{collectionName, jdbcType=VARCHAR},
            </if>
            <if test="collectionPic != null and collectionPic != '' ">
                collection_pic = #{collectionPic, jdbcType=VARCHAR},
            </if>
            <if test="pwd != null and pwd != '' ">
                pwd = #{pwd,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from live_meeting_ticket t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.MeetingTicket"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from live_meeting_ticket t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectMeetingTick" parameterType="com.std.core.pojo.domain.MeetingTicket"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        live_meeting_ticket t
        INNER JOIN live_meeting_application ta ON t.meeting_id = ta.id
        where (
        ta.`status` = '1'
        OR ( ta.`status` = '0' AND DATE_ADD( #{date}, INTERVAL #{meetingStartEarlyMinutes} MINUTE ) <![CDATA[ >=]]>
        ta.estimate_start_datetime ))
    </select>
</mapper>