<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ActivityMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Activity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="pic" jdbcType="VARCHAR" property="pic"/>
        <result column="rule_note" jdbcType="VARCHAR" property="ruleNote"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="invite_send_time" jdbcType="INTEGER" property="inviteSendTime"/>
        <result column="register_send_time" jdbcType="INTEGER" property="registerSendTime"/>
        <result column="is_real_name" jdbcType="VARCHAR" property="isRealName"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="start_status" jdbcType="VARCHAR" property="startStatus"/>
        <result column="weight_type" jdbcType="VARCHAR" property="weightType"/>
        <result column="start_datetime" jdbcType="TIMESTAMP" property="startDatetime"/>
        <result column="end_datetime" jdbcType="TIMESTAMP" property="endDatetime"/>
        <result column="creater" jdbcType="BIGINT" property="creater"/>
        <result column="creater_name" jdbcType="VARCHAR" property="createrName"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.name
        , t.pic
        , t.rule_note
        , t.collection_id
        , t.invite_send_time
        , t.register_send_time
        , t.is_real_name
        , t.status
        , t.start_status
        , t.weight_type
        , t.start_datetime
        , t.end_datetime
        , t.creater
        , t.creater_name
        , t.create_datetime
        , t.updater
        , t.updater_name
        , t.update_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="pic != null and pic != '' ">
                AND t.pic = #{pic, jdbcType=VARCHAR}
            </if>
            <if test="ruleNote != null and ruleNote != '' ">
                AND t.rule_note = #{ruleNote, jdbcType=VARCHAR}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=INTEGER}
            </if>
            <if test="inviteSendTime != null">
                AND t.invite_send_time = #{inviteSendTime, jdbcType=BIGINT}
            </if>
            <if test="registerSendTime != null">
                AND t.register_send_time = #{registerSendTime, jdbcType=BIGINT}
            </if>
            <if test="isRealName != null and isRealName != '' ">
                AND t.is_real_name = #{isRealName, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="startStatus != null and startStatus != '' ">
                AND t.start_status = #{startStatus, jdbcType=VARCHAR}
            </if>
            <if test="weightType != null and weightType != '' ">
                AND t.weight_type = #{weightType, jdbcType=VARCHAR}
            </if>
            <if test="startDatetime != null">
                AND t.start_datetime = #{startDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="endDatetime != null">
                AND t.end_datetime = #{endDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="creater != null">
                AND t.creater = #{creater, jdbcType=BIGINT}
            </if>
            <if test="createrName != null and createrName != '' ">
                AND t.creater_name = #{createrName, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Activity">
        insert into nya_activity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="name != null and name != '' ">
                name,
            </if>
            <if test="pic != null and pic != '' ">
                pic,
            </if>
            <if test="ruleNote != null and ruleNote != '' ">
                rule_note,
            </if>
            <if test="collectionId != null ">
                collection_id,
            </if>
            <if test="inviteSendTime != null">
                invite_send_time,
            </if>
            <if test="registerSendTime != null">
                register_send_time,
            </if>
            <if test="isRealName != null and isRealName != '' ">
                is_real_name,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="startStatus != null and startStatus != '' ">
                start_status,
            </if>
            <if test="weightType != null and weightType != '' ">
                weight_type,
            </if>
            <if test="startDatetime != null ">
                start_datetime,
            </if>
            <if test="endDatetime != null ">
                end_datetime,
            </if>
            <if test="creater != null ">
                creater,
            </if>
            <if test="createrName != null and createrName != '' ">
                creater_name,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="updater != null ">
                updater,
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="pic != null and pic != '' ">
                #{pic,jdbcType=VARCHAR},
            </if>
            <if test="ruleNote != null and ruleNote != '' ">
                #{ruleNote, jdbcType=VARCHAR},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="inviteSendTime != null">
                #{inviteSendTime, jdbcType=BIGINT},
            </if>
            <if test="registerSendTime != null">
                #{registerSendTime, jdbcType=BIGINT},
            </if>
            <if test="isRealName != null and isRealName != '' ">
                #{isRealName, jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="startStatus != null and startStatus != '' ">
                #{startStatus,jdbcType=VARCHAR},
            </if>
            <if test="weightType != null and weightType != '' ">
                #{weightType, jdbcType=VARCHAR},
            </if>
            <if test="startDatetime != null">
                #{startDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="endDatetime != null">
                #{endDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="creater != null">
                #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nya_activity
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Activity">
        update nya_activity
        <set>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="pic != null and pic != '' ">
                pic = #{pic,jdbcType=VARCHAR},
            </if>
            <if test="ruleNote != null and ruleNote != '' ">
                rule_note = #{ruleNote, jdbcType=VARCHAR},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="inviteSendTime != null">
                invite_send_time = #{inviteSendTime, jdbcType=BIGINT},
            </if>
            <if test="registerSendTime != null">
                register_send_time = #{registerSendTime, jdbcType=BIGINT},
            </if>
            <if test="isRealName != null and isRealName != '' ">
                is_real_name = #{isRealName, jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="startStatus != null and startStatus != '' ">
                start_status = #{startStatus,jdbcType=VARCHAR},
            </if>
            <if test="weightType != null and weightType != '' ">
                weight_type = #{weightType, jdbcType=VARCHAR},
            </if>
            <if test="startDatetime != null">
                start_datetime = #{startDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="endDatetime != null">
                end_datetime = #{endDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="creater != null">
                creater = #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                creater_name = #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="startActivity">
        update nya_activity
        set start_status='1'
        where id in
              (SELECT id
               FROM (select id
                     from nya_activity
                     where start_status = '0'
                       and DATE_FORMAT(start_datetime, '%Y-%m-%d %H:%i') <![CDATA[ <= ]]> DATE_FORMAT(#{date}, '%Y-%m-%d %H:%i')) a);
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nya_activity t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Activity"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nya_activity t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="getEndActivity" parameterType="com.std.core.pojo.domain.Activity"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nya_activity t
        where t.start_status='1' and DATE_FORMAT(t.end_datetime,'%Y-%m-%d %H:%i') <![CDATA[ <=]]>
        DATE_FORMAT(#{date},'%Y-%m-%d %H:%i')

    </select>
</mapper>