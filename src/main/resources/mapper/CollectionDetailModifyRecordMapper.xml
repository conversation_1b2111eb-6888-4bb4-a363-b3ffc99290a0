<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.CollectionDetailModifyRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.CollectionDetailModifyRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="before_value" jdbcType="VARCHAR" property="beforeValue"/>
        <result column="modify_value" jdbcType="VARCHAR" property="modifyValue"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="apply_user" jdbcType="BIGINT" property="applyUser"/>
        <result column="apply_datetime" jdbcType="TIMESTAMP" property="applyDatetime"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.type
        , t.collection_id
        , t.before_value
        , t.modify_value
        , t.status
        , t.apply_user
        , t.apply_datetime
        , t.updater
        , t.update_datetime
        , t.remark
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="beforeValue != null and beforeValue != '' ">
                AND t.before_value = #{beforeValue, jdbcType=VARCHAR}
            </if>
            <if test="modifyValue != null and modifyValue != '' ">
                AND t.modify_value = #{modifyValue, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="applyUser != null">
                AND t.apply_user = #{applyUser, jdbcType=BIGINT}
            </if>
            <if test="applyDatetime != null">
                AND t.apply_datetime = #{applyDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.CollectionDetailModifyRecord">
        insert into nft_collection_detail_modify_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="collectionId != null ">
                collection_id,
            </if>
            <if test="beforeValue != null and beforeValue != '' ">
                before_value,
            </if>
            <if test="modifyValue != null and modifyValue != '' ">
                modify_value,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="applyUser != null ">
                apply_user,
            </if>
            <if test="applyDatetime != null ">
                apply_datetime,
            </if>
            <if test="updater != null ">
                updater,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
            <if test="remark != null and remark != '' ">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="beforeValue != null and beforeValue != '' ">
                #{beforeValue,jdbcType=VARCHAR},
            </if>
            <if test="modifyValue != null and modifyValue != '' ">
                #{modifyValue,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="applyUser != null ">
                #{applyUser,jdbcType=BIGINT},
            </if>
            <if test="applyDatetime != null ">
                #{applyDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_collection_detail_modify_record
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.CollectionDetailModifyRecord">
        update nft_collection_detail_modify_record
        <set>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="beforeValue != null and beforeValue != '' ">
                before_value = #{beforeValue,jdbcType=VARCHAR},
            </if>
            <if test="modifyValue != null and modifyValue != '' ">
                modify_value = #{modifyValue,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="applyUser != null">
                apply_user = #{applyUser,jdbcType=BIGINT},
            </if>
            <if test="applyDatetime != null">
                apply_datetime = #{applyDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_detail_modify_record t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.CollectionDetailModifyRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_detail_modify_record t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>