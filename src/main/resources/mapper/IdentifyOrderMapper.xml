<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.IdentifyOrderMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.IdentifyOrder">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="real_name" jdbcType="VARCHAR" property="realName"/>
        <result column="id_no" jdbcType="VARCHAR" property="idNo"/>
        <result column="front_image" jdbcType="VARCHAR" property="frontImage"/>
        <result column="back_image" jdbcType="VARCHAR" property="backImage"/>
        <result column="face_image" jdbcType="VARCHAR" property="faceImage"/>
        <result column="biz_code" jdbcType="VARCHAR" property="bizCode"/>
        <result column="certify_id" jdbcType="VARCHAR" property="certifyId"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="next_datetime" jdbcType="TIMESTAMP" property="nextDatetime"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.user_id
        , t.status
        , t.real_name
        , t.id_no
        , t.front_image
        , t.back_image
        , t.face_image
        , t.biz_code
        , t.certify_id
        , t.create_datetime
        , t.updater
        , t.update_datetime
        , t.remark
        , t.next_datetime
        , t.order_no
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.login_name like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                t.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                t.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="realName != null and realName != '' ">
                AND t.real_name = #{realName, jdbcType=VARCHAR}
            </if>
            <if test="idNo != null and idNo != '' ">
                AND t.id_no = #{idNo, jdbcType=VARCHAR}
            </if>
            <if test="frontImage != null and frontImage != '' ">
                AND t.front_image = #{frontImage, jdbcType=VARCHAR}
            </if>
            <if test="backImage != null and backImage != '' ">
                AND t.back_image = #{backImage, jdbcType=VARCHAR}
            </if>
            <if test="faceImage != null and faceImage != '' ">
                AND t.face_image = #{faceImage, jdbcType=VARCHAR}
            </if>
            <if test="bizCode != null and bizCode != '' ">
                AND t.biz_code = #{bizCode, jdbcType=VARCHAR}
            </if>
            <if test="certifyId != null and certifyId != '' ">
                AND t.certify_id = #{certifyId, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="createDatetimeStart != null">
                AND t.create_datetime <![CDATA[ >=]]> #{createDatetimeStart, jdbcType=TIMESTAMP}
            </if>
            <if test="updater != null ">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
            <if test="nextDatetime != null">
                AND t.next_datetime = #{nextDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="orderNo != null">
                AND t.order_no = #{orderNo, jdbcType=INTEGER}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.IdentifyOrder">
        insert into tstd_identify_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="userId != null and userId != '' ">
                user_id,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="realName != null and realName != '' ">
                real_name,
            </if>
            <if test="idNo != null and idNo != '' ">
                id_no,
            </if>
            <if test="frontImage != null and frontImage != '' ">
                front_image,
            </if>
            <if test="backImage != null and backImage != '' ">
                back_image,
            </if>
            <if test="faceImage != null and faceImage != '' ">
                face_image,
            </if>
            <if test="bizCode != null and bizCode != '' ">
                biz_code,
            </if>
            <if test="certifyId != null and certifyId != '' ">
                certify_id,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="updater != null and updater != '' ">
                updater,
            </if>
            <if test="updateDatetime != null">
                update_datetime,
            </if>
            <if test="remark != null and remark != '' ">
                remark,
            </if>
            <if test="nextDatetime != null ">
                next_datetime,
            </if>
            <if test="orderNo != null ">
                order_no,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null and userId != '' ">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="realName != null and realName != '' ">
                #{realName,jdbcType=VARCHAR},
            </if>
            <if test="idNo != null and idNo != '' ">
                #{idNo,jdbcType=VARCHAR},
            </if>
            <if test="frontImage != null and frontImage != '' ">
                #{frontImage,jdbcType=VARCHAR},
            </if>
            <if test="backImage != null and backImage != '' ">
                #{backImage,jdbcType=VARCHAR},
            </if>
            <if test="faceImage != null and faceImage != '' ">
                #{faceImage,jdbcType=VARCHAR},
            </if>
            <if test="bizCode != null and bizCode != '' ">
                #{bizCode,jdbcType=VARCHAR},
            </if>
            <if test="certifyId != null and certifyId != '' ">
                #{certifyId,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="nextDatetime != null">
                #{nextDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tstd_identify_order
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.IdentifyOrder">
        update tstd_identify_order
        <set>
            <if test="userId != null and userId != '' ">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="realName != null and realName != '' ">
                real_name = #{realName,jdbcType=VARCHAR},
            </if>
            <if test="idNo != null and idNo != '' ">
                id_no = #{idNo,jdbcType=VARCHAR},
            </if>
            <if test="frontImage != null and frontImage != '' ">
                front_image = #{frontImage,jdbcType=VARCHAR},
            </if>
            <if test="backImage != null and backImage != '' ">
                back_image = #{backImage,jdbcType=VARCHAR},
            </if>
            <if test="faceImage != null and faceImage != '' ">
                face_image = #{faceImage,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updateDatetime != null ">
                update_datetime = #{updateDatetime},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="nextDatetime != null">
                next_datetime = #{nextDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_identify_order t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.IdentifyOrder"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_identify_order t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectCount" parameterType="com.std.core.pojo.domain.IdentifyOrder" resultType="java.lang.Integer">
        select count(1)
        from tstd_identify_order t
        <include refid="where_condition"/>
    </select>
</mapper>