<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.MeetingHostingMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.MeetingHosting">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="meeting_id" jdbcType="BIGINT" property="meetingId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="presenter_id" jdbcType="BIGINT" property="presenterId"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="end_datetime" jdbcType="TIMESTAMP" property="endDatetime"/>

    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.meeting_id
        , t.user_id
        , t.user_name
        , t.presenter_id
        , t.status
        , t.create_datetime
        , t.update_datetime
        , t.end_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="meetingId != null">
                AND t.meeting_id = #{meetingId, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="userName != null and userName != '' ">
                AND t.user_name = #{userName, jdbcType=VARCHAR}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="presenterId != null">
                AND t.presenter_id = #{presenterId, jdbcType=BIGINT}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="endDatetime != null">
                AND t.end_datetime = #{endDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.MeetingHosting">
        insert into live_meeting_hosting
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="meetingId != null ">
                meeting_id,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="userName != null and userName != '' ">
                user_name,
            </if>
            <if test="presenterId != null ">
                presenter_id,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
            <if test="endDatetime != null">
                end_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="meetingId != null">
                #{meetingId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="userName != null and userName != '' ">
                #{userName, jdbcType=VARCHAR},
            </if>
            <if test="presenterId != null">
                #{presenterId,jdbcType=BIGINT},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="endDatetime != null">
                #{endDatetime, jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from live_meeting_hosting
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.MeetingHosting">
        update live_meeting_hosting
        <set>
            <if test="meetingId != null">
                meeting_id = #{meetingId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="userName != null and userName != '' ">
                user_name = #{userName, jdbcType=VARCHAR},
            </if>
            <if test="presenterId != null">
                presenter_id = #{presenterId,jdbcType=BIGINT},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="endDatetime != null">
                end_datetime = #{endDatetime, jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from live_meeting_hosting t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.MeetingHosting"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from live_meeting_hosting t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectTimeOutData" parameterType="com.std.core.pojo.domain.MeetingHosting"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from live_meeting_hosting t
        where t.status='1' and DATE_ADD(t.update_datetime,INTERVAL #{second} SECOND) <![CDATA[ <=]]> #{date}
    </select>
    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from live_meeting_hosting t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>
</mapper>