<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.InviteConfigMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.InviteConfig">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_level" jdbcType="VARCHAR" property="userLevel"/>
        <result column="level" jdbcType="INTEGER" property="level"/>
        <result column="rate" jdbcType="DECIMAL" property="rate"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="creator_name" jdbcType="VARCHAR" property="creatorName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.user_level
        , t.level
        , t.rate
        , t.creator
        , t.creator_name
        , t.create_time
        , t.updater
        , t.updater_name
        , t.update_time
        , t.remark
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userLevel != null and userLevel != '' ">
                AND t.user_level = #{userLevel, jdbcType=VARCHAR}
            </if>
            <if test="level != null">
                AND t.level = #{level, jdbcType=INTEGER}
            </if>
            <if test="rate != null">
                AND t.rate = #{rate, jdbcType=DECIMAL}
            </if>
            <if test="creator != null">
                AND t.creator = #{creator, jdbcType=BIGINT}
            </if>
            <if test="creatorName != null and creatorName != '' ">
                AND t.creator_name = #{creatorName, jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                AND t.create_time = #{createTime, jdbcType=TIMESTAMP}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                AND t.update_time = #{updateTime, jdbcType=TIMESTAMP}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.InviteConfig">
        insert into tsys_invite_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="userLevel != null and userLevel != '' ">
                user_level,
            </if>
            <if test="level != null ">
                level,
            </if>
            <if test="rate != null ">
                rate,
            </if>
            <if test="creator != null ">
                creator,
            </if>
            <if test="creatorName != null and creatorName != '' ">
                creator_name,
            </if>
            <if test="createTime != null ">
                create_time,
            </if>
            <if test="updater != null ">
                updater,
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateTime != null ">
                update_time,
            </if>
            <if test="remark != null and remark != '' ">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userLevel != null and userLevel != '' ">
                #{userLevel,jdbcType=VARCHAR},
            </if>
            <if test="level != null">
                #{level,jdbcType=INTEGER},
            </if>
            <if test="rate != null">
                #{rate,jdbcType=DECIMAL},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="creatorName != null and creatorName != '' ">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tsys_invite_config
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.InviteConfig">
        update tsys_invite_config
        <set>
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="userLevel != null and userLevel != '' ">
                user_level = #{userLevel,jdbcType=VARCHAR},
            </if>
            <if test="level != null">
                level = #{level,jdbcType=INTEGER},
            </if>
            <if test="rate != null">
                rate = #{rate,jdbcType=DECIMAL},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="creatorName != null and creatorName != '' ">
                creator_name = #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_invite_config t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.InviteConfig"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_invite_config t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>