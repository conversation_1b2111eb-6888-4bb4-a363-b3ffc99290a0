<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.OperatorLogMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.OperatorLog">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="operate_ts" jdbcType="BIGINT" property="operateTs"/>
        <result column="operate_time" jdbcType="TIMESTAMP" property="operateTime"/>
        <result column="ip" jdbcType="VARCHAR" property="ip"/>
        <result column="userAgent" jdbcType="VARCHAR" property="userAgent"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="get" jdbcType="VARCHAR" property="get"/>
        <result column="post" jdbcType="VARCHAR" property="post"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.user_id
        , t.operate_ts
        , t.operate_time
        , t.ip
        , t.userAgent
        , t.url
        , t.get
        , t.post
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="operateTs != null">
                AND t.operate_ts = #{operateTs}
            </if>
            <if test="operateTime != null">
                AND t.operate_time = #{operateTime, jdbcType=TIMESTAMP}
            </if>
            <if test="searchTsStart != null">
                AND operate_ts <![CDATA[ >= ]]> #{searchTsStart}
            </if>
            <if test="searchTsEnd != null">
                AND operate_ts <![CDATA[ <= ]]> #{searchTsEnd}
            </if>
            <if test="ip != null and ip != '' ">
                AND t.ip = #{ip, jdbcType=VARCHAR}
            </if>
            <if test="userAgent != null and userAgent != '' ">
                AND t.userAgent = #{userAgent, jdbcType=VARCHAR}
            </if>
            <if test="url != null and url != '' ">
                AND t.url = #{url, jdbcType=VARCHAR}
            </if>
            <if test="get != null and get != '' ">
                AND t.get = #{get, jdbcType=VARCHAR}
            </if>
            <if test="post != null and post != '' ">
                AND t.post = #{post, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.OperatorLog">
        insert into tstd_operator_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="operateTs != null">
                operate_ts,
            </if>
            <if test="operateTime != null ">
                operate_time,
            </if>
            <if test="ip != null and ip != '' ">
                ip,
            </if>
            <if test="userAgent != null and userAgent != '' ">
                userAgent,
            </if>
            <if test="url != null and url != '' ">
                url,
            </if>
            <if test="get != null and get != '' ">
                get,
            </if>
            <if test="post != null and post != '' ">
                post,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="operateTs != null">
                #{operateTs,jdbcType=BIGINT},
            </if>
            <if test="operateTime != null">
                #{operateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="ip != null and ip != '' ">
                #{ip,jdbcType=VARCHAR},
            </if>
            <if test="userAgent != null and userAgent != '' ">
                #{userAgent,jdbcType=VARCHAR},
            </if>
            <if test="url != null and url != '' ">
                #{url,jdbcType=VARCHAR},
            </if>
            <if test="get != null and get != '' ">
                #{get,jdbcType=VARCHAR},
            </if>
            <if test="post != null and post != '' ">
                #{post,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tstd_operator_log
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.OperatorLog">
        update tstd_operator_log
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="operateTs != null">
                operate_ts = #{operateTs},
            </if>
            <if test="operateTime != null">
                operate_time = #{operateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="ip != null and ip != '' ">
                ip = #{ip,jdbcType=VARCHAR},
            </if>
            <if test="userAgent != null and userAgent != '' ">
                userAgent = #{userAgent,jdbcType=VARCHAR},
            </if>
            <if test="url != null and url != '' ">
                url = #{url,jdbcType=VARCHAR},
            </if>
            <if test="get != null and get != '' ">
                get = #{get,jdbcType=VARCHAR},
            </if>
            <if test="post != null and post != '' ">
                post = #{post,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_operator_log t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.OperatorLog"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_operator_log t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectUserCount" parameterType="com.std.core.pojo.domain.OperatorLog"
            resultType="java.lang.Integer">
        select count(distinct user_id)
        from tstd_operator_log t
        <include refid="where_condition"/>
    </select>
</mapper>