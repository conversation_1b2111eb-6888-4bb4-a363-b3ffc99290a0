<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.SmsCompanyMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.SmsCompany">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="send_type" jdbcType="VARCHAR" property="sendType"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="is_send" jdbcType="VARCHAR" property="isSend"/>
        <result column="ref_collection_id" jdbcType="VARCHAR" property="refCollectionId"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="auditr" jdbcType="BIGINT" property="auditr"/>
        <result column="auditr_name" jdbcType="VARCHAR" property="auditrName"/>
        <result column="audit_datetime" jdbcType="TIMESTAMP" property="auditDatetime"/>
        <result column="send_datetime" jdbcType="TIMESTAMP" property="sendDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.company_id
        , t.send_type
        , t.status
        , t.title
        , t.content
        , t.is_send
        , t.ref_collection_id
        , t.create_datetime
        , t.updater
        , t.updater_name
        , t.update_datetime
        , t.auditr
        , t.auditr_name
        , t.audit_datetime
        , t.send_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="companyId != null">
                AND t.company_id = #{companyId, jdbcType=BIGINT}
            </if>
            <if test="sendType != null and sendType != '' ">
                AND t.send_type = #{sendType, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="title != null and title != '' ">
                AND t.title like concat('%', #{title, jdbcType=VARCHAR},'%')
            </if>
            <if test="content != null">
                AND t.content = #{content, jdbcType=VARCHAR}
            </if>
            <if test="isSend != null and isSend != '' ">
                AND t.is_send = #{isSend, jdbcType=VARCHAR}
            </if>
            <if test="refCollectionId != null and refCollectionId != '' ">
                AND t.ref_collection_id like concat('%', #{refCollectionId, jdbcType=VARCHAR},'%')
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="auditr != null ">
                AND t.auditr = #{auditr, jdbcType=BIGINT}
            </if>
            <if test="auditrName != null and auditrName != '' ">
                AND t.auditr_name = #{auditrName, jdbcType=VARCHAR}
            </if>
            <if test="auditDatetime != null">
                AND t.audit_datetime = #{auditDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="sendDatetime != null">
                AND t.send_datetime = #{sendDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="sendDatetimeStart != null and sendDatetimeStart != '' ">
                AND date_format(t.send_datetime,'%Y-%m-%d') <![CDATA[ >=]]> #{sendDatetimeStart, jdbcType=VARCHAR}
            </if>
            <if test="sendDatetimeEnd != null and sendDatetimeEnd != '' ">
                AND date_format(t.send_datetime,'%Y-%m-%d') <![CDATA[ <=]]> #{sendDatetimeEnd, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.SmsCompany">
        insert into tstd_sms_company
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="companyId != null ">
                company_id,
              </if>
              <if test="sendType != null and sendType != '' ">
                send_type,
              </if>
              <if test="status != null and status != '' ">
                status,
              </if>
              <if test="title != null and title != '' ">
                title,
              </if>
              <if test="content != null ">
                content,
              </if>
              <if test="isSend != null and isSend != '' ">
                is_send,
              </if>
              <if test="refCollectionId != null and refCollectionId != '' ">
                ref_collection_id,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
              <if test="updater != null ">
                updater,
              </if>
              <if test="updaterName != null and updaterName != '' ">
                updater_name,
              </if>
              <if test="updateDatetime != null ">
                update_datetime,
              </if>
              <if test="auditr != null ">
                auditr,
              </if>
              <if test="auditrName != null and auditrName != '' ">
                auditr_name,
              </if>
              <if test="auditDatetime != null ">
                audit_datetime,
              </if>
            <if test="sendDatetime != null">
                send_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="sendType != null and sendType != '' ">
                #{sendType,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="title != null and title != '' ">
                #{title,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="isSend != null and isSend != '' ">
                #{isSend,jdbcType=VARCHAR},
            </if>
            <if test="refCollectionId != null and refCollectionId != '' ">
                #{refCollectionId,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="auditr != null ">
                #{auditr,jdbcType=BIGINT},
            </if>
            <if test="auditrName != null and auditrName != '' ">
                #{auditrName,jdbcType=VARCHAR},
            </if>
            <if test="auditDatetime != null">
                #{auditDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="sendDatetime != null">
                #{sendDatetime, jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tstd_sms_company
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.SmsCompany">
        update tstd_sms_company
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="sendType != null and sendType != '' ">
                send_type = #{sendType,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="title != null and title != '' ">
                title = #{title,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="isSend != null and isSend != '' ">
                is_send = #{isSend,jdbcType=VARCHAR},
            </if>
            <if test="refCollectionId != null and refCollectionId != '' ">
                ref_collection_id = #{refCollectionId,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="auditr != null">
                auditr = #{auditr,jdbcType=BIGINT},
            </if>
            <if test="auditrName != null and auditrName != '' ">
                auditr_name = #{auditrName,jdbcType=VARCHAR},
            </if>
            <if test="auditDatetime != null">
                audit_datetime = #{auditDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="sendDatetime != null">
                send_datetime = #{sendDatetime, jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_sms_company t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.SmsCompany"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_sms_company t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_sms_company t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>

    <select id="selectListSmsCompanySend" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_sms_company t
        where t.status = '4' and t.send_datetime <![CDATA[ <=]]> #{date}
    </select>
</mapper>