<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.StatisticsCollectionMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.StatisticsCollection">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="collection_size" jdbcType="INTEGER" property="collectionSize"/>
        <result column="collection_total_price" jdbcType="DECIMAL" property="collectionTotalPrice"/>
        <result column="create_datetime" jdbcType="DATE" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.collection_size
        , t.collection_total_price
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="collectionSize != null">
                AND t.collection_size = #{collectionSize, jdbcType=INTEGER}
            </if>
            <if test="collectionTotalPrice != null">
                AND t.collection_total_price = #{collectionTotalPrice, jdbcType=DECIMAL}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=DATE}
            </if>
            <if test="createDatetimeStart != null">
                <![CDATA[AND t.create_datetime >= #{createDatetimeStart, jdbcType=TIMESTAMP}]]>
            </if>
            <if test="createDatetimeEnd != null">
                <![CDATA[AND t.create_datetime <= #{createDatetimeEnd, jdbcType=TIMESTAMP}]]>
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.StatisticsCollection">
        insert into tstd_statistics_collection
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="collectionSize != null ">
                collection_size,
              </if>
              <if test="collectionTotalPrice != null ">
                collection_total_price,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="collectionSize != null">
                #{collectionSize,jdbcType=INTEGER},
            </if>
            <if test="collectionTotalPrice != null">
                #{collectionTotalPrice,jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=DATE},
            </if>
        </trim>
    </insert>
    <insert id="insertStatistics">
        INSERT INTO `tstd_statistics_collection` ( `collection_size`, `collection_total_price`, `create_datetime` ) SELECT
        (
        SELECT
        COUNT( 1 ) size
        FROM
        nft_collection_detail t
        WHERE
        (( t.lock_datetime IS NULL OR t.lock_datetime <![CDATA[ <=]]> NOW()) AND t.lock_time != '-1' )
        AND t.`status` IN ( '0', '2', '3' )
        AND buy_channel != '6' and owner_type='0' and owner_id not in (select user_id from tstd_statistics_except_user where `type`='1')
        ) AS size,(
        SELECT
        SUM( buy_price ) amount
        FROM
        nft_collection_detail t
        WHERE
        (( t.lock_datetime IS NULL OR t.lock_datetime <![CDATA[ <=]]> NOW()) AND t.lock_time != '-1' )
        AND t.`status` IN ( '0', '2', '3' )
        AND buy_channel != '6' and owner_type='0' and owner_id not in (select user_id from tstd_statistics_except_user where `type`='1')
        ) amount,
        NOW()
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tstd_statistics_collection
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.StatisticsCollection">
        update tstd_statistics_collection
        <set>
            <if test="collectionSize != null">
                collection_size = #{collectionSize,jdbcType=INTEGER},
            </if>
            <if test="collectionTotalPrice != null">
                collection_total_price = #{collectionTotalPrice,jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=DATE},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_statistics_collection t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.StatisticsCollection"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_statistics_collection t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>