<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ChannelSystemMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.ChannelSystem">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="biz_code" jdbcType="VARCHAR" property="bizCode"/>
        <result column="channel_name" jdbcType="VARCHAR" property="channelName"/>
        <result column="channel_logo" jdbcType="VARCHAR" property="channelLogo"/>
        <result column="transfer_price" jdbcType="DECIMAL" property="transferPrice"/>
        <result column="transfer_bsn_price" jdbcType="DECIMAL" property="transferBsnPrice"/>
        <result column="transfer_size" jdbcType="INTEGER" property="transferSize"/>
        <result column="free_size" jdbcType="INTEGER" property="freeSize"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="is_transfer" jdbcType="VARCHAR" property="isTransfer"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="contract_id" jdbcType="BIGINT" property="contractId"/>
        <result column="collection_type" jdbcType="VARCHAR" property="collectionType"/>
        <result column="h5_url" jdbcType="VARCHAR" property="h5Url"/>

    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.type
        , t.biz_code
        , t.channel_name
        , t.channel_logo
        , t.transfer_price
        , t.transfer_bsn_price
        , t.transfer_size
        , t.free_size
        , t.status
        , t.is_transfer
        , t.create_datetime
        , t.updater
        , t.updater_name
        , t.update_datetime
        , t.company_id
        , t.contract_id
        , t.collection_type
        , t.h5_url
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="bizCode != null and bizCode != '' ">
                AND t.biz_code = #{bizCode, jdbcType=VARCHAR}
            </if>
            <if test="channelName != null and channelName != '' ">
                AND t.channel_name = #{channelName, jdbcType=VARCHAR}
            </if>
            <if test="channelName != null and channelName != '' ">
                AND t.channel_name = #{channelName, jdbcType=VARCHAR}
            </if>
            <if test="channelLogo != null and channelLogo != '' ">
                AND t.channel_logo = #{channelLogo, jdbcType=VARCHAR}
            </if>
            <if test="transferPrice != null ">
                AND t.transfer_price = #{transferPrice, jdbcType=DECIMAL}
            </if>
            <if test="transferBsnPrice != null ">
                AND t.transfer_bsn_price = #{transferBsnPrice, jdbcType=DECIMAL}
            </if>
            <if test="transferSize != null ">
                AND t.transfer_size = #{transferSize, jdbcType=INTEGER}
            </if>
            <if test="freeSize != null ">
                AND t.free_size = #{freeSize, jdbcType=INTEGER}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="isTransfer != null and isTransfer != '' ">
                AND t.is_transfer = #{isTransfer, jdbcType=VARCHAR}
            </if>
            <if test="transferOutFlag != null and transferOutFlag != '' ">
                AND t.transfer_out_flag = #{transferOutFlag, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != ''">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null ">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="companyId != null">
                AND t.company_id = #{companyId, jdbcType=BIGINT}
            </if>
            <if test="contractId != null">
                AND t.contract_id = #{contractId, jdbcType=BIGINT}
            </if>
            <if test="collectionType != null and collectionType !='' ">
                AND t.collection_type = #{collectionType, jdbcType=VARCHAR}
            </if>
            <if test="h5Url != null and h5Url !='' ">
                AND t.h5_url = #{h5Url, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.ChannelSystem">
        insert into cs_channel_system
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="bizCode != null and bizCode != '' ">
                biz_code ,
            </if>
            <if test="channelName != null and channelName != '' ">
                channel_name,
            </if>
            <if test="channelLogo != null and channelLogo != '' ">
                channel_logo,
            </if>
            <if test="transferPrice != null ">
                transfer_price,
            </if>
            <if test="transferBsnPrice != null ">
                transfer_bsn_price,
            </if>
            <if test="transferSize != null ">
                transfer_size,
            </if>
            <if test="freeSize != null ">
                free_size ,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="isTransfer != null and isTransfer != '' ">
                is_transfer,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="updaterName != null and updaterName != ''">
                updater_name,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
            <if test="companyId != null">
                company_id,
            </if>
            <if test="contractId != null">
                contract_id,
            </if>
            <if test="collectionType != null and collectionType !='' ">
                collection_type,
            </if>
            <if test="h5Url != null and h5Url !='' ">
                h5_url,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                #{type, jdbcType=VARCHAR},
            </if>
            <if test="bizCode != null and bizCode != '' ">
                #{bizCode, jdbcType=VARCHAR},
            </if>
            <if test="channelName != null and channelName != '' ">
                #{channelName,jdbcType=VARCHAR},
            </if>
            <if test="channelLogo != null and channelLogo != '' ">
                #{channelLogo,jdbcType=VARCHAR},
            </if>
            <if test="transferPrice != null ">
                #{transferPrice, jdbcType=DECIMAL},
            </if>
            <if test="transferBsnPrice != null ">
                #{transferBsnPrice, jdbcType=DECIMAL},
            </if>
            <if test="transferSize != null ">
                #{transferSize, jdbcType=INTEGER},
            </if>
            <if test="freeSize != null ">
                #{freeSize, jdbcType=INTEGER},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="isTransfer != null and isTransfer != '' ">
                #{isTransfer, jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater, jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != ''">
                #{updaterName, jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null ">
                #{updateDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="companyId != null">
                #{companyId, jdbcType=BIGINT},
            </if>
            <if test="contractId != null">
                #{contractId, jdbcType=BIGINT},
            </if>
            <if test="collectionType != null and collectionType !='' ">
                #{collectionType, jdbcType=VARCHAR},
            </if>
            <if test="h5Url != null and h5Url !='' ">
                #{h5Url, jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from cs_channel_system
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.ChannelSystem">
        update cs_channel_system
        <set>
            <if test="type != null and type != '' ">
                type = #{type, jdbcType=VARCHAR},
            </if>
            <if test="bizCode != null and bizCode != '' ">
                biz_code = #{bizCode, jdbcType=VARCHAR},
            </if>
            <if test="channelName != null and channelName != '' ">
                channel_name = #{channelName,jdbcType=VARCHAR},
            </if>
            <if test="channelLogo != null and channelLogo != '' ">
                channel_logo = #{channelLogo,jdbcType=VARCHAR},
            </if>
            <if test="transferPrice != null ">
                transfer_price = #{transferPrice, jdbcType=DECIMAL},
            </if>
            <if test="transferBsnPrice != null ">
                transfer_bsn_price = #{transferBsnPrice, jdbcType=DECIMAL},
            </if>
            <if test="transferSize != null ">
                transfer_size = #{transferSize, jdbcType=INTEGER},
            </if>
            <if test="freeSize != null ">
                free_size = #{freeSize, jdbcType=INTEGER},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="isTransfer != null and isTransfer != '' ">
                is_transfer = #{isTransfer, jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater, jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != ''">
                updater_name = #{updaterName, jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null ">
                update_datetime = #{updateDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="companyId != null">
                company_id = #{companyId, jdbcType=BIGINT},
            </if>
            <if test="contractId != null">
                contract_id = #{contractId, jdbcType=BIGINT},
            </if>
            <if test="collectionType != null and collectionType !='' ">
                collection_type = #{collectionType, jdbcType=VARCHAR},
            </if>
            <if test="h5Url != null and h5Url !='' ">
                h5_url = #{h5Url, jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cs_channel_system t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.ChannelSystem"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cs_channel_system t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectByCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cs_channel_system t
        where t.biz_code = #{code,jdbcType=BIGINT}
    </select>
    <select id="detailUrlByCode" resultType="com.std.core.pojo.domain.ChannelSystemPrivate">
        select t
            .
            id
             , t.type               type
             , t.biz_code           bizCode
             , t.channel_name       channelName
             , t.channel_logo       channelLogo
             , t.transfer_price     transferPrice
             , t.transfer_bsn_price transferBsnPrice
             , t.transfer_size      transferSize
             , t.free_size         freeSize
             , t.status
             , t.is_transfer        isTransfer
             , t.create_datetime    createDatetime
             , t.access_url         accessUrl
             , t.md5_key            md5Key
             , t.company_id         companyId
             , t.contract_id        contractId
             , t.collection_type    collectionType
             , t.transfer_out_flag  transferOutFlag
            , t.h5_url h5Url
        from cs_channel_system t
        where t.biz_code = #{code,jdbcType=BIGINT}
    </select>
</mapper>