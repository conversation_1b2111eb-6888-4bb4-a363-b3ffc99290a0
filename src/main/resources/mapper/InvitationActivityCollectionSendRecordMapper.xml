<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.InvitationActivityCollectionSendRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.InvitationActivityCollectionSendRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="activity_id" jdbcType="BIGINT" property="activityId"/>
        <result column="activity_send_type" jdbcType="VARCHAR" property="activitySendType"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="period_id" jdbcType="BIGINT" property="periodId"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="collection_detail_id" jdbcType="BIGINT" property="collectionDetailId"/>
        <result column="ref_user" jdbcType="BIGINT" property="refUser"/>
        <result column="get_collection_size" jdbcType="INTEGER" property="getCollectionSize"/>
        <result column="befor_collection_size" jdbcType="INTEGER" property="beforCollectionSize"/>
        <result column="after_collection_size" jdbcType="INTEGER" property="afterCollectionSize"/>
        <result column="invitation_finish_size" jdbcType="INTEGER" property="invitationFinishSize"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.activity_id
        , t.activity_send_type
        , t.user_id
        , t.period_id
        , t.collection_id
        , t.collection_detail_id
        , t.ref_user
        , t.get_collection_size
        , t.befor_collection_size
        , t.after_collection_size
        , t.invitation_finish_size
        , t.create_datetime
        , t.create_time
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="activityId != null">
                AND t.activity_id = #{activityId, jdbcType=BIGINT}
            </if>
            <if test="activitySendType != null and activitySendType != '' ">
                AND t.activity_send_type = #{activitySendType, jdbcType=VARCHAR}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="periodId != null">
                AND t.period_id = #{periodId, jdbcType=BIGINT}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="collectionDetailId != null">
                AND t.collection_detail_id = #{collectionDetailId, jdbcType=BIGINT}
            </if>
            <if test="refUser != null">
                AND t.ref_user = #{refUser, jdbcType=BIGINT}
            </if>
            <if test="getCollectionSize != null">
                AND t.get_collection_size = #{getCollectionSize, jdbcType=INTEGER}
            </if>
            <if test="beforCollectionSize != null">
                AND t.befor_collection_size = #{beforCollectionSize, jdbcType=INTEGER}
            </if>
            <if test="afterCollectionSize != null">
                AND t.after_collection_size = #{afterCollectionSize, jdbcType=INTEGER}
            </if>
            <if test="invitationFinishSize != null">
                AND t.invitation_finish_size = #{invitationFinishSize, jdbcType=INTEGER}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="createTime != null">
                AND t.create_time = #{createTime, jdbcType=BIGINT}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.InvitationActivityCollectionSendRecord" useGeneratedKeys="true" keyProperty="id">
        insert into lxa_invitation_activity_collection_send_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="activityId != null ">
                activity_id,
              </if>
            <if test="activitySendType != null and activitySendType != '' ">
                activity_send_type,
            </if>
              <if test="userId != null ">
                user_id,
              </if>
            <if test="periodId != null">
                period_id,
            </if>
              <if test="collectionId != null ">
                collection_id,
              </if>
              <if test="collectionDetailId != null ">
                collection_detail_id,
              </if>
              <if test="refUser != null ">
                ref_user,
              </if>
              <if test="getCollectionSize != null ">
                get_collection_size,
              </if>
              <if test="beforCollectionSize != null ">
                befor_collection_size,
              </if>
              <if test="afterCollectionSize != null ">
                after_collection_size,
              </if>
              <if test="invitationFinishSize != null ">
                invitation_finish_size,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
              <if test="createTime != null ">
                create_time,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="activityId != null">
                #{activityId,jdbcType=BIGINT},
            </if>
            <if test="activitySendType != null and activitySendType != '' ">
                #{activitySendType, jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="periodId != null">
                #{periodId, jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="collectionDetailId != null">
                #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="refUser != null">
                #{refUser,jdbcType=BIGINT},
            </if>
            <if test="getCollectionSize != null">
                #{getCollectionSize,jdbcType=INTEGER},
            </if>
            <if test="beforCollectionSize != null">
                #{beforCollectionSize,jdbcType=INTEGER},
            </if>
            <if test="afterCollectionSize != null">
                #{afterCollectionSize,jdbcType=INTEGER},
            </if>
            <if test="invitationFinishSize != null">
                #{invitationFinishSize,jdbcType=INTEGER},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from lxa_invitation_activity_collection_send_record
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.InvitationActivityCollectionSendRecord">
        update lxa_invitation_activity_collection_send_record
        <set>
            <if test="activityId != null">
                activity_id = #{activityId,jdbcType=BIGINT},
            </if>
            <if test="activitySendType != null and activitySendType != '' ">
                activity_send_type = #{activitySendType, jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="periodId != null">
                period_id = #{periodId, jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="collectionDetailId != null">
                collection_detail_id = #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="refUser != null">
                ref_user = #{refUser,jdbcType=BIGINT},
            </if>
            <if test="getCollectionSize != null">
                get_collection_size = #{getCollectionSize,jdbcType=INTEGER},
            </if>
            <if test="beforCollectionSize != null">
                befor_collection_size = #{beforCollectionSize,jdbcType=INTEGER},
            </if>
            <if test="afterCollectionSize != null">
                after_collection_size = #{afterCollectionSize,jdbcType=INTEGER},
            </if>
            <if test="invitationFinishSize != null">
                invitation_finish_size = #{invitationFinishSize,jdbcType=INTEGER},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from lxa_invitation_activity_collection_send_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.InvitationActivityCollectionSendRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from lxa_invitation_activity_collection_send_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>