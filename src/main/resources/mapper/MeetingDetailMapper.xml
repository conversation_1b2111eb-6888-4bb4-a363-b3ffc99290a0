<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.MeetingDetailMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.MeetingDetail">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="meeting_id" jdbcType="BIGINT" property="meetingId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="hosting_flag" jdbcType="VARCHAR" property="hostingFlag"/>
        <result column="start_datetime" jdbcType="TIMESTAMP" property="startDatetime"/>
        <result column="end_datetime" jdbcType="TIMESTAMP" property="endDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.meeting_id
        , t.user_id
        , t.status
        , t.hosting_flag
        , t.start_datetime
        , t.end_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="meetingId != null">
                AND t.meeting_id = #{meetingId, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="status != null and status != ''">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="hostingFlag != null and hostingFlag != ''">
                AND t.hosting_flag = #{hostingFlag, jdbcType=VARCHAR}
            </if>
            <if test="startDatetime != null">
                AND t.start_datetime = #{startDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="endDatetime != null">
                AND t.end_datetime = #{endDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.MeetingDetail">
        insert into live_meeting_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="meetingId != null ">
                meeting_id,
            </if>
            <if test="userId != null">
                user_id ,
            </if>
            <if test="status != null and status != ''">
                status,
            </if>
            <if test="hostingFlag != null and hostingFlag != ''">
                hosting_flag,
            </if>
            <if test="startDatetime != null ">
                start_datetime,
            </if>
            <if test="endDatetime != null ">
                end_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="meetingId != null">
                #{meetingId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId, jdbcType=BIGINT},
            </if>
            <if test="status != null and status != ''">
                #{status, jdbcType=VARCHAR},
            </if>
            <if test="hostingFlag != null and hostingFlag != ''">
                #{hostingFlag, jdbcType=VARCHAR},
            </if>
            <if test="startDatetime != null">
                #{startDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="endDatetime != null">
                #{endDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from live_meeting_detail
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.MeetingDetail">
        update live_meeting_detail
        <set>
            <if test="meetingId != null">
                meeting_id = #{meetingId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId, jdbcType=BIGINT},
            </if>
            <if test="status != null and status != ''">
                status = #{status, jdbcType=VARCHAR},
            </if>
            <if test="hostingFlag != null and hostingFlag != ''">
                hosting_flag = #{hostingFlag, jdbcType=VARCHAR},
            </if>
            <if test="startDatetime != null">
                start_datetime = #{startDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="endDatetime != null">
                end_datetime = #{endDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateMeetingEndByMetting">
        update live_meeting_detail
            set status ='0' ,end_datetime = #{date}
        where meeting_id = #{meetingId,jdbcType=BIGINT} and status ='1'
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from live_meeting_detail t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.MeetingDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from live_meeting_detail t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectLatestByCondition" parameterType="com.std.core.pojo.domain.MeetingDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from live_meeting_detail t
        where t.meeting_id = #{meetingId, jdbcType=BIGINT}
        order by t.id desc
        limit 1
    </select>
</mapper>