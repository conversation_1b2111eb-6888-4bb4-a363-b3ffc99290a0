####### server #######
server.port=6903
###### system.code #######
system.code=CL-META
####### dev datasource #######
spring.datasource.url=************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=Lwn1YZLoBUVa0cBJ
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
####### eureka #######
eureka.client.serviceUrl.defaultZone=***********************************/eureka/
eureka.client.register-with-eureka=true
eureka.client.fetchRegistry=true
####### sms #######
sms_url=http://127.0.0.1:7902/std-sms/api
cloud_wallet_url=http://127.0.0.1:2803/forward-service/api
###### logger #######
logging.level.root=INFO
logging.level.org.springframework.web=INFO
logging.level.com.std.core.mapper=INFO
####### swagger #######
swagger.host:api.metahz.com
swagger.enable=false
##### AliOSS  #######
oss.endpoint=sts.cn-hangzhou.aliyuncs.com
oss.accessKeyId=LTAI5tQnnYK4WXy9zFnn4snq
oss.accessKeySecret=******************************
oss.roleArn=acs:ram::1126068150577983:role/oss
oss.bucket=mlkj-bucket
oss.bucket.endpoint=http://oss-accelerate.aliyuncs.com
oss.bucket.ossEndpoint=oss-accelerate
oss.bucket.filePath=https://mlkj-bucket.oss-cn-beijing.aliyuncs.com
oss.bucket.bucketName=mlkj-bucket
##### AliOCR #######
ocr.accessKeyId=1
ocr.accessKeySecret=1
ocr.endpoint=ocr-api.cn-hangzhou.aliyuncs.com
###### dingxiang #######
dingxiang.appId=1
dingxiang.appSecret=1
dingxiang.apiServer=https://dxvip.dingxiang-inc.com
###### bsn #######
bsn.plat.code=MTKJ
bsn.key=1
bsn.base.url=https://ddc.wanlinsl.com/api
bsn.host.address=1
###?????### https://explorer.testnet.bianjie.ai/
avata.url=
avata.apiKey=
avata.apiSecret=
avata.systemAddress=
###### alipay #######
alipay.providerid=2088341304406111
alipay.appid=2021003107627080
alipay.privatekey=1
alipay.publickey=1
alipay.notifyurl=http://api.metahz.com/callback/public/alipay
alipay.returnurl=http://front.metahz.com/pay/paySuccess
alipay.signtype=RSA2
alipay.gateway=https://openapi.alipay.com/gateway.do
alipay.format=json
alipay.charset=UTF-8
###### wechat #######
wechat.h5.appid=1
wechat.h5.secret=1
wechat.appid=1
wechat.backurl=http://api.metahz.com/callback/public/wechat
wechat.merchant.id=1
wechat.merchant.privatekey=1
wechat.refundurl=https://api.mch.weixin.qq.com/secapi/pay/refund
####### adapay1 #######
adapay.appid=app_511dc9b5-63d1-4e5d-9668-0582d29ef447
adapay.backurl=http://api.metahz.com/callback/public/adapay
adapay.api.key=api_live_4a8d8286-e01d-4854-8e89-dcf847ffec17
adapay.mock.api.key=api_test_bc402229-801c-49d2-b995-028833c7cae3
adapay.rsa.private.key=1
adapay.merchant.key=**********
###### adapay2 #######
#adapay.appid=app_c7c83a9d-8876-4160-b48a-d2764a87faef
#adapay.backurl=http://api.metahz.com/callback/public/adapay
#adapay.api.key=api_live_e7eeefb7-6713-4b64-8bdd-e2f66fd70d70
#adapay.mock.api.key=api_test_2ab1128a-985f-4e1a-a6ac-e43be3fa5652
#adapay.rsa.private.key=MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBALJltTdPDg92/WZvnfp2g15PoMBUOAen/3ueURLK3S6NdZF1c+vzZ4D8QGylZ0Nm3/g7pYQJKZNkrJrTL45ZIw0lpdLqd0W1PL9Hi99tih6qFn/CDQ7CtNjKZOPw8XdfMyRD50qNCVk7stbaCOZBOTRV0GVIk3K9vDt17Sl0O9RBAgMBAAECgYAsk62MfHyO8LSw/AMFqsd4sX0BkgnOH/R+9zha8cYC8+YEysyEkPW0DVQr0cjT1sjwRjc9nux8oBNhPJQNhchz/UtOb1jIvmD/+lyMFSwIZg0KyPrBoKMScmUaMUogAGaCy+ivaeLnCTh3/8Sl5i8noqJyp9H44VEJT4nh01f54QJBAOpUpo7cfCghqf6atxUNwJr2k3+WCEsKtHiHL4aben7hamy32qSd1iAJ8POrfzBDF1A4Ekj13eoaWRjSr5yVZzcCQQDC5O/pb6PLu+/21MsF8ZZt1f+szibuJid8ZNL0lf+2knUk6zoXaB7+p0DQyB28d1Xli/xLtJTmfJMw6C4ZR2xHAkAHx8e/9pIowvVh/sEVcd9zRbyf/9tGopRbbCGn1oG6tMFT2sbnsFXrumjFrmwKri+X+kShJ4i2vSk4Kcaq7EsBAkAtHlr8K8A7tjKDbhfiNF+i5dve4YrZrzOM9LpxrRgZ6CX+egZkzNIAGjBDJVDdBLN6BMJPtGqUQVrCA75IU+SZAkEAyzyK+goCkPOhTBaPopv2xg2jP/jj8GL/+Q16Y1nfYgtFzHt1PCQqDMi2PMsuFFsprmqpXS0PKUNFGavWzlbxzg==
#adapay.merchant.key=**********
###### babyPay #######
babyPay.backurl=http://api.metahz.com/callback/public/adapay
babyPay.merchant.no=************
babyPay.pwd=123456
babyPay.url=https://test-api.huishouqian.com/api/acquiring
babyPay.pfxpath=/************_pri.pfx
babyPay.publicpath=/MANDAO_************_pub.cer
###### babyPayBank #######
babyPayBank.merchant.id=1255466
babyPayBank.terminal.id=64056
babyPayBank.aes.key=1
babyPayBank.baopay.url=https://public.baofoo.com/cutpayment/protocol/backTransRequest
babyPayBank.card.info.url=https://gw.baofoo.com/biztransfer/product/bankcard/v1/bin/info
babyPayBank.pri.key.pass=891230
babyPayBank.pfxpath=/bfkey_1255466@@64056.pfx
babyPayBank.cerpath=/bfkey_1255466@@64056.cer
babyPayBank.backurl=http://api.metahz.com/callback/public/baopay_bank
###### three.System #######
three.System.user.apply.authorization=/public/authorization
three.System.company.apply.authorization=/public/company_authorization
three.System.user.collection.info=/public/user_collection_info
three.System.collection.pass.on=/public/pass_on 
three.System.collection.export.on=/public/export_on
three.System.collection.export.check=/public/collection_export_check
three.System.roll.out.callback=/public/roll_out_callback
three.System.get.token=/public/get_token
three.System.synchronization.collection.series=/public/synchronization_collection_series
three.System.collection.export.on.finish=/public/export_finish
###### channleAction.System #######
channleAction.System.url=http://47.110.55.234:1818/api/core/v1/auction_system
channleAction.System.user.apply.authorization=/public/authorization
channleAction.System.user.collection.info=/public/user_collection_info
channleAction.System.collection.pass.on=/public/pass_on
channleAction.System.roll.out.callback=/public/roll_out_callback
channleAction.System.get.token=/public/get_token
###### yeepay.config #######
yeepay.bank.parentMerchantNo=1
yeepay.bank.merchantNo=1
yeepay.bank.notifyUrl=http://api.metahz.com/callback/public/yeepay_bank
yeepay.bank.fundProcessType=DELAY_SETTLE
yeepay.bank.appKey=1
yeepay.bank.url=https://cash.yeepay.com/cashier/std
yeepay.bank.certType=RSA2048
yeepay.bank.priKey=1
yeepay.bank.redirectUrl=http://front.metahz.com/yeepay/return
yeepay.bank.micro.noticeUrl=http://api.metahz.com/callback/public/yeepay_micro_notice
yeepay.bank.merchant.notify.url=http://api.metahz.com/callback/public/yeepay_user_micro_notice
yeepay.bank.micro.filePath=/mnt/www/chain_play/file
#yeepay.wallet
yeepay.wallet.openReturnUrl=https://front.metahz.com
yeepay.wallet.openNoticeUrl=http://api.metahz.com/callback/public/yeepay_wallet_open
yeepay.wallet.payReturnUrl=https://front.metahz.com
yeepay.wallet.csNotifyUrl=http://api.metahz.com/callback/public/yeepay_wallet_cs_notice
yeepay.wallet.payNoticeUrl=http://api.metahz.com/callback/public/yeepay_wallet
###### culturalchain.config #######
culturalchain.access.id=1
culturalchain.access.key=1
culturalchain.url=https://culturalchain.net
##### AgoraIO #######
agora.appId=1
agora.appCertificate=1
agora.customerKey=1
agora.customerSecret=1
###### chuanglan.config #######
chuanglan.appId=IY9hHNrK
chuanglan.appKey=bREIfjXu
chuanglan.url=https://api.253.com/open/carriers/carriers-auth-detail
###### xmly #######
xmly.app.key=1
xmly.app.secret=1
xmly.http.url=https://api.ximalaya.com
xmly.grant.type=js_client_credentials
xmly.device.id=app_jssdk_device
xmly.redirect.uri=http://channel.front.metahz.com/home
xmly.h5.domain.uri=http://channel.front.metahz.com
###### RongCloud #######
rongcloud.appKey=bmdehs6pba19s
rongcloud.appSecret=dWf7mCkOgnd
##### xmeta  #######
#????rsa2??
xmeta.publicKey=1
#???????rsa2??
xmeta.appPublicKey=1
#???????rsa2??
xmeta.appPrivateKey=1
#???????aes
xmeta.aesKey=1
#???????appId
xmeta.appId=1
#????????
xmeta.version=1.2.0
#?????api????
xmeta.url=https://open.x-metash.cn/gateway/forward/
#?????????
xmeta.authUrl=https://xmeta.x-metash.cn/excenter
##### xmeta method  #######
#?????Xmeta??????
xmeta.method.archiveGoodsTransfer=xmeta.mall.archive.goods.transfer
#Xmeta???????????
xmeta.method.userVerify=xmeta.mall.user.verify
#Xmeta???????????????????
xmeta.method.archiveGoodsCount=xmeta.mall.archive.goodsCount
#Xmeta??????????????????
xmeta.method.goodsList=xmeta.mall.goods.list
#Xmeta???????????
xmeta.method.goodsVerify=xmeta.mall.goods.verify
#Xmeta?????????????
xmeta.method.goodsLock=xmeta.mall.goods.lock
#Xmeta???????????
xmeta.method.goodsTransfer=xmeta.mall.goods.transfer
#Xmeta???????????
xmeta.method.goodsTransferConfirm=xmeta.mall.goods.transfer.confirm
#Xmeta???????????????
xmeta.method.buyerReceiveVerify=xmeta.mall.buyer.receive.verify
#Xmeta???????????????
xmeta.method.userInfo=xmeta.mall.user.info
##### REDIS  #######
# Redis\u6570\u636E\u5E93\u7D22\u5F15\uFF08\u9ED8\u8BA4\u4E3A0\uFF09
spring.redis.database=5
spring.redis.host=**************
spring.redis.port=6379
spring.redis.password=clkj2018
spring.redis.max-active=600
# \u8FDE\u63A5\u6C60\u6700\u5927\u963B\u585E\u7B49\u5F85\u65F6\u95F4\uFF08\u4F7F\u7528\u8D1F\u503C\u8868\u793A\u6CA1\u6709\u9650\u5236\uFF09
spring.redis.max-wait=60000
# \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5927\u7A7A\u95F2\u8FDE\u63A5
spring.redis.max-idle=300
# \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5C0F\u7A7A\u95F2\u8FDE\u63A5
spring.redis.min-idle=0
spring.redis.timeout=6000
spring.devtools.add-properties=false


###### third party auth #######
third.party.auth.domain=http://t.yesauc.net
third.party.auth.app.key=demo_app
third.party.auth.app.secret=gKKg546AOP5jp3Grl9nGf2v57bRYcvYC
third.party.auth.token.expire.minutes=5
third.party.auth.timestamp.tolerance.minutes=5
third.party.auth.user.auth.expire.hours=24

###### OSS STS?? #######
# ?????????RAM????????????
oss.sts.role.arn=acs:ram::your-account-id:role/your-oss-upload-role
oss.sts.region=cn-hangzhou


redisson.redis.address=redis://**************:6379
redisson.redis.password=clkj2018
### xxl-job admin address list, such as "http://address" or "http://address01,http://address02"
xxl.job.admin.addresses=http://127.0.0.1:6904/xxl-job-admin
### xxl-job, access token
xxl.job.accessToken=
### xxl-job executor appname
xxl.job.executor.appname=chain-play-job-executor
### xxl-job executor registry-address: default use address to registry , otherwise use ip:port if address is null
xxl.job.executor.address=
### xxl-job executor server-info
xxl.job.executor.ip=127.0.0.1
xxl.job.executor.port=6309
### xxl-job executor log-path
xxl.job.executor.logpath=/mnt/www/chain_play/xxl-job/jobhandler
### xxl-job executor log-retention-days
xxl.job.executor.logretentiondays=30
### \u706B\u5E01\u4EA4\u6613\u5206\u5E03\u5F0F\u9501\u8D85\u65F6\u65F6\u95F4\uFF08\u5355\u4F4D\uFF1A\u6BEB\u79D2\uFF09
huobi.entrust.timeout=30000
meta.lock.timeout=20000
#-------------------------------------------------------------------------------
# Nacos\u914D\u7F6E
#-------------------------------------------------------------------------------
#nacos:
#config:
#server-addr: ***************:8848
#type: yaml
#namespace: a3029897-dd3d-4205-aae5-28e6b0e425dc
#bootstrap:
#enable: true
#log-enable: true
#context-path: nacos
#data-id: goldMqProducer
#auto-refresh: true
#####mq###
#spring.rabbitmq.host=***************
#spring.rabbitmq.port=5672
#spring.rabbitmq.username=admin
#spring.rabbitmq.password=ASD123
jpush.customer.appKey=1
jpush.customer.masterSecret=1
jpush.apns.production=1
#-----------?????-------------
#schedule.doPeriodStartTrigger=1
#schedule.doPeriodEndTrigger=1
#schedule.doFirstMarketOrderDesignCollecction=1
#schedule.doPeriodDrawStrawsEndTrigger=1
#schedule.doDealDrawStrawsJoinRecordTrigger=1
#schedule.doPayCancelBuyoutProducts=1
#schedule.doBuyProductsTimeoutDownTrigger=1
#schedule.doSynPayRecordTrigger=1
#schedule.doAuctionStartTrigger=1
#schedule.doPayCancelAuctionProducts=1
#schedule.doSynAuctionBondRefundTrigger=1
#schedule.doChallengeFinalDeal=1
#schedule.doChallengeFinal=1
#schedule.doChangeStatusChangeTrigger=1
#schedule.doIncomeSettleTrigger=1
#schedule.doStatisticDailyTrigger=1
#schedule.doBlindSellFinal=1
#schedule.doBlindSellFinalDeal=1
#schedule.doStatUserBizData=1
#schedule.doSendUpgradeInfoTrigger=1
#schedule.doJpushSend=1
#schedule.doFirstMarketUnFreezeTrigger=1
#schedule.doUnFreezeTrigger=1
#schedule.doPeriodJoinRecordMigration=1
#schedule.doBaopaySmsCodeFailure=1
#schedule.doCollectionDetailPassOn=1
#schedule.doPeriodAuctionCancelFreezeTrigger=1
#schedule.doPeriodAuctionEndFreezeTrigger=1
#schedule.doPeriodAuctionBondReturnFreezeTrigger=1
#schedule.doPeriodAuctionDefaultFreezeTrigger=1
#schedule.doPayBackTrigger=1
#schedule.doQueryPayBackTrigger=1
#schedule.doDivideTrigger=1
#schedule.doUserDivideTrigger=1
#schedule.doCheckDivideResultTrigger=1
#schedule.doPeriodAuctionDividesTrigger=1
#schedule.doInvitationActivityTrigger=1
#schedule.doCollectionStatisticsTrigger=1
#schedule.doSynchronousYeepaySettleRecordTrigger=1
#schedule.doIntegralGoodsDistributionTrigger=1
#schedule.doIntegralGoodsEndTrigger=1
#schedule.doIntegralGoodsActivityCloseTrigger=1
#schedule.doMetaPitSellerEndTrigger=1
#schedule.doDegressionAuctionTrigger=1
#schedule.doLotteryActivityEndTrigger=1
#schedule.doPeriodSubscriptionJpushSendTrigger=1
#schedule.doChipCollectionRecordDistributionTrigger=1
#schedule.doMetaRobotCalculationRecordTrigger=1
#schedule.doCompanySmsSendTrigger=1
#schedule.doMetaTicketRecordEndTrigger=1
#schedule.doPublishNft=1
#schedule.doTransferNftTrigger=1
#schedule.doSyncNftTrade=1
#schedule.doYaoMilletConfigCreate=1
#schedule.doCheckLuckDrawOpenClose=1
#schedule.doPushMeetingReserve=1
#schedule.doStartRobotActivity=1
#schedule.doEndRobotActivity=1
#schedule.doEthToBroadcastOrder=0
#schedule.doEthQueryBroadcastOrderResult=0
#schedule.doCollectionSeriesConfigStatusChangeTrigger=1
#schedule.doCollectionOpenTimeStratTrigger=1
#schedule.doRedissonClientAddDayTrigger=1


schedule.doPeriodStartTrigger=0
schedule.doPeriodEndTrigger=0
schedule.doFirstMarketOrderDesignCollecction=0
schedule.doPeriodDrawStrawsEndTrigger=0
schedule.doDealDrawStrawsJoinRecordTrigger=0
schedule.doPayCancelBuyoutProducts=0
schedule.doBuyProductsTimeoutDownTrigger=0
schedule.doSynPayRecordTrigger=0
schedule.doAuctionStartTrigger=0
schedule.doPayCancelAuctionProducts=0
schedule.doSynAuctionBondRefundTrigger=0
schedule.doChallengeFinalDeal=0
schedule.doChallengeFinal=0
schedule.doChangeStatusChangeTrigger=0
schedule.doIncomeSettleTrigger=0
schedule.doStatisticDailyTrigger=0
schedule.doBlindSellFinal=0
schedule.doBlindSellFinalDeal=0
schedule.doStatUserBizData=0
schedule.doSendUpgradeInfoTrigger=0
schedule.doJpushSend=0
schedule.doFirstMarketUnFreezeTrigger=0
schedule.doUnFreezeTrigger=0
schedule.doPeriodJoinRecordMigration=0
schedule.doBaopaySmsCodeFailure=0
schedule.doCollectionDetailPassOn=0
schedule.doPeriodAuctionCancelFreezeTrigger=0
schedule.doPeriodAuctionEndFreezeTrigger=0
schedule.doPeriodAuctionBondReturnFreezeTrigger=0
schedule.doPeriodAuctionDefaultFreezeTrigger=0
schedule.doPayBackTrigger=0
schedule.doQueryPayBackTrigger=0
schedule.doDivideTrigger=0
schedule.doUserDivideTrigger=0
schedule.doCheckDivideResultTrigger=0
schedule.doPeriodAuctionDividesTrigger=0
schedule.doInvitationActivityTrigger=0
schedule.doCollectionStatisticsTrigger=0
schedule.doSynchronousYeepaySettleRecordTrigger=0
schedule.doIntegralGoodsDistributionTrigger=0
schedule.doIntegralGoodsEndTrigger=0
schedule.doIntegralGoodsActivityCloseTrigger=0
schedule.doMetaPitSellerEndTrigger=0
schedule.doDegressionAuctionTrigger=0
schedule.doLotteryActivityEndTrigger=0
schedule.doPeriodSubscriptionJpushSendTrigger=0
schedule.doChipCollectionRecordDistributionTrigger=0
schedule.doMetaRobotCalculationRecordTrigger=0
schedule.doCompanySmsSendTrigger=0
schedule.doMetaTicketRecordEndTrigger=0
schedule.doPublishNft=0
schedule.doTransferNftTrigger=0
schedule.doSyncNftTrade=0
schedule.doYaoMilletConfigCreate=0
schedule.doCheckLuckDrawOpenClose=0
schedule.doPushMeetingReserve=0
schedule.doStartRobotActivity=0
schedule.doEndRobotActivity=0
schedule.doEthToBroadcastOrder=0
schedule.doEthQueryBroadcastOrderResult=0
schedule.doCollectionSeriesConfigStatusChangeTrigger=0
schedule.doCollectionOpenTimeStratTrigger=0
schedule.doRedissonClientAddDayTrigger=0
