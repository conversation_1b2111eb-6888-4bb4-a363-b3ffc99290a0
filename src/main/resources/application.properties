
####### \u73AF\u5883 #######
spring.profiles.active=local
#\u8BF7\u6C42\u5904\u7406\u7684\u8D85\u65F6\u65F6\u95F4
ribbon.ReadTimeout:60000
#\u8BF7\u6C42\u8FDE\u63A5\u7684\u8D85\u65F6\u65F6\u95F4
ribbon.ConnectTimeout:60000
####### \u5FAE\u670D\u52A1\u8FDE\u63A5\u8D85\u65F6\u65F6\u95F4 #######
spring.application.name=czyx-nft-core
eureka.instance.preferIpAddress=true
hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds=30000
####### mapper #######
mybatis.type-aliases-package=com.std.core.domain
mybatis.mapper-locations=classpath:mapper/*.xml
mapper.identity=mysql
###### system #######
default.role.c=300000000000000000
default.role.cps=400000000000000000
default.role.bp=490080063174483969
default.role.company.bp=572517123390447616
default.role.registered.company.bp=584566916442759168
default.pay.minutes.time=1440
###### \u5404\u7C7B\u6shB63\u5219\u9A8C\u8BC1 #######
#\u8EAB\u4EFD\u8BC1\u7684\u6B63\u5219
identityRegex.regexp=(^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$)|(^[1-9]\\d{5}\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}$)
#####\u90AE\u4EF6\u914D\u7F6E(\u53D1\u4EF6\u4EBA\uFF09######
email:
host:smtp.qq.com
username:<EMAIL>
password:tiaozlebgxqrbbbc
senderName:\u6A59\u94FE\u79D1\u6280
#####\u90AE\u4EF6\u914D\u7F6E(\u53D1\u4EF6\u4EBA\uFF09######
spring.jackson.date-format=yyyy-MM-dd
spring.jackson.time-zone=GMT+8
spring.jackson.serialization.write-dates-as-timestamps=false

redisson.redis.isOpen=0
