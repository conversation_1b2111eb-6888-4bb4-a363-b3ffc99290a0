<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PFP图片批量上传 - ZIP文件上传示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .file-input {
            border: 2px dashed #ddd;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        .file-input:hover {
            border-color: #007bff;
        }
        .file-input.dragover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
            display: none;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            width: 0%;
            transition: width 0.3s;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .file-list {
            margin-top: 10px;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
        }
        .file-item {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .file-item:last-child {
            border-bottom: none;
        }
        .file-item.success {
            color: #28a745;
        }
        .file-item.error {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PFP图片批量上传 - ZIP文件上传</h1>
        
        <form id="uploadForm">
            <div class="form-group">
                <label for="periodId">期数ID *</label>
                <input type="number" id="periodId" name="periodId" required placeholder="请输入期数ID">
            </div>

            <div class="form-group">
                <label for="zipFile">ZIP文件 *</label>
                <div class="file-input" id="fileDropZone">
                    <input type="file" id="zipFile" name="zipFile" accept=".zip" required style="display: none;">
                    <p>点击选择ZIP文件或拖拽文件到此处</p>
                    <p style="font-size: 12px; color: #666;">支持标准ZIP格式，最大100MB</p>
                </div>
                <div id="selectedFile" style="margin-top: 10px; display: none;">
                    <strong>已选择文件：</strong> <span id="fileName"></span>
                    <span id="fileSize" style="color: #666;"></span>
                </div>
            </div>

            <div class="form-group">
                <label for="namingRule">命名规则</label>
                <select id="namingRule" name="namingRule">
                    <option value="AUTO">自动编号</option>
                    <option value="FILENAME">使用文件名</option>
                </select>
            </div>

            <div class="form-group">
                <label for="startNumber">起始编号</label>
                <input type="number" id="startNumber" name="startNumber" value="1" min="1">
            </div>

            <div class="form-group">
                <label for="imagePrefix">图片前缀</label>
                <input type="text" id="imagePrefix" name="imagePrefix" value="pfp" placeholder="OSS存储路径前缀">
            </div>

            <div class="form-group">
                <label for="keepDirectoryStructure">保持目录结构</label>
                <select id="keepDirectoryStructure" name="keepDirectoryStructure">
                    <option value="false">否</option>
                    <option value="true">是</option>
                </select>
            </div>

            <div class="form-group">
                <label for="supportedFormats">支持的图片格式</label>
                <input type="text" id="supportedFormats" name="supportedFormats" 
                       value="jpg,jpeg,png,gif,bmp,webp" 
                       placeholder="用逗号分隔，如：jpg,png,gif">
            </div>

            <div class="form-group">
                <label for="token">用户Token *</label>
                <input type="text" id="token" name="token" required placeholder="请输入用户认证Token">
            </div>

            <button type="submit" id="uploadBtn">开始上传</button>
            <button type="button" id="clearBtn">清空表单</button>
        </form>

        <div class="progress" id="progressBar">
            <div class="progress-bar" id="progressBarInner"></div>
        </div>

        <div class="result" id="result">
            <h3 id="resultTitle"></h3>
            <p id="resultMessage"></p>
            <div class="file-list" id="fileList"></div>
        </div>
    </div>

    <script>
        // DOM元素
        const form = document.getElementById('uploadForm');
        const fileDropZone = document.getElementById('fileDropZone');
        const zipFileInput = document.getElementById('zipFile');
        const selectedFileDiv = document.getElementById('selectedFile');
        const fileNameSpan = document.getElementById('fileName');
        const fileSizeSpan = document.getElementById('fileSize');
        const uploadBtn = document.getElementById('uploadBtn');
        const clearBtn = document.getElementById('clearBtn');
        const progressBar = document.getElementById('progressBar');
        const progressBarInner = document.getElementById('progressBarInner');
        const result = document.getElementById('result');
        const resultTitle = document.getElementById('resultTitle');
        const resultMessage = document.getElementById('resultMessage');
        const fileList = document.getElementById('fileList');

        // API配置
        const API_BASE = 'http://localhost:8080/core';
        const UPLOAD_URL = `${API_BASE}/v1/pfp/image/batch_upload_zip`;

        // 文件拖拽处理
        fileDropZone.addEventListener('click', () => zipFileInput.click());
        fileDropZone.addEventListener('dragover', handleDragOver);
        fileDropZone.addEventListener('dragleave', handleDragLeave);
        fileDropZone.addEventListener('drop', handleDrop);
        zipFileInput.addEventListener('change', handleFileSelect);

        function handleDragOver(e) {
            e.preventDefault();
            fileDropZone.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            fileDropZone.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            fileDropZone.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                zipFileInput.files = files;
                handleFileSelect();
            }
        }

        function handleFileSelect() {
            const file = zipFileInput.files[0];
            if (file) {
                fileNameSpan.textContent = file.name;
                fileSizeSpan.textContent = `(${formatFileSize(file.size)})`;
                selectedFileDiv.style.display = 'block';
                
                // 验证文件类型
                if (!file.name.toLowerCase().endsWith('.zip')) {
                    showResult('error', '文件格式错误', '请选择ZIP格式的文件');
                    return;
                }
                
                // 验证文件大小（100MB限制）
                if (file.size > 100 * 1024 * 1024) {
                    showResult('error', '文件过大', '文件大小不能超过100MB');
                    return;
                }
                
                hideResult();
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 表单提交处理
        form.addEventListener('submit', handleSubmit);
        clearBtn.addEventListener('click', clearForm);

        async function handleSubmit(e) {
            e.preventDefault();
            
            const formData = new FormData();
            const file = zipFileInput.files[0];
            
            if (!file) {
                showResult('error', '请选择文件', '请先选择要上传的ZIP文件');
                return;
            }
            
            // 构建表单数据
            formData.append('periodId', document.getElementById('periodId').value);
            formData.append('zipFile', file);
            formData.append('namingRule', document.getElementById('namingRule').value);
            formData.append('startNumber', document.getElementById('startNumber').value);
            formData.append('imagePrefix', document.getElementById('imagePrefix').value);
            formData.append('keepDirectoryStructure', document.getElementById('keepDirectoryStructure').value);
            formData.append('supportedFormats', document.getElementById('supportedFormats').value);
            
            const token = document.getElementById('token').value;
            if (!token) {
                showResult('error', '请输入Token', '用户认证Token不能为空');
                return;
            }
            
            // 开始上传
            uploadBtn.disabled = true;
            uploadBtn.textContent = '上传中...';
            showProgress();
            hideResult();
            
            try {
                const response = await fetch(UPLOAD_URL, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.code === '000000') {
                    const uploadData = data.data;
                    showResult('success', '上传完成', 
                        `成功上传 ${uploadData.successCount} 个文件，失败 ${uploadData.failCount} 个文件`);
                    
                    // 显示文件列表
                    displayFileList(uploadData.successList, uploadData.failList);
                } else {
                    showResult('error', '上传失败', data.message || '未知错误');
                }
                
            } catch (error) {
                console.error('上传错误:', error);
                showResult('error', '上传失败', '网络错误或服务器异常');
            } finally {
                uploadBtn.disabled = false;
                uploadBtn.textContent = '开始上传';
                hideProgress();
            }
        }

        function showProgress() {
            progressBar.style.display = 'block';
            // 模拟进度（实际项目中可以使用XMLHttpRequest来获取真实进度）
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 10;
                if (progress >= 90) {
                    clearInterval(interval);
                    progress = 90;
                }
                progressBarInner.style.width = progress + '%';
            }, 200);
        }

        function hideProgress() {
            progressBar.style.display = 'none';
            progressBarInner.style.width = '0%';
        }

        function showResult(type, title, message) {
            result.className = `result ${type}`;
            result.style.display = 'block';
            resultTitle.textContent = title;
            resultMessage.textContent = message;
        }

        function hideResult() {
            result.style.display = 'none';
            fileList.innerHTML = '';
        }

        function displayFileList(successList, failList) {
            let html = '';
            
            if (successList && successList.length > 0) {
                html += '<h4 style="color: #28a745;">成功上传的文件：</h4>';
                successList.forEach(item => {
                    html += `<div class="file-item success">
                        ✓ ${item.originalName} (排序号: ${item.orderNumber})
                    </div>`;
                });
            }
            
            if (failList && failList.length > 0) {
                html += '<h4 style="color: #dc3545;">上传失败的文件：</h4>';
                failList.forEach(item => {
                    html += `<div class="file-item error">
                        ✗ ${item.originalName} - ${item.errorMessage}
                    </div>`;
                });
            }
            
            fileList.innerHTML = html;
        }

        function clearForm() {
            form.reset();
            selectedFileDiv.style.display = 'none';
            hideResult();
            hideProgress();
        }

        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 可以在这里设置一些默认值
            console.log('PFP ZIP上传页面已加载');
        });
    </script>
</body>
</html>
