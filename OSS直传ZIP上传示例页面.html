<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PFP图片批量上传 - OSS直传ZIP文件</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .step {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background-color: #fafafa;
        }
        .step h3 {
            margin-top: 0;
            color: #333;
        }
        .step.active {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .step.completed {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .file-input {
            border: 2px dashed #ddd;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        .file-input:hover {
            border-color: #007bff;
        }
        .file-input.dragover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
            display: none;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            width: 0%;
            transition: width 0.3s;
            text-align: center;
            line-height: 20px;
            color: white;
            font-size: 12px;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info-box {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .file-list {
            margin-top: 10px;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
        }
        .file-item {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .file-item:last-child {
            border-bottom: none;
        }
        .file-item.success {
            color: #28a745;
        }
        .file-item.error {
            color: #dc3545;
        }
    </style>
    <!-- 引入阿里云OSS SDK -->
    <script src="https://gosspublic.alicdn.com/aliyun-oss-sdk-6.17.1.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>PFP图片批量上传 - OSS直传ZIP文件</h1>
        
        <div class="info-box">
            <strong>流程说明：</strong>
            <ol>
                <li>获取OSS临时上传凭证</li>
                <li>直接上传ZIP文件到OSS（避免大文件通过后端传输）</li>
                <li>通知后端处理OSS上的ZIP文件</li>
            </ol>
        </div>

        <!-- 步骤1：配置参数 -->
        <div class="step active" id="step1">
            <h3>步骤1：配置上传参数</h3>
            <form id="configForm">
                <div class="form-group">
                    <label for="periodId">期数ID *</label>
                    <input type="number" id="periodId" name="periodId" required placeholder="请输入期数ID">
                </div>

                <div class="form-group">
                    <label for="namingRule">命名规则</label>
                    <select id="namingRule" name="namingRule">
                        <option value="AUTO">自动编号</option>
                        <option value="FILENAME">使用文件名</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="startNumber">起始编号</label>
                    <input type="number" id="startNumber" name="startNumber" value="1" min="1">
                </div>

                <div class="form-group">
                    <label for="imagePrefix">图片前缀</label>
                    <input type="text" id="imagePrefix" name="imagePrefix" value="pfp" placeholder="OSS存储路径前缀">
                </div>

                <div class="form-group">
                    <label for="keepDirectoryStructure">保持目录结构</label>
                    <select id="keepDirectoryStructure" name="keepDirectoryStructure">
                        <option value="false">否</option>
                        <option value="true">是</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="supportedFormats">支持的图片格式</label>
                    <input type="text" id="supportedFormats" name="supportedFormats" 
                           value="jpg,jpeg,png,gif,bmp,webp" 
                           placeholder="用逗号分隔，如：jpg,png,gif">
                </div>

                <div class="form-group">
                    <label for="deleteAfterProcess">处理完成后删除ZIP文件</label>
                    <select id="deleteAfterProcess" name="deleteAfterProcess">
                        <option value="true">是</option>
                        <option value="false">否</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="token">用户Token *</label>
                    <input type="text" id="token" name="token" required placeholder="请输入用户认证Token">
                </div>

                <button type="button" id="getTokenBtn">获取OSS上传凭证</button>
            </form>
        </div>

        <!-- 步骤2：上传ZIP文件到OSS -->
        <div class="step" id="step2">
            <h3>步骤2：上传ZIP文件到OSS</h3>
            <div class="form-group">
                <label for="zipFile">ZIP文件 *</label>
                <div class="file-input" id="fileDropZone">
                    <input type="file" id="zipFile" name="zipFile" accept=".zip" style="display: none;">
                    <p>点击选择ZIP文件或拖拽文件到此处</p>
                    <p style="font-size: 12px; color: #666;">支持标准ZIP格式，建议不超过500MB</p>
                </div>
                <div id="selectedFile" style="margin-top: 10px; display: none;">
                    <strong>已选择文件：</strong> <span id="fileName"></span>
                    <span id="fileSize" style="color: #666;"></span>
                </div>
            </div>

            <div class="progress" id="uploadProgress">
                <div class="progress-bar" id="uploadProgressBar">0%</div>
            </div>

            <button type="button" id="uploadToOssBtn" disabled>上传到OSS</button>
            <div id="ossUploadResult" style="margin-top: 10px; display: none;">
                <strong>OSS文件URL：</strong> <span id="ossFileUrl"></span>
            </div>
        </div>

        <!-- 步骤3：通知后端处理 -->
        <div class="step" id="step3">
            <h3>步骤3：通知后端处理ZIP文件</h3>
            <button type="button" id="processZipBtn" disabled>开始处理ZIP文件</button>
            
            <div class="progress" id="processProgress">
                <div class="progress-bar" id="processProgressBar">0%</div>
            </div>
        </div>

        <div class="result" id="result">
            <h3 id="resultTitle"></h3>
            <p id="resultMessage"></p>
            <div class="file-list" id="fileList"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let ossClient = null;
        let ossConfig = null;
        let uploadedFileUrl = null;

        // API配置
        const API_BASE = 'http://localhost:8080/core';
        const GET_TOKEN_URL = `${API_BASE}/v1/pfp/image/get_oss_token`;
        const PROCESS_ZIP_URL = `${API_BASE}/v1/pfp/image/batch_upload_oss_zip`;

        // DOM元素
        const step1 = document.getElementById('step1');
        const step2 = document.getElementById('step2');
        const step3 = document.getElementById('step3');
        const getTokenBtn = document.getElementById('getTokenBtn');
        const fileDropZone = document.getElementById('fileDropZone');
        const zipFileInput = document.getElementById('zipFile');
        const selectedFileDiv = document.getElementById('selectedFile');
        const fileNameSpan = document.getElementById('fileName');
        const fileSizeSpan = document.getElementById('fileSize');
        const uploadToOssBtn = document.getElementById('uploadToOssBtn');
        const uploadProgress = document.getElementById('uploadProgress');
        const uploadProgressBar = document.getElementById('uploadProgressBar');
        const ossUploadResult = document.getElementById('ossUploadResult');
        const ossFileUrl = document.getElementById('ossFileUrl');
        const processZipBtn = document.getElementById('processZipBtn');
        const processProgress = document.getElementById('processProgress');
        const processProgressBar = document.getElementById('processProgressBar');
        const result = document.getElementById('result');
        const resultTitle = document.getElementById('resultTitle');
        const resultMessage = document.getElementById('resultMessage');
        const fileList = document.getElementById('fileList');

        // 事件监听
        getTokenBtn.addEventListener('click', getOssToken);
        fileDropZone.addEventListener('click', () => zipFileInput.click());
        fileDropZone.addEventListener('dragover', handleDragOver);
        fileDropZone.addEventListener('dragleave', handleDragLeave);
        fileDropZone.addEventListener('drop', handleDrop);
        zipFileInput.addEventListener('change', handleFileSelect);
        uploadToOssBtn.addEventListener('click', uploadToOss);
        processZipBtn.addEventListener('click', processZipFile);

        // 步骤1：获取OSS上传凭证
        async function getOssToken() {
            const token = document.getElementById('token').value;
            if (!token) {
                showResult('error', '请输入Token', '用户认证Token不能为空');
                return;
            }

            getTokenBtn.disabled = true;
            getTokenBtn.textContent = '获取中...';

            try {
                const response = await fetch(`${GET_TOKEN_URL}?prefix=temp/zip&expireMinutes=60`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();

                if (data.code === '000000') {
                    ossConfig = data.data;
                    
                    // 初始化OSS客户端
                    ossClient = new OSS({
                        region: ossConfig.endpoint.replace('https://', '').replace('.aliyuncs.com', ''),
                        accessKeyId: ossConfig.accessKeyId,
                        accessKeySecret: ossConfig.accessKeySecret,
                        stsToken: ossConfig.securityToken,
                        bucket: ossConfig.bucket
                    });

                    // 激活步骤2
                    step1.classList.remove('active');
                    step1.classList.add('completed');
                    step2.classList.add('active');
                    
                    showResult('success', '获取OSS凭证成功', '可以开始上传ZIP文件到OSS');
                } else {
                    showResult('error', '获取OSS凭证失败', data.message || '未知错误');
                }
            } catch (error) {
                console.error('获取OSS凭证错误:', error);
                showResult('error', '获取OSS凭证失败', '网络错误或服务器异常');
            } finally {
                getTokenBtn.disabled = false;
                getTokenBtn.textContent = '获取OSS上传凭证';
            }
        }

        // 文件选择处理
        function handleDragOver(e) {
            e.preventDefault();
            fileDropZone.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            fileDropZone.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            fileDropZone.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                zipFileInput.files = files;
                handleFileSelect();
            }
        }

        function handleFileSelect() {
            const file = zipFileInput.files[0];
            if (file) {
                fileNameSpan.textContent = file.name;
                fileSizeSpan.textContent = `(${formatFileSize(file.size)})`;
                selectedFileDiv.style.display = 'block';
                
                // 验证文件类型
                if (!file.name.toLowerCase().endsWith('.zip')) {
                    showResult('error', '文件格式错误', '请选择ZIP格式的文件');
                    uploadToOssBtn.disabled = true;
                    return;
                }
                
                // 验证文件大小（500MB限制）
                if (file.size > 500 * 1024 * 1024) {
                    showResult('error', '文件过大', '文件大小不能超过500MB');
                    uploadToOssBtn.disabled = true;
                    return;
                }
                
                uploadToOssBtn.disabled = false;
                hideResult();
            }
        }

        // 步骤2：上传到OSS
        async function uploadToOss() {
            const file = zipFileInput.files[0];
            if (!file || !ossClient) {
                showResult('error', '上传失败', '请先选择文件并获取OSS凭证');
                return;
            }

            uploadToOssBtn.disabled = true;
            uploadToOssBtn.textContent = '上传中...';
            uploadProgress.style.display = 'block';

            try {
                // 生成唯一文件名
                const timestamp = Date.now();
                const objectName = `${ossConfig.prefix}/${timestamp}_${file.name}`;

                // 上传文件到OSS
                const result = await ossClient.put(objectName, file, {
                    progress: function (p) {
                        const percent = Math.round(p * 100);
                        uploadProgressBar.style.width = percent + '%';
                        uploadProgressBar.textContent = percent + '%';
                    }
                });

                uploadedFileUrl = `${ossConfig.baseUrl}/${objectName}`;
                ossFileUrl.textContent = uploadedFileUrl;
                ossUploadResult.style.display = 'block';

                // 激活步骤3
                step2.classList.remove('active');
                step2.classList.add('completed');
                step3.classList.add('active');
                processZipBtn.disabled = false;

                showResult('success', '上传到OSS成功', '文件已成功上传到OSS，可以开始处理');

            } catch (error) {
                console.error('OSS上传错误:', error);
                showResult('error', '上传到OSS失败', error.message || '上传过程中发生错误');
            } finally {
                uploadToOssBtn.disabled = false;
                uploadToOssBtn.textContent = '上传到OSS';
                uploadProgress.style.display = 'none';
            }
        }

        // 步骤3：通知后端处理ZIP文件
        async function processZipFile() {
            if (!uploadedFileUrl) {
                showResult('error', '处理失败', '请先上传ZIP文件到OSS');
                return;
            }

            const token = document.getElementById('token').value;
            const requestData = {
                periodId: parseInt(document.getElementById('periodId').value),
                zipFileUrl: uploadedFileUrl,
                namingRule: document.getElementById('namingRule').value,
                startNumber: parseInt(document.getElementById('startNumber').value),
                imagePrefix: document.getElementById('imagePrefix').value,
                keepDirectoryStructure: document.getElementById('keepDirectoryStructure').value === 'true',
                supportedFormats: document.getElementById('supportedFormats').value,
                deleteAfterProcess: document.getElementById('deleteAfterProcess').value === 'true'
            };

            processZipBtn.disabled = true;
            processZipBtn.textContent = '处理中...';
            processProgress.style.display = 'block';

            // 模拟处理进度
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += Math.random() * 10;
                if (progress >= 90) {
                    clearInterval(progressInterval);
                    progress = 90;
                }
                processProgressBar.style.width = progress + '%';
                processProgressBar.textContent = Math.round(progress) + '%';
            }, 500);

            try {
                const response = await fetch(PROCESS_ZIP_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(requestData)
                });

                const data = await response.json();

                clearInterval(progressInterval);
                processProgressBar.style.width = '100%';
                processProgressBar.textContent = '100%';

                if (data.code === '000000') {
                    const uploadData = data.data;
                    step3.classList.remove('active');
                    step3.classList.add('completed');
                    
                    showResult('success', '处理完成', 
                        `成功处理 ${uploadData.successCount} 个文件，失败 ${uploadData.failCount} 个文件`);
                    
                    // 显示文件列表
                    displayFileList(uploadData.successList, uploadData.failList);
                } else {
                    showResult('error', '处理失败', data.message || '未知错误');
                }

            } catch (error) {
                console.error('处理ZIP文件错误:', error);
                showResult('error', '处理失败', '网络错误或服务器异常');
                clearInterval(progressInterval);
            } finally {
                processZipBtn.disabled = false;
                processZipBtn.textContent = '开始处理ZIP文件';
                processProgress.style.display = 'none';
            }
        }

        // 工具函数
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function showResult(type, title, message) {
            result.className = `result ${type}`;
            result.style.display = 'block';
            resultTitle.textContent = title;
            resultMessage.textContent = message;
        }

        function hideResult() {
            result.style.display = 'none';
            fileList.innerHTML = '';
        }

        function displayFileList(successList, failList) {
            let html = '';
            
            if (successList && successList.length > 0) {
                html += '<h4 style="color: #28a745;">成功处理的文件：</h4>';
                successList.forEach(item => {
                    html += `<div class="file-item success">
                        ✓ ${item.originalName} (排序号: ${item.orderNumber})
                    </div>`;
                });
            }
            
            if (failList && failList.length > 0) {
                html += '<h4 style="color: #dc3545;">处理失败的文件：</h4>';
                failList.forEach(item => {
                    html += `<div class="file-item error">
                        ✗ ${item.originalName} - ${item.errorMessage}
                    </div>`;
                });
            }
            
            fileList.innerHTML = html;
        }

        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('OSS直传ZIP上传页面已加载');
        });
    </script>
</body>
</html>
